<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>房源监控系统</title>
    <link rel="shortcut icon" href="{{ url_for('static', filename='img/favicon.ico') }}" type="image/x-icon">
    <link rel="icon" href="{{ url_for('static', filename='img/favicon.ico') }}" type="image/x-icon">
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap@4.6.2/dist/css/bootstrap.min.css">
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/font-awesome@4.7.0/css/font-awesome.min.css">
    <style>
        .card {
            margin-bottom: 20px;
        }
        .badge-enabled {
            background-color: #28a745;
            color: white;
        }
        .badge-disabled {
            background-color: #dc3545;
            color: white;
        }
        .navbar-brand {
            font-weight: bold;
        }
        .device-item {
            padding: 10px;
            margin-bottom: 10px;
            border: 1px solid #ddd;
            border-radius: 4px;
            transition: all 0.3s ease;
        }
        .device-item:hover {
            background-color: #f8f9fa;
        }
        .device-actions {
            display: flex;
            flex-wrap: wrap;
            gap: 5px;
        }

        /* 设备过期状态样式 */
        .expired {
            border-left: 5px solid #dc3545;
            background-color: rgba(220, 53, 69, 0.1);
            animation: pulse-red 2s infinite;
        }
        .expiring-soon {
            border-left: 5px solid #ffc107;
            background-color: rgba(255, 193, 7, 0.1);
        }

        /* 过期设备的脉冲动画 */
        @keyframes pulse-red {
            0% {
                box-shadow: 0 0 0 0 rgba(220, 53, 69, 0.4);
            }
            70% {
                box-shadow: 0 0 0 10px rgba(220, 53, 69, 0);
            }
            100% {
                box-shadow: 0 0 0 0 rgba(220, 53, 69, 0);
            }
        }

        /* 设备列表中的过期状态统计提示 */
        .expire-status-summary {
            margin-bottom: 15px;
            padding: 10px 15px;
            border-radius: 4px;
            border-left: 5px solid #ffc107;
            background-color: #fff3cd;
        }
        .expire-status-summary .expired-count {
            color: #dc3545;
            font-weight: bold;
        }
        .expire-status-summary .expiring-count {
            color: #fd7e14;
            font-weight: bold;
        }

        /* 设备列表中的过期标签样式 */
        .badge-expired {
            background-color: #dc3545;
            color: white;
            font-weight: bold;
            animation: blink 1s infinite;
        }
        .badge-expiring {
            background-color: #ffc107;
            color: #212529;
            font-weight: bold;
        }

        @keyframes blink {
            0% { opacity: 1; }
            50% { opacity: 0.5; }
            100% { opacity: 1; }
        }

        /* 设备卡片样式 */
        .device-card {
            transition: all 0.3s ease;
            border: 1px solid #dee2e6;
        }

        .device-card:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 8px rgba(0,0,0,0.1);
        }

        .device-card .card-header {
            background-color: #f8f9fa;
            border-bottom: 1px solid #dee2e6;
            padding: 0.75rem 1rem;
        }

        .device-card .card-body {
            padding: 1rem;
        }

        .device-actions .btn {
            margin-right: 4px;
            margin-bottom: 4px;
        }

        .device-actions .btn:last-child {
            margin-right: 0;
        }

        /* 设备类型颜色边框 */
        .device-card.border-success {
            border-left: 4px solid #28a745;
        }

        .device-card.border-warning {
            border-left: 4px solid #ffc107;
        }

        .device-card.border-danger {
            border-left: 4px solid #dc3545;
        }

        /* 分页样式优化 */
        .pagination-sm .page-link {
            padding: 0.25rem 0.5rem;
            font-size: 0.875rem;
        }

        /* 响应式设计 */
        @media (max-width: 768px) {
            .device-card .device-actions {
                display: flex;
                flex-wrap: wrap;
                gap: 4px;
            }

            .device-card .device-actions .btn {
                flex: 1;
                min-width: 60px;
                margin-right: 0;
            }
        }

        #loading {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background-color: rgba(0, 0, 0, 0.5);
            display: flex;
            justify-content: center;
            align-items: center;
            z-index: 9999;
        }
        .spinner-border {
            margin-right: 10px;
        }
        .status-indicator {
            width: 12px;
            height: 12px;
            border-radius: 50%;
            display: inline-block;
            margin-right: 8px;
        }
        .status-indicator.disabled {
            background-color: #dc3545;
        }
        .status-indicator.running {
            background-color: #28a745;
            animation: pulse 2s infinite;
        }
        .status-indicator.stopped {
            background-color: #ffc107;
        }
        /* 确保模态框正确显示 */
        .modal-dialog {
            max-height: 90vh;
            overflow-y: auto;
        }

        .modal-content {
            max-height: none;
        }

        .modal-body {
            max-height: 70vh;
            overflow-y: auto;
        }

        /* 修复抢房设备模态框的布局 */
        #grabDeviceModal .modal-dialog {
            max-width: 800px;
        }

        #grabDeviceModal .modal-body {
            padding: 1.5rem;
        }

        #grabDeviceModal .modal-footer {
            padding: 1rem 1.5rem;
            border-top: 1px solid #dee2e6;
        }

        @keyframes pulse {
            0% {
                box-shadow: 0 0 0 0 rgba(40, 167, 69, 0.4);
            }
            70% {
                box-shadow: 0 0 0 10px rgba(40, 167, 69, 0);
            }
            100% {
                box-shadow: 0 0 0 0 rgba(40, 167, 69, 0);
            }
        }
        .schedule-details {
            margin-top: 10px;
            padding-top: 10px;
            border-top: 1px solid rgba(0,0,0,0.1);
        }
        .schedule-details p {
            font-size: 0.9rem;
        }
    </style>
</head>
<body>
    <div id="loading" style="display: none;">
        <div class="spinner-border" role="status"></div>
        <span>处理中...</span>
    </div>

    <nav class="navbar navbar-expand-lg navbar-dark bg-dark">
        <div class="container">
            <a class="navbar-brand" href="#">房源监控系统</a>
            <button class="navbar-toggler" type="button" data-toggle="collapse" data-target="#navbarNav"
                    aria-controls="navbarNav" aria-expanded="false" aria-label="Toggle navigation">
                <span class="navbar-toggler-icon"></span>
            </button>
            <div class="collapse navbar-collapse" id="navbarNav">
                <ul class="navbar-nav">
                    <li class="nav-item active">
                        <a class="nav-link" href="#">主页</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="{{ url_for('views.logs_page') }}">日志</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="{{ url_for('auth.logout') }}">退出</a>
                    </li>
                </ul>
            </div>
        </div>
    </nav>

    <div class="container mt-4">
        <div class="row mb-4">
            <div class="col-md-6">
                <div class="card">
                    <div class="card-header bg-primary text-white">
                        <h5 class="mb-0">状态控制</h5>
                    </div>
                    <div class="card-body">
                        <div id="status-info">
                            <p><strong>状态:</strong> <span id="status-text">加载中...</span></p>
                            <p><strong>当前代理:</strong> <span id="proxy-text">加载中...</span></p>
                            <p><strong>剩余代理:</strong> <span id="remaining-proxies">加载中...</span></p>
                            <p><strong>上次更新:</strong> <span id="last-fetch-time">加载中...</span></p>
                        </div>
                        <div class="mt-3">
                            <button id="start-btn" class="btn btn-success mr-2" disabled>
                                <i class="fa fa-play"></i> 启动监控
                            </button>
                            <button id="stop-btn" class="btn btn-danger" disabled>
                                <i class="fa fa-stop"></i> 停止监控
                            </button>
                        </div>
                        </div>
                            </div>
                        </div>
            <div class="col-md-6">
                <div class="card">
                    <div class="card-header bg-success text-white">
                        <h5 class="mb-0">房源数量</h5>
                        </div>
                    <div class="card-body">
                        <div id="house-counts">
                            <p class="text-muted">尚未获取房源数据...</p>
                        </div>
                    </div>
                        </div>
                    </div>
                </div>

        <ul class="nav nav-tabs" id="myTab" role="tablist">
            <li class="nav-item">
                <a class="nav-link active" id="houses-tab" data-toggle="tab" href="#houses" role="tab">
                    房源管理
                </a>
            </li>
            <li class="nav-item">
                <a class="nav-link" id="devices-tab" data-toggle="tab" href="#devices" role="tab">
                    设备管理
                </a>
            </li>
            <li class="nav-item">
                <a class="nav-link" id="settings-tab" data-toggle="tab" href="#settings" role="tab">
                    系统设置
                </a>
            </li>
            <li class="nav-item">
                <a class="nav-link" id="schedule-settings-tab" data-toggle="tab" href="#schedule-settings" role="tab">
                    定时任务
                </a>
            </li>
            <li class="nav-item">
                <a class="nav-link" id="custom-push-tab" data-toggle="tab" href="#custom-push" role="tab">
                    自定义推送
                </a>
            </li>
            <li class="nav-item">
                <a class="nav-link" id="grab-devices-tab" data-toggle="tab" href="#grab-devices" role="tab">
                    抢房设备
                </a>
            </li>
        </ul>

        <div class="tab-content" id="myTabContent">
            <!-- 房源管理 -->
            <div class="tab-pane fade show active" id="houses" role="tabpanel">
                <div class="card">
                    <div class="card-header d-flex justify-content-between align-items-center">
                        <h5 class="mb-0">房源列表</h5>
                        <button id="add-house-btn" class="btn btn-sm btn-primary">
                            <i class="fa fa-plus"></i> 添加房源
                        </button>
                    </div>
                    <div class="card-body">
                        <div id="house-list" class="row">
                            <p class="text-muted col-12">加载中...</p>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 设备管理 -->
            <div class="tab-pane fade" id="devices" role="tabpanel">
                <div class="card">
                    <div class="card-header d-flex justify-content-between align-items-center">
                        <h5 class="mb-0">设备列表</h5>
                        <div>
                            <button id="check-device-expiry-btn" class="btn btn-sm btn-info mr-2">
                                <i class="fa fa-search"></i> 一键检测过期设备
                            </button>
                            <button id="check-device-relations-btn" class="btn btn-sm btn-success mr-2">
                                <i class="fa fa-link"></i> 一键检测关联关系
                            </button>
                            <button id="batch-expire-btn" class="btn btn-sm btn-warning mr-2">
                                <i class="fa fa-clock-o"></i> 批量设置过期时间
                            </button>
                            <button id="add-device-btn" class="btn btn-sm btn-primary">
                                <i class="fa fa-plus"></i> 添加设备
                            </button>
                        </div>
                    </div>
                    <div class="card-body">
                        <!-- 设备统计信息 -->
                        <div id="device-stats" class="row mb-3" style="display: none;">
                            <div class="col-md-2">
                                <div class="small text-center p-2 bg-light rounded">
                                    <div class="font-weight-bold text-info" id="total-devices">0</div>
                                    <div class="text-muted small">总设备</div>
                                </div>
                            </div>
                            <div class="col-md-2">
                                <div class="small text-center p-2 bg-light rounded">
                                    <div class="font-weight-bold text-primary" id="bark-devices">0</div>
                                    <div class="text-muted small">Bark</div>
                                </div>
                            </div>
                            <div class="col-md-2">
                                <div class="small text-center p-2 bg-light rounded">
                                    <div class="font-weight-bold text-success" id="wxpush-devices">0</div>
                                    <div class="text-muted small">微信推送</div>
                                </div>
                            </div>
                            <div class="col-md-2">
                                <div class="small text-center p-2 bg-light rounded">
                                    <div class="font-weight-bold text-warning" id="pushme-devices">0</div>
                                    <div class="text-muted small">PushMe</div>
                                </div>
                            </div>
                            <div class="col-md-2">
                                <div class="small text-center p-2 bg-light rounded">
                                    <div class="font-weight-bold text-danger" id="expired-devices">0</div>
                                    <div class="text-muted small">已过期</div>
                                </div>
                            </div>
                            <div class="col-md-2">
                                <div class="small text-center p-2 bg-light rounded">
                                    <div class="font-weight-bold text-orange" id="expiring-devices">0</div>
                                    <div class="text-muted small">即将过期</div>
                                </div>
                            </div>
                        </div>

                        <!-- 筛选和搜索 -->
                        <div class="row mb-3">
                            <div class="col-md-3">
                                <div class="form-group mb-0">
                                    <label for="device-type-filter" class="sr-only">设备类型</label>
                                    <select class="form-control" id="device-type-filter">
                                        <option value="all">所有设备类型</option>
                                        <option value="bark">Bark设备</option>
                                        <option value="wxpush">微信推送设备</option>
                                        <option value="pushme">PushMe设备</option>
                                    </select>
                                </div>
                            </div>
                            <div class="col-md-3">
                                <div class="form-group mb-0">
                                    <label for="device-search" class="sr-only">搜索设备</label>
                                    <div class="input-group">
                                        <input type="text" class="form-control" id="device-search" placeholder="搜索设备名称...">
                                        <div class="input-group-append">
                                            <button class="btn btn-outline-secondary" type="button" id="clear-search">
                                                <i class="fa fa-times"></i>
                                            </button>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-3">
                                <div class="form-group mb-0">
                                    <label for="device-sort-filter" class="sr-only">排序方式</label>
                                    <select class="form-control" id="device-sort-filter">
                                        <option value="created_desc">按添加时间（最新优先）</option>
                                        <option value="created_asc">按添加时间（最早优先）</option>
                                        <option value="name">按名称排序</option>
                                        <option value="type">按类型排序</option>
                                        <option value="expire_asc">按过期时间排序</option>
                                    </select>
                                </div>
                            </div>
                            <div class="col-md-3">
                                <div class="form-group mb-0">
                                    <label for="devices-per-page" class="sr-only">每页显示</label>
                                    <select class="form-control" id="devices-per-page">
                                        <option value="15">每页15个</option>
                                        <option value="30" selected>每页30个</option>
                                        <option value="45">每页45个</option>
                                        <option value="60">每页60个</option>
                                    </select>
                                </div>
                            </div>
                        </div>

                        <!-- 设备列表 -->
                        <div id="device-list">
                            <p class="text-muted">加载中...</p>
                        </div>

                        <!-- 分页控件 -->
                        <nav aria-label="设备列表分页" id="device-pagination" style="display: none;">
                            <div class="d-flex justify-content-between align-items-center">
                                <div class="text-muted small" id="pagination-info">
                                    显示第 1-10 项，共 0 项
                                </div>
                                <ul class="pagination pagination-sm mb-0" id="pagination-controls">
                                    <!-- 分页按钮将在JavaScript中生成 -->
                                </ul>
                            </div>
                        </nav>
                    </div>
                </div>
            </div>

            <!-- 系统设置 -->
            <div class="tab-pane fade" id="settings" role="tabpanel">
                <div class="card">
                    <div class="card-header">
                        <h5 class="mb-0">配置管理</h5>
                    </div>
                    <div class="card-body">
                        <form id="settings-form">
                            <div class="form-group">
                                <label for="base_url">API基础URL</label>
                                <input type="text" class="form-control" id="base_url" name="base_url">
                        </div>
                            <div class="form-group">
                                <label for="check_interval">检查间隔 (秒)</label>
                                <input type="number" class="form-control" id="check_interval" name="check_interval" min="1">
                    </div>
                            <div class="form-group">
                                <label for="max_retries">最大重试次数</label>
                                <input type="number" class="form-control" id="max_retries" name="max_retries" min="1">
                </div>
                            <div class="form-group">
                                <label for="proxy_api_url">代理API地址</label>
                                <input type="text" class="form-control" id="proxy_api_url" name="proxy_api_url">
                            </div>
                            <button type="submit" class="btn btn-primary">保存设置</button>
                        </form>
                    </div>
                </div>
            </div>

            <!-- 定时任务设置 -->
            <div class="tab-pane fade" id="schedule-settings" role="tabpanel">
                <div class="card">
                    <div class="card-header">
                        <h5 class="mb-0">定时任务设置</h5>
                    </div>
                    <div class="card-body">
                        <!-- 添加状态显示区域 -->
                        <div class="schedule-status mb-4">
                            <div class="alert alert-info">
                                <div class="d-flex align-items-center mb-2">
                                    <i class="fa fa-clock-o fa-2x mr-3"></i>
                                    <h6 class="alert-heading mb-0">定时任务当前状态</h6>
                                </div>
                                <div id="scheduleStatus">
                                    <div class="d-flex align-items-center mb-2">
                                        <div class="status-indicator mr-2" id="statusIndicator"></div>
                                        <span id="scheduleStatusText">加载中...</span>
                                    </div>
                                    <div class="schedule-details">
                                        <p class="mb-1">
                                            <i class="fa fa-play-circle text-success mr-2"></i>
                                            下次启动时间：<span id="nextStartTime">-</span>
                                        </p>
                                        <p class="mb-1">
                                            <i class="fa fa-stop-circle text-danger mr-2"></i>
                                            下次停止时间：<span id="nextStopTime">-</span>
                                        </p>
                                        <p class="mb-1" id="remainingTimeContainer" style="display: none;">
                                            <i class="fa fa-hourglass-half text-info mr-2"></i>
                                            剩余运行时间：<span id="remainingTime">-</span>
                                        </p>
                                        <p class="mb-1">
                                            <i class="fa fa-calendar mr-2"></i>
                                            执行日期：<span id="scheduleDays">-</span>
                                        </p>
                                        <p class="mb-1">
                                            <i class="fa fa-clock-o mr-2"></i>
                                            执行时段：<span id="scheduleTimeRange">-</span>
                                        </p>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <form id="schedule-form">
                            <div class="form-group">
                                <div class="custom-control custom-switch">
                                    <input type="checkbox" class="custom-control-input" id="schedule-enabled">
                                    <label class="custom-control-label" for="schedule-enabled">启用定时任务</label>
                                </div>
                            </div>

                            <div class="form-row">
                                <div class="form-group col-md-6">
                                    <label for="start-time">开始时间</label>
                                    <input type="time" class="form-control" id="start-time">
                                </div>
                                <div class="form-group col-md-6">
                                    <label for="stop-time">停止时间</label>
                                    <input type="time" class="form-control" id="stop-time">
                                </div>
                            </div>

                            <div class="form-group">
                                <label>执行日期</label>
                                <div class="form-check">
                                    <input class="form-check-input" type="checkbox" id="day-0" value="0">
                                    <label class="form-check-label" for="day-0">星期一</label>
                                </div>
                                <div class="form-check">
                                    <input class="form-check-input" type="checkbox" id="day-1" value="1">
                                    <label class="form-check-label" for="day-1">星期二</label>
                                </div>
                                <div class="form-check">
                                    <input class="form-check-input" type="checkbox" id="day-2" value="2">
                                    <label class="form-check-label" for="day-2">星期三</label>
                                </div>
                                <div class="form-check">
                                    <input class="form-check-input" type="checkbox" id="day-3" value="3">
                                    <label class="form-check-label" for="day-3">星期四</label>
                                </div>
                                <div class="form-check">
                                    <input class="form-check-input" type="checkbox" id="day-4" value="4">
                                    <label class="form-check-label" for="day-4">星期五</label>
                                </div>
                                <div class="form-check">
                                    <input class="form-check-input" type="checkbox" id="day-5" value="5">
                                    <label class="form-check-label" for="day-5">星期六</label>
                                </div>
                                <div class="form-check">
                                    <input class="form-check-input" type="checkbox" id="day-6" value="6">
                                    <label class="form-check-label" for="day-6">星期日</label>
                                </div>
                            </div>

                            <button type="submit" id="save-schedule-btn" class="btn btn-primary">保存定时设置</button>
                        </form>
                    </div>
                </div>
            </div>

            <!-- 自定义推送 -->
            <div class="tab-pane fade" id="custom-push" role="tabpanel">
                <div class="card">
                    <div class="card-header">
                        <h5 class="mb-0">自定义推送</h5>
                    </div>
                    <div class="card-body">
                        <form id="custom-push-form">
                            <div class="form-group">
                                <label for="push-title">推送标题</label>
                                <input type="text" class="form-control" id="push-title" placeholder="输入推送标题" required>
                            </div>
                            <div class="form-group">
                                <label for="push-content">推送内容</label>
                                <textarea class="form-control" id="push-content" rows="5" placeholder="输入推送内容" required></textarea>
                            </div>
                            <div class="form-group">
                                <label>选择推送设备</label>
                                <div id="push-device-selector" class="border rounded p-2" style="max-height: 300px; overflow-y: auto;">
                                    <p class="text-muted">加载设备列表中...</p>
                                </div>
                            </div>
                            <button type="submit" class="btn btn-primary">发送推送</button>
                        </form>
                    </div>
                </div>
            </div>

            <!-- 抢房设备管理 -->
            <div class="tab-pane fade" id="grab-devices" role="tabpanel">
                <div class="card">
                    <div class="card-header d-flex justify-content-between align-items-center">
                        <h5 class="mb-0">抢房设备列表</h5>
                        <div>
                            <button id="check-auto-login-status-btn" class="btn btn-sm btn-info mr-2">
                                <i class="fa fa-refresh"></i> 自动登录状态
                            </button>
                            <button id="add-grab-device-btn" class="btn btn-sm btn-primary">
                                <i class="fa fa-plus"></i> 添加抢房设备
                            </button>
                        </div>
                    </div>
                    <div class="card-body">
                        <!-- 自动登录管理状态 -->
                        <div id="auto-login-status" class="alert alert-info mb-3" style="display: none;">
                            <div class="d-flex justify-content-between align-items-center">
                                <div>
                                    <i class="fa fa-robot"></i> <strong>自动Cookie延长管理器</strong>
                                    <span id="auto-login-manager-status" class="badge badge-secondary ml-2">检查中...</span>
                                </div>
                                <div>
                                    <button id="start-auto-login-btn" class="btn btn-sm btn-success mr-2" style="display: none;">
                                        <i class="fa fa-play"></i> 启动
                                    </button>
                                    <button id="stop-auto-login-btn" class="btn btn-sm btn-danger" style="display: none;">
                                        <i class="fa fa-stop"></i> 停止
                                    </button>
                                </div>
                            </div>
                            <div id="auto-login-details" class="mt-2" style="display: none;">
                                <small class="text-muted">
                                    检查间隔: <span id="check-interval-text">-</span> |
                                    Cookie阈值: <span id="cookie-threshold-text">-</span> |
                                    等待设备: <span id="pending-devices-count">0</span>
                                </small>
                            </div>
                        </div>

                        <!-- 等待短信验证码的设备 -->
                        <div id="pending-sms-devices" class="alert alert-warning mb-3" style="display: none;">
                            <h6><i class="fa fa-clock-o"></i> 等待短信验证码的设备</h6>
                            <div id="pending-devices-list">
                                <!-- 动态生成等待设备列表 -->
                            </div>
                        </div>

                        <!-- 统计信息 -->
                        <div id="grab-device-stats" class="row mb-3" style="display: none;">
                            <div class="col-md-3">
                                <div class="small text-center p-2 bg-light rounded">
                                    <div class="font-weight-bold text-info" id="total-grab-devices">0</div>
                                    <div class="text-muted small">总设备</div>
                                </div>
                            </div>
                            <div class="col-md-3">
                                <div class="small text-center p-2 bg-light rounded">
                                    <div class="font-weight-bold text-success" id="enabled-grab-devices">0</div>
                                    <div class="text-muted small">已启用</div>
                                </div>
                            </div>
                            <div class="col-md-3">
                                <div class="small text-center p-2 bg-light rounded">
                                    <div class="font-weight-bold text-warning" id="disabled-grab-devices">0</div>
                                    <div class="text-muted small">已禁用</div>
                                </div>
                            </div>
                            <div class="col-md-3">
                                <div class="small text-center p-2 bg-light rounded">
                                    <div class="font-weight-bold text-primary" id="logged-grab-devices">0</div>
                                    <div class="text-muted small">已登录</div>
                                </div>
                            </div>
                        </div>

                        <!-- 筛选和搜索 -->
                        <div class="row mb-3">
                            <div class="col-md-4">
                                <div class="form-group mb-0">
                                    <label for="grab-device-search" class="sr-only">搜索设备</label>
                                    <div class="input-group">
                                        <input type="text" class="form-control" id="grab-device-search" placeholder="搜索用户名或小区...">
                                        <div class="input-group-append">
                                            <button class="btn btn-outline-secondary" type="button" id="clear-grab-search">
                                                <i class="fa fa-times"></i>
                                            </button>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-4">
                                <div class="form-group mb-0">
                                    <label for="grab-device-enabled-filter" class="sr-only">启用状态</label>
                                    <select class="form-control" id="grab-device-enabled-filter">
                                        <option value="">所有状态</option>
                                        <option value="true">已启用</option>
                                        <option value="false">已禁用</option>
                                    </select>
                                </div>
                            </div>
                            <div class="col-md-4">
                                <div class="form-group mb-0">
                                    <label for="grab-devices-per-page" class="sr-only">每页显示</label>
                                    <select class="form-control" id="grab-devices-per-page">
                                        <option value="15">每页15个</option>
                                        <option value="30" selected>每页30个</option>
                                        <option value="45">每页45个</option>
                                    </select>
                                </div>
                            </div>
                        </div>

                        <!-- 抢房设备列表 -->
                        <div id="grab-device-list">
                            <p class="text-muted">加载中...</p>
                        </div>

                        <!-- 分页控件 -->
                        <nav aria-label="抢房设备列表分页" id="grab-device-pagination" style="display: none;">
                            <div class="d-flex justify-content-between align-items-center">
                                <div class="text-muted small" id="grab-pagination-info">
                                    显示第 1-10 项，共 0 项
                                </div>
                                <ul class="pagination pagination-sm mb-0" id="grab-pagination-controls">
                                    <!-- 分页按钮将在JavaScript中生成 -->
                                </ul>
                            </div>
                        </nav>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- 设备模态框 -->
    <div class="modal fade" id="deviceModal" tabindex="-1" role="dialog" aria-hidden="true">
        <div class="modal-dialog" role="document">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title" id="deviceModalTitle">添加设备</h5>
                    <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                        <span aria-hidden="true">&times;</span>
                    </button>
                </div>
                <div class="modal-body">
                    <form id="device-form">
                        <input type="hidden" id="device-id" name="id">
                        <div class="form-group">
                            <label for="device-name">设备名称</label>
                            <input type="text" class="form-control" id="device-name" name="name" required>
                        </div>
                        <div class="form-group">
                            <label for="device-type">设备类型</label>
                            <select class="form-control" id="device-type" name="type" required>
                                            <option value="bark">Bark</option>
                                <option value="wxpush">微信推送</option>
                                <option value="pushme">PushMe</option>
                                        </select>
                                        </div>
                        <!-- 添加设备过期时间字段 -->
                        <div class="form-group">
                            <label for="device-expire-date">设备过期时间</label>
                            <input type="date" class="form-control" id="device-expire-date" name="expire_date">
                            <small class="form-text text-muted">设备到期后将自动停止使用，留空表示永不过期</small>
                                        </div>
                        <div id="bark-fields">
                            <div class="form-group">
                                <label for="bark-key">Bark Key</label>
                                <input type="text" class="form-control" id="bark-key" name="bark_key">
                                    </div>
                            <div class="form-group">
                                <div class="custom-control custom-switch">
                                    <input type="checkbox" class="custom-control-input" id="bark-call" name="bark_call">
                                    <label class="custom-control-label" for="bark-call">铃声重复播放</label>
                                    <small class="form-text text-muted">启用后通知铃声将重复播放</small>
                                </div>
                            </div>
                            <div class="form-group">
                                <label for="bark-sound">通知铃声</label>
                                <select class="form-control" id="bark-sound" name="bark_sound">
                                    <option value="default">默认铃声</option>
                                    <option value="bell">铃声(bell)</option>
                                    <option value="chime">风铃(chime)</option>
                                    <option value="electronic">电子(electronic)</option>
                                    <option value="minuet">小步舞曲(minuet)</option>
                                    <option value="alarm">警报(alarm)</option>
                                    <option value="birdsong">鸟鸣(birdsong)</option>
                                    <option value="anticipate">期待(anticipate)</option>
                                    <option value="bloom">绽放(bloom)</option>
                                    <option value="calypso">卡利普索(calypso)</option>
                                    <option value="voices">人声(voices)</option>
                                </select>
                            </div>
                        </div>
                        <div id="wxpush-fields" style="display:none">
                            <div class="form-group">
                                <label for="wx-uid">WxPush UID</label>
                                <input type="text" class="form-control" id="wx-uid" name="uid">
                            </div>
                        </div>
                        <div id="pushme-fields" style="display:none">
                            <div class="form-group">
                                <label for="pushme-key">PushMe Key</label>
                                <input type="text" class="form-control" id="pushme-key" name="pushme_key">
                                <small class="form-text text-muted">在PushMe App中获取的接口密钥</small>
                            </div>
                        </div>
                    </form>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-dismiss="modal">取消</button>
                    <button type="button" class="btn btn-primary" id="save-device-btn">保存</button>
                    <button type="button" class="btn btn-success" id="test-device-btn">测试推送</button>
                </div>
            </div>
        </div>
    </div>

    <!-- 房源模态框 -->
    <div class="modal fade" id="houseModal" tabindex="-1" role="dialog" aria-hidden="true">
        <div class="modal-dialog" role="document">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title" id="houseModalTitle">添加房源</h5>
                    <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                        <span aria-hidden="true">&times;</span>
                    </button>
                </div>
                <div class="modal-body">
                    <form id="house-form">
                        <div class="form-group">
                            <label for="house-name">房源名称</label>
                            <input type="text" class="form-control" id="house-name" name="name" required>
                        </div>
                        <div class="form-group">
                            <div class="custom-control custom-switch">
                                <input type="checkbox" class="custom-control-input" id="house-enabled" name="enabled" checked>
                                <label class="custom-control-label" for="house-enabled">启用监控</label>
                            </div>
                        </div>
                        <div class="form-group">
                            <label>选择推送设备</label>
                            <div id="device-selector" class="border rounded p-2" style="max-height: 300px; overflow-y: auto;">
                                <p class="text-muted">加载设备列表中...</p>
                            </div>
                        </div>
                    </form>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-dismiss="modal">取消</button>
                    <button type="button" class="btn btn-primary" id="save-house-btn">保存</button>
                </div>
            </div>
        </div>
    </div>

    <!-- 设备使用房源模态框 -->
    <div class="modal fade" id="deviceHousesModal" tabindex="-1" role="dialog" aria-hidden="true">
        <div class="modal-dialog" role="document">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">设备使用情况</h5>
                    <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                        <span aria-hidden="true">&times;</span>
                    </button>
                </div>
                <div class="modal-body">
                    <div id="device-info" class="mb-3">
                        <h5 id="device-name-display"></h5>
                        <span id="device-type-badge" class="badge badge-info"></span>
                    </div>
                    <div id="device-houses-list">
                        <p class="text-muted">加载中...</p>
                    </div>
                </div>
                <div class="modal-footer d-flex justify-content-between">
                    <div>
                        <span class="text-muted small">修改设备使用的房源状态后，点击保存应用更改</span>
                    </div>
                    <div>
                        <button type="button" class="btn btn-secondary" data-dismiss="modal">取消</button>
                        <button type="button" class="btn btn-primary" id="save-device-houses-btn">保存更改</button>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- 自定义推送结果模态框 -->
    <div class="modal fade" id="pushResultModal" tabindex="-1" role="dialog" aria-hidden="true">
        <div class="modal-dialog modal-lg" role="document">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">推送结果</h5>
                    <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                        <span aria-hidden="true">&times;</span>
                    </button>
                </div>
                <div class="modal-body">
                    <div id="push-result-summary" class="mb-3">
                        <div class="alert alert-info">
                            <strong>发送统计:</strong> <span id="push-success-count">0</span> 成功, <span id="push-fail-count">0</span> 失败
                        </div>
                    </div>
                    <div id="push-result-details">
                        <h6>详细结果:</h6>
                        <div class="table-responsive">
                            <table class="table table-bordered table-sm">
                                <thead>
                                    <tr>
                                        <th>设备名称</th>
                                        <th>设备类型</th>
                                        <th>状态</th>
                                        <th>详情</th>
                                    </tr>
                                </thead>
                                <tbody id="push-result-table">
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-dismiss="modal">关闭</button>
                </div>
            </div>
        </div>
    </div>

    <!-- 设备房源关联关系检测模态框 -->
    <div class="modal fade" id="deviceRelationsModal" tabindex="-1" role="dialog" aria-hidden="true">
        <div class="modal-dialog modal-xl" role="document">
            <div class="modal-content">
                <div class="modal-header bg-success text-white">
                    <h5 class="modal-title"><i class="fa fa-link"></i> 设备与房源关联关系检测报告</h5>
                    <button type="button" class="close text-white" data-dismiss="modal" aria-label="Close">
                        <span aria-hidden="true">&times;</span>
                    </button>
                </div>
                <div class="modal-body">
                    <!-- 统计概览 -->
                    <div class="row mb-4" id="relations-stats">
                        <div class="col-md-3">
                            <div class="card text-center">
                                <div class="card-body">
                                    <h4 class="text-primary" id="total-devices-count">0</h4>
                                    <small class="text-muted">总设备数</small>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="card text-center">
                                <div class="card-body">
                                    <h4 class="text-success" id="linked-devices-count">0</h4>
                                    <small class="text-muted">已关联设备</small>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="card text-center">
                                <div class="card-body">
                                    <h4 class="text-warning" id="orphaned-devices-count">0</h4>
                                    <small class="text-muted">孤立设备</small>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="card text-center">
                                <div class="card-body">
                                    <h4 class="text-info" id="total-relations-count">0</h4>
                                    <small class="text-muted">关联关系数</small>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- 问题警告 -->
                    <div id="relations-issues" style="display: none;">
                        <div class="alert alert-warning">
                            <h6><i class="fa fa-exclamation-triangle"></i> 发现的问题</h6>
                            <div id="issues-list"></div>
                        </div>
                    </div>

                    <!-- Tab导航 -->
                    <ul class="nav nav-tabs" id="relationsTab" role="tablist">
                        <li class="nav-item">
                            <a class="nav-link active" id="orphaned-tab" data-toggle="tab" href="#orphaned" role="tab">
                                孤立设备 <span class="badge badge-warning" id="orphaned-badge">0</span>
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" id="relations-tab" data-toggle="tab" href="#relations" role="tab">
                                关联关系 <span class="badge badge-info" id="relations-badge">0</span>
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" id="by-house-tab" data-toggle="tab" href="#by-house" role="tab">
                                按房源分组
                            </a>
                        </li>
                    </ul>

                    <!-- Tab内容 -->
                    <div class="tab-content" id="relationsTabContent">
                        <!-- 孤立设备 -->
                        <div class="tab-pane fade show active" id="orphaned" role="tabpanel">
                            <div class="mt-3">
                                <div class="d-flex justify-content-between align-items-center mb-3">
                                    <h6>未关联任何房源的设备</h6>
                                    <div>
                                        <button class="btn btn-sm btn-success" id="fix-orphaned-btn" style="display: none;">
                                            <i class="fa fa-wrench"></i> 批量关联到房源
                                        </button>
                                    </div>
                                </div>
                                <div id="orphaned-devices-list">
                                    <p class="text-muted">加载中...</p>
                                </div>
                            </div>
                        </div>

                        <!-- 关联关系 -->
                        <div class="tab-pane fade" id="relations" role="tabpanel">
                            <div class="mt-3">
                                <h6>设备与房源关联关系</h6>
                                <div id="relations-list">
                                    <p class="text-muted">加载中...</p>
                                </div>
                            </div>
                        </div>

                        <!-- 按房源分组 -->
                        <div class="tab-pane fade" id="by-house" role="tabpanel">
                            <div class="mt-3">
                                <h6>按房源分组的设备关联</h6>
                                <div id="relations-by-house-list">
                                    <p class="text-muted">加载中...</p>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-dismiss="modal">关闭</button>
                    <button type="button" class="btn btn-primary" id="refresh-relations-btn">
                        <i class="fa fa-refresh"></i> 刷新检测
                    </button>
                </div>
            </div>
        </div>
    </div>

    <!-- 修复孤立设备模态框 -->
    <div class="modal fade" id="fixOrphanedModal" tabindex="-1" role="dialog" aria-hidden="true">
        <div class="modal-dialog" role="document">
            <div class="modal-content">
                <div class="modal-header bg-success text-white">
                    <h5 class="modal-title"><i class="fa fa-wrench"></i> 批量关联孤立设备</h5>
                    <button type="button" class="close text-white" data-dismiss="modal" aria-label="Close">
                        <span aria-hidden="true">&times;</span>
                    </button>
                </div>
                <div class="modal-body">
                    <div class="form-group">
                        <label for="target-house-select">选择目标房源</label>
                        <select class="form-control" id="target-house-select">
                            <option value="">请选择房源</option>
                        </select>
                    </div>
                    <div class="form-group">
                        <label>选择要关联的设备</label>
                        <div id="orphaned-devices-selector" class="border rounded p-2" style="max-height: 300px; overflow-y: auto;">
                            <p class="text-muted">加载设备列表中...</p>
                        </div>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-dismiss="modal">取消</button>
                    <button type="button" class="btn btn-success" id="confirm-fix-orphaned-btn">
                        <i class="fa fa-check"></i> 确认关联
                    </button>
                </div>
            </div>
        </div>
    </div>

    <!-- 批量设置过期时间模态框 -->
    <div class="modal fade" id="batchExpireModal" tabindex="-1" role="dialog" aria-hidden="true">
        <div class="modal-dialog modal-lg" role="document">
            <div class="modal-content">
                <div class="modal-header bg-warning text-dark">
                    <h5 class="modal-title"><i class="fa fa-clock-o"></i> 批量设置过期时间</h5>
                    <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                        <span aria-hidden="true">&times;</span>
                    </button>
                </div>
                <div class="modal-body">
                    <div class="alert alert-info">
                        <i class="fa fa-info-circle"></i> 批量设置设备过期时间可以帮助您更高效地管理设备。设备过期后将自动停止接收推送通知。
                    </div>

                    <div class="form-group">
                        <label for="batch-expire-date"><i class="fa fa-calendar"></i> 过期时间</label>
                        <input type="date" class="form-control" id="batch-expire-date">
                        <small class="form-text text-muted">选择您希望设备过期的日期</small>
                    </div>

                    <div class="form-group">
                        <label><i class="fa fa-bolt"></i> 快速选择</label>
                        <div class="btn-group d-flex">
                            <button type="button" class="btn btn-outline-primary batch-renew-btn" data-days="30">1个月</button>
                            <button type="button" class="btn btn-outline-primary batch-renew-btn" data-days="90">3个月</button>
                            <button type="button" class="btn btn-outline-primary batch-renew-btn" data-days="180">6个月</button>
                            <button type="button" class="btn btn-outline-primary batch-renew-btn" data-days="365">1年</button>
                        </div>
                    </div>

                    <div class="form-group">
                        <label><i class="fa fa-mobile-phone"></i> 选择设备</label>
                        <div class="mb-2">
                            <button type="button" class="btn btn-sm btn-outline-primary mr-2" id="batch-select-all">全选</button>
                            <button type="button" class="btn btn-sm btn-outline-secondary" id="batch-deselect-all">取消全选</button>
                            <button type="button" class="btn btn-sm btn-outline-warning ml-2" id="batch-select-expiring">仅选择即将过期</button>
                            <button type="button" class="btn btn-sm btn-outline-danger ml-2" id="batch-select-expired">仅选择已过期</button>
                        </div>
                        <div id="batch-device-selector" class="border rounded p-2" style="max-height: 300px; overflow-y: auto;">
                            <p class="text-muted">加载设备列表中...</p>
                        </div>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-dismiss="modal">取消</button>
                    <button type="button" class="btn btn-success" id="confirm-batch-expire-btn"><i class="fa fa-check"></i> 确认设置</button>
                </div>
            </div>
        </div>
    </div>

    <!-- 申请记录查看模态框 -->
    <div class="modal fade" id="applicationRecordsModal" tabindex="-1" role="dialog" aria-hidden="true">
        <div class="modal-dialog modal-xl" role="document">
            <div class="modal-content">
                <div class="modal-header bg-info text-white">
                    <h5 class="modal-title">
                        <i class="fa fa-file-text-o"></i> 申请记录
                    </h5>
                    <button type="button" class="close text-white" data-dismiss="modal" aria-label="Close">
                        <span aria-hidden="true">&times;</span>
                    </button>
                </div>
                <div class="modal-body">
                    <!-- 设备信息 -->
                    <div id="application-device-info" class="alert alert-info mb-3" style="display: none;">
                        <div class="row">
                            <div class="col-md-4">
                                <strong>设备用户名:</strong> <span id="app-device-username">-</span>
                            </div>
                            <div class="col-md-4">
                                <strong>设备手机号:</strong> <span id="app-device-phone">-</span>
                            </div>
                            <div class="col-md-4">
                                <strong>申请记录数:</strong> <span id="app-records-count">0</span> 条
                            </div>
                        </div>
                    </div>

                    <!-- 申请记录列表 -->
                    <div id="application-records-list">
                        <div class="text-center">
                            <div class="spinner-border" role="status">
                                <span class="sr-only">加载中...</span>
                            </div>
                            <p class="mt-2 text-muted">正在获取申请记录...</p>
                        </div>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-dismiss="modal">关闭</button>
                    <button type="button" class="btn btn-primary" id="refresh-application-records-btn">
                        <i class="fa fa-refresh"></i> 刷新记录
                    </button>
                </div>
            </div>
        </div>
    </div>

    <!-- 用户主页查看模态框 -->
    <div class="modal fade" id="userProfileModal" tabindex="-1" role="dialog" aria-hidden="true">
        <div class="modal-dialog modal-xl" role="document">
            <div class="modal-content">
                <div class="modal-header bg-secondary text-white">
                    <h5 class="modal-title">
                        <i class="fa fa-user"></i> 用户主页信息
                    </h5>
                    <button type="button" class="close text-white" data-dismiss="modal" aria-label="Close">
                        <span aria-hidden="true">&times;</span>
                    </button>
                </div>
                <div class="modal-body">
                    <!-- 设备信息 -->
                    <div id="profile-device-info" class="alert alert-secondary mb-3" style="display: none;">
                        <div class="row">
                            <div class="col-md-4">
                                <strong>设备用户名:</strong> <span id="profile-device-username">-</span>
                            </div>
                            <div class="col-md-4">
                                <strong>设备手机号:</strong> <span id="profile-device-phone">-</span>
                            </div>
                            <div class="col-md-4">
                                <strong>获取时间:</strong> <span id="profile-fetch-time">-</span>
                            </div>
                        </div>
                    </div>

                    <!-- 用户主页信息 -->
                    <div id="user-profile-content">
                        <div class="text-center">
                            <div class="spinner-border" role="status">
                                <span class="sr-only">加载中...</span>
                            </div>
                            <p class="mt-2 text-muted">正在获取用户主页信息...</p>
                        </div>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-dismiss="modal">关闭</button>
                    <button type="button" class="btn btn-primary" id="refresh-user-profile-btn">
                        <i class="fa fa-refresh"></i> 刷新信息
                    </button>
                </div>
            </div>
        </div>
    </div>

    <!-- 预上传缓存查看模态框 -->
    <div class="modal fade" id="preuploadCacheModal" tabindex="-1" role="dialog" aria-hidden="true">
        <div class="modal-dialog modal-lg" role="document">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title" id="preuploadCacheModalTitle">预上传缓存状态</h5>
                    <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                        <span aria-hidden="true">&times;</span>
                    </button>
                </div>
                <div class="modal-body">
                    <div id="preupload-cache-content">
                        <div class="text-center">
                            <i class="fa fa-spin fa-spinner"></i> 加载中...
                        </div>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-dismiss="modal">关闭</button>
                    <button type="button" class="btn btn-primary" id="refresh-preupload-cache-btn">
                        <i class="fa fa-refresh"></i> 刷新缓存
                    </button>
                </div>
            </div>
        </div>
    </div>

    <!-- 抢房设备模态框 -->
    <div class="modal fade" id="grabDeviceModal" tabindex="-1" role="dialog" aria-hidden="true">
        <div class="modal-dialog modal-lg" role="document">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title" id="grabDeviceModalTitle">添加抢房设备</h5>
                    <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                        <span aria-hidden="true">&times;</span>
                    </button>
                </div>
                <div class="modal-body">
                    <form id="grab-device-form">
                        <input type="hidden" id="grab-device-id" name="id">

                        <!-- 基本信息 -->
                        <div class="row">
                            <div class="col-md-6">
                                <div class="form-group">
                                    <label for="grab-device-username">用户名 <span class="text-danger">*</span></label>
                                    <input type="text" class="form-control" id="grab-device-username" name="username" required>
                                    <small class="form-text text-muted">用户名必须与profiles目录中的配置一致</small>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="form-group">
                                    <label for="grab-device-phone">手机号 <span class="text-danger">*</span></label>
                                    <input type="tel" class="form-control" id="grab-device-phone" name="phone" required>
                                    <small class="form-text text-muted">用于接收验证码</small>
                                </div>
                            </div>
                        </div>

                        <!-- 目标信息 -->
                        <div class="row">
                            <div class="col-md-6">
                                <div class="form-group">
                                    <label for="grab-target-estate">目标小区</label>
                                    <select class="form-control" id="grab-target-estate" name="target_estate">
                                        <option value="">请选择目标小区</option>
                                    </select>
                                    <small class="form-text text-muted">要抢房的小区名称</small>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="form-group">
                                    <label for="grab-house-id">房源ID</label>
                                    <input type="text" class="form-control" id="grab-house-id" name="house_id">
                                    <small class="form-text text-muted">特定房源ID，可选</small>
                                </div>
                            </div>
                        </div>

                        <!-- 抢房条件 -->
                        <div class="form-group">
                            <label>抢房条件</label>
                            <div id="grab-conditions-container">
                                <div class="row mb-2 grab-condition-row">
                                    <div class="col-md-4">
                                        <select class="form-control condition-type">
                                            <option value="">选择条件类型</option>
                                            <option value="area">面积</option>
                                            <option value="direction">朝向</option>
                                            <option value="building">楼号</option>
                                            <option value="floor">楼层</option>
                                            <option value="roomno">房间号</option>
                                            <option value="outtype">租赁类型</option>
                                            <option value="rent">租金</option>
                                        </select>
                                    </div>
                                    <div class="col-md-6">
                                        <input type="text" class="form-control condition-value" placeholder="输入条件值，如: >50, 南向, 1号楼">
                                    </div>
                                    <div class="col-md-2">
                                        <button type="button" class="btn btn-danger btn-sm remove-condition" style="display: none;">
                                            <i class="fa fa-times"></i>
                                        </button>
                                    </div>
                                </div>
                            </div>
                            <button type="button" class="btn btn-sm btn-outline-primary" id="add-condition-btn">
                                <i class="fa fa-plus"></i> 添加条件
                            </button>
                            <small class="form-text text-muted">
                                不设置条件表示无条件抢房。面积示例：>50（大于50平米），朝向示例：南向
                            </small>
                        </div>

                        <!-- 启用状态 -->
                        <div class="form-group">
                            <div class="custom-control custom-switch">
                                <input type="checkbox" class="custom-control-input" id="grab-device-enabled" name="enabled" checked>
                                <label class="custom-control-label" for="grab-device-enabled">启用设备</label>
                            </div>
                        </div>

                        <!-- 登录状态 -->
                        <div id="login-status-section" style="display: none;">
                            <hr>
                            <h6>登录状态</h6>

                            <!-- Cookie状态显示 -->
                            <div class="alert alert-info" id="login-status-alert">
                                <p class="mb-2">
                                    Cookie状态: <span id="cookie-status">未登录</span>
                                    <span id="cookie-remaining-time" class="ml-2 badge badge-secondary"></span>
                                </p>
                                <!-- Cookie管理按钮 -->
                                <div class="cookie-management-buttons" style="display: none;">
                                    <button type="button" class="btn btn-sm btn-success mr-2" id="extend-cookie-modal-btn">
                                        <i class="fa fa-refresh"></i> 延长Cookie
                                    </button>
                                    <button type="button" class="btn btn-sm btn-info mr-2" id="test-cookie-modal-btn">
                                        <i class="fa fa-flask"></i> 测试Cookie
                                    </button>
                                    <button type="button" class="btn btn-sm btn-secondary" id="debug-cookie-btn">
                                        <i class="fa fa-bug"></i> 调试
                                    </button>
                                </div>
                            </div>

                            <!-- 一键自动登录 -->
                            <div class="form-group">
                                <button type="button" class="btn btn-warning btn-block" id="auto-login-btn" onclick="autoLoginGrabDevice()">
                                    <i class="fa fa-magic"></i> 一键自动登录
                                </button>
                                <small class="form-text text-muted">自动识别验证码并发送短信，您只需输入短信验证码即可完成登录</small>
                            </div>

                            <!-- 自动登录进度显示 -->
                            <div id="auto-login-progress" style="display: none;"></div>

                            <!-- 手动登录流程 -->
                            <div id="manual-login-section">
                                <hr>
                                <h6 class="text-muted">手动登录流程</h6>

                                <!-- 步骤1: 获取图形验证码 -->
                                <div class="form-group">
                                    <label>步骤1: 获取图形验证码</label>
                                    <button type="button" class="btn btn-outline-primary btn-sm" id="get-captcha-btn">
                                        <i class="fa fa-image"></i> 获取验证码图片
                                    </button>

                                    <!-- 验证码图片显示 -->
                                    <div id="captcha-display" style="display: none; margin-top: 10px;">
                                        <img id="captcha-image" style="max-width: 200px; border: 1px solid #ddd;" />
                                        <div id="captcha-candidates" style="margin-top: 5px;"></div>
                                    </div>
                                </div>

                                <!-- 步骤2: 输入图形验证码 -->
                                <div class="form-group">
                                    <label for="img-verification-code">步骤2: 输入图形验证码</label>
                                    <div class="input-group">
                                        <input type="text" class="form-control" id="img-verification-code" placeholder="请输入图形验证码">
                                        <div class="input-group-append">
                                            <button type="button" class="btn btn-outline-secondary btn-sm" id="send-sms-btn">
                                                <i class="fa fa-mobile"></i> 发送短信验证码
                                            </button>
                                        </div>
                                    </div>
                                </div>

                                <!-- 步骤3: 输入短信验证码 -->
                                <div class="form-group">
                                    <label for="sms-verification-code">步骤3: 输入短信验证码</label>
                                    <div class="input-group">
                                        <input type="text" class="form-control" id="sms-verification-code" placeholder="请输入短信验证码">
                                        <div class="input-group-append">
                                            <button type="button" class="btn btn-success btn-sm" id="login-device-btn">
                                                <i class="fa fa-sign-in"></i> 完成登录
                                            </button>
                                        </div>
                                    </div>
                                </div>
                            </div> <!-- 关闭 manual-login-section -->
                        </div> <!-- 关闭 login-status-section -->
                    </form>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-dismiss="modal">取消</button>
                    <button type="button" class="btn btn-primary" id="save-grab-device-btn">保存</button>
                    <button type="button" class="btn btn-info" id="view-profile-btn" style="display: none;">
                        <i class="fa fa-user"></i> 查看配置
                    </button>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/jquery@3.6.0/dist/jquery.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@4.6.2/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        let config = {}; // 全局配置对象
        let devices = []; // 设备列表
        let houses = []; // 房源列表
        let currentHouseIndex = -1; // 当前操作的房源索引
        let selectedDeviceIds = []; // 跟踪已选择的设备ID

        // 抢房设备相关变量
        let grabDevices = []; // 抢房设备列表
        let currentGrabDeviceIndex = -1; // 当前操作的抢房设备索引

        // 设备分页相关变量
        let devicePagination = {
            currentPage: 1,
            perPage: 15,
            totalPages: 1,
            totalCount: 0,
            currentType: 'all',
            currentSearch: '',
            currentSort: 'created_desc'
        };

        // 抢房设备分页相关变量
        let grabDevicePagination = {
            currentPage: 1,
            perPage: 30,
            totalPages: 1,
            totalCount: 0,
            currentSearch: '',
            enabledFilter: ''
        };

        // 显示/隐藏加载中状态
        function toggleLoading(show) {
            if (show) {
                $('#loading').show();
            } else {
                $('#loading').hide();
            }
        }

        // 初始化页面
        function initPage() {
            // 加载配置
            loadConfig();

            // 设置点击事件
            $('#start-btn').click(startMonitor);
            $('#stop-btn').click(stopMonitor);
            $('#add-device-btn').click(showAddDeviceModal);
            $('#add-house-btn').click(showAddHouseModal);
            $('#device-type').change(toggleDeviceFields);
            $('#save-device-btn').click(saveDevice);
            $('#test-device-btn').click(testDevice);
            $('#save-house-btn').click(saveHouse);
            $('#settings-form').submit(saveSettings);

            // 抢房设备相关事件 - 使用事件委托确保动态生成的按钮也能绑定
            $('#add-grab-device-btn').click(showAddGrabDeviceModal);

            // 阻止抢房设备表单的默认提交行为
            $(document).on('submit', '#grab-device-form', function(e) {
                console.log('表单提交被阻止');
                e.preventDefault();
                return false;
            });

            $(document).on('click', '#save-grab-device-btn', function(e) {
                console.log('保存按钮点击事件被触发', e); // 添加更详细的调试日志
                e.preventDefault(); // 阻止默认行为
                saveGrabDevice();
            });
            $(document).on('click', '#add-condition-btn', addGrabCondition);
            $(document).on('click', '#get-captcha-btn', getCaptchaImage);
            $(document).on('click', '#send-sms-btn', sendSmsCode);
            $(document).on('click', '#login-device-btn', loginGrabDevice);
            $(document).on('click', '#view-profile-btn', viewGrabDeviceProfile);
            $(document).on('click', '#extend-cookie-modal-btn', extendCookieFromModal);
            $(document).on('click', '#test-cookie-modal-btn', testCookieFromModal);
            $(document).on('click', '#debug-cookie-btn', debugCookieStatus);

            // 删除抢房条件按钮事件委托
            $(document).on('click', '.remove-condition', function() {
                console.log('删除条件按钮被点击'); // 添加调试日志
                $(this).closest('.grab-condition-row').remove();
                updateRemoveConditionButtons();
            });

            // 注意：autoLoginGrabDevice 已在HTML中使用onclick绑定

            // 定时刷新状态
            setInterval(updateStatus, 10000);
        }

        // 加载配置
        function loadConfig() {
            toggleLoading(true);
            $.ajax({
                url: '/api/config',
                method: 'GET',
                success: function(data) {
                    config = data;
                    devices = config.device_list || [];
                    houses = config.monitor_configs || [];

                    // 更新状态
                    updateStatus();

                    // 填充表单
                    $('#base_url').val(config.base_url);
                    $('#check_interval').val(config.check_interval);
                    $('#max_retries').val(config.max_retries);
                    $('#proxy_api_url').val(config.proxy_api_url);

                    // 加载设备列表
                    loadDeviceList();

                    // 加载房源列表
                    renderHouseList();

                    // 重新加载定时任务设置（确保使用最新配置）
                    if ($('#schedule-settings-tab').hasClass('active')) {
                        loadScheduleSettings();
                    }

                    toggleLoading(false);
                },
                error: function(xhr) {
                    alert('加载配置失败: ' + (xhr.responseJSON?.error || xhr.statusText));
                    toggleLoading(false);
                }
            });
        }

        // 更新状态
        function updateStatus() {
            $.ajax({
                url: '/api/status',
                method: 'GET',
                success: function(data) {
                    let isRunning = data.is_running;
                    $('#status-text').text(isRunning ? '运行中' : '已停止');
                    $('#proxy-text').text(data.proxy || '无');
                    $('#remaining-proxies').text(data.remaining_proxies || 0);

                    // 格式化时间显示
                    let fetchTime = data.last_proxy_fetch_time ? new Date(data.last_proxy_fetch_time * 1000).toLocaleString() : '无';
                    $('#last-fetch-time').text(fetchTime);

                    // 启用/禁用按钮
                    $('#start-btn').prop('disabled', isRunning);
                    $('#stop-btn').prop('disabled', !isRunning);

                    // 如果正在运行，获取房源数量
                    if (isRunning) {
                        getHouseCounts();
                    }
                },
                error: function() {
                    $('#status-text').text('无法获取状态');
                }
            });
        }

        // 获取房源数量
        function getHouseCounts() {
            $.ajax({
                url: '/api/house_counts',
                method: 'GET',
                success: function(data) {
                    let counts = data.counts;
                    let html = '';

                    if (Object.keys(counts).length === 0) {
                        html = '<p class="text-muted">暂无房源数据</p>';
                } else {
                        html = '<ul class="list-group">';
                        for (let name in counts) {
                            html += `<li class="list-group-item d-flex justify-content-between align-items-center">
                                ${name}
                                <span class="badge badge-primary badge-pill">${counts[name]}</span>
                            </li>`;
                        }
                        html += '</ul>';
                    }

                    $('#house-counts').html(html);
                },
                error: function() {
                    $('#house-counts').html('<p class="text-danger">获取房源数量失败</p>');
                }
            });
        }

        // 启动监控
        function startMonitor() {
            toggleLoading(true);
            $.ajax({
                url: '/api/monitor/start',
                method: 'POST',
                success: function() {
                    updateStatus();
                    toggleLoading(false);
                },
                error: function(xhr) {
                    alert('启动监控失败: ' + (xhr.responseJSON?.error || xhr.statusText));
                    toggleLoading(false);
                }
            });
        }

        // 停止监控
        function stopMonitor() {
            toggleLoading(true);
            $.ajax({
                url: '/api/monitor/stop',
                method: 'POST',
                success: function() {
                    updateStatus();
                    toggleLoading(false);
                },
                error: function(xhr) {
                    alert('停止监控失败: ' + (xhr.responseJSON?.error || xhr.statusText));
                    toggleLoading(false);
                }
            });
        }

                // 加载设备列表（支持分页和筛选）
        function loadDeviceList() {
            const params = new URLSearchParams({
                page: devicePagination.currentPage,
                per_page: devicePagination.perPage,
                type: devicePagination.currentType,
                search: devicePagination.currentSearch,
                sort: devicePagination.currentSort
            });

            toggleLoading(true);

            $.ajax({
                url: '/api/devices?' + params.toString(),
                method: 'GET',
                success: function(response) {
                    // 更新全局设备列表（保持兼容性）
                    devices = response.devices;

                    // 更新分页信息
                    devicePagination.totalPages = response.pagination.total_pages;
                    devicePagination.totalCount = response.pagination.total_count;
                    devicePagination.currentPage = response.pagination.page;

                    // 直接渲染设备列表（后端已排序）
                    renderDeviceList(response.devices);

                    // 更新统计信息
                    updateDeviceStats(response.stats);

                    // 更新分页控件
                    updatePaginationControls(response.pagination);

                    toggleLoading(false);
                },
                error: function(xhr) {
                    $('#device-list').html('<p class="text-danger">加载设备列表失败: ' + (xhr.responseJSON?.error || xhr.statusText) + '</p>');
                    toggleLoading(false);
                }
            });
        }

        // 手动检测设备过期状态
        function checkDeviceExpiry() {
            if (!confirm('确定要手动检测所有设备的过期状态并发送通知吗？\n\n注意：此操作将忽略通知冷却时间，立即向过期和即将过期的设备发送通知。')) {
                return;
            }

            toggleLoading(true);
            const button = $('#check-device-expiry-btn');
            const originalText = button.html();
            button.prop('disabled', true).html('<i class="fa fa-spin fa-spinner"></i> 检测中...');

            $.ajax({
                url: '/api/devices/check_expiry',
                method: 'POST',
                contentType: 'application/json',
                data: JSON.stringify({}),
                success: function(response) {
                    toggleLoading(false);
                    button.prop('disabled', false).html(originalText);

                    if (response.status === 'success') {
                        const result = response.result;
                        let message = `设备过期检查完成！\n\n`;
                        message += `检查设备总数: ${result.total_devices_checked}\n`;
                        message += `已过期设备: ${result.expired_count} 个\n`;
                        message += `即将过期设备: ${result.expiring_count} 个\n`;
                        message += `发送通知: ${result.notifications_sent} 条\n`;

                        if (result.notification_errors > 0) {
                            message += `通知发送失败: ${result.notification_errors} 条\n`;
                        }

                        // 显示详细信息
                        if (result.expired_devices && result.expired_devices.length > 0) {
                            message += `\n已过期设备：\n`;
                            result.expired_devices.forEach(device => {
                                message += `- ${device.name} (过期时间: ${device.expire_date})\n`;
                            });
                        }

                        if (result.expiring_devices && result.expiring_devices.length > 0) {
                            message += `\n即将过期设备：\n`;
                            result.expiring_devices.forEach(device => {
                                message += `- ${device.name} (剩余 ${device.days_left} 天)\n`;
                            });
                        }

                        alert(message);

                        // 刷新设备列表
                        loadDeviceList();
                    } else {
                        alert('检测失败: ' + (response.error || '未知错误'));
                    }
                },
                error: function(xhr) {
                    toggleLoading(false);
                    button.prop('disabled', false).html(originalText);
                    alert('检测设备过期状态失败: ' + (xhr.responseJSON?.error || xhr.statusText));
                }
            });
        }

        // 渲染设备列表
        function renderDeviceList(deviceList) {
            let html = '';

            if (deviceList.length === 0) {
                if (devicePagination.currentType !== 'all' || devicePagination.currentSearch) {
                    html = '<p class="text-muted">未找到符合条件的设备</p>';
                } else {
                    html = '<p class="text-muted">暂无设备，请添加推送设备</p>';
                }
            } else {
                // 使用卡片网格布局
                html += '<div class="row">';

                deviceList.forEach((device, i) => {
                    // 获取在全局设备列表中的索引（用于编辑功能）
                    let globalIndex = devices.findIndex(d => d.id === device.id);
                    html += renderDeviceCard(device, globalIndex);
                });

                html += '</div>';
            }

            $('#device-list').html(html);

            // 添加按钮事件监听
            $('.edit-device-btn').click(function() {
                let index = $(this).data('index');
                showEditDeviceModal(index);
            });

            $('.delete-device-btn').click(function() {
                let deviceId = $(this).data('id');
                deleteDevice(deviceId);
            });

            $('.view-device-houses-btn').click(function() {
                let deviceId = $(this).data('id');
                showDeviceHousesModal(deviceId);
            });

            // 添加续费按钮事件
            $('.renew-device-btn').click(function() {
                let index = $(this).data('index');
                showRenewDeviceModal(index);
            });
        }

                // 渲染设备卡片
        function renderDeviceCard(device, index) {
            let deviceType = device.type || 'unknown';
            let typeBadge = '';
            let cardClass = 'card device-card h-100';

            // 根据设备类型设置不同的徽章和样式
            if (deviceType === 'bark') {
                typeBadge = '<span class="badge badge-info">Bark</span>';
            } else if (deviceType === 'wxpush') {
                typeBadge = '<span class="badge badge-success">微信推送</span>';
            } else if (deviceType === 'pushme') {
                typeBadge = '<span class="badge badge-warning">PushMe</span>';
            } else {
                typeBadge = `<span class="badge badge-secondary">${deviceType}</span>`;
            }

            // 使用后端提供的过期状态信息
            let expireBadge = '';
            let expireInfo = '';
            let expireStatus = device.expire_status || 'normal';

            if (device.expire_date) {
                if (expireStatus === 'expired') {
                    // 已过期
                    expireBadge = '<span class="badge badge-danger badge-expired"><i class="fa fa-exclamation-circle"></i> 已过期</span>';
                    cardClass += ' border-danger expired';
                    expireInfo = `<small class="text-danger">过期时间: ${device.expire_date}</small>`;
                } else if (expireStatus === 'expiring') {
                    // 即将过期
                    const daysLeft = device.expire_days_left || 0;
                    expireBadge = `<span class="badge badge-warning badge-expiring"><i class="fa fa-clock-o"></i> 剩余${daysLeft}天</span>`;
                    cardClass += ' border-warning expiring-soon';
                    expireInfo = `<small class="text-warning">过期时间: ${device.expire_date}</small>`;
                } else {
                    // 正常
                    cardClass += ' border-success';
                    const daysLeft = device.expire_days_left;
                    if (daysLeft !== null && daysLeft > 0) {
                        expireInfo = `<small class="text-muted">过期时间: ${device.expire_date} (剩余${daysLeft}天)</small>`;
                    } else {
                        expireInfo = `<small class="text-muted">过期时间: ${device.expire_date}</small>`;
                    }
                }
            } else {
                expireInfo = `<small class="text-muted">无过期时间</small>`;
            }

            // 添加续费按钮（仅对已过期或即将过期的设备显示）
            let renewButton = '';
            if (expireStatus === 'expired' || expireStatus === 'expiring' ||
                (device.expire_date && device.expire_days_left !== null && device.expire_days_left <= 7)) {
                renewButton = `
                    <button class="btn btn-sm btn-success renew-device-btn mb-1" data-index="${index}" title="续费">
                        <i class="fa fa-refresh"></i>
                    </button>
                `;
            }

            return `
                <div class="col-md-6 col-lg-4 mb-3">
                    <div class="${cardClass}">
                        <div class="card-header d-flex justify-content-between align-items-center">
                            <div>
                                <strong class="card-title mb-0">${device.name}</strong>
                            </div>
                            <div>
                                ${typeBadge}
                                ${expireBadge}
                            </div>
                        </div>
                        <div class="card-body">
                            <div class="mb-2">
                                ${expireInfo}
                            </div>
                            <div class="device-actions d-flex flex-wrap gap-1">
                                <button class="btn btn-sm btn-outline-info view-device-houses-btn" data-id="${device.id}" title="使用情况">
                                    <i class="fa fa-eye"></i>
                                </button>
                                ${renewButton}
                                <button class="btn btn-sm btn-outline-primary edit-device-btn" data-index="${index}" title="编辑">
                                    <i class="fa fa-edit"></i>
                                </button>
                                <button class="btn btn-sm btn-outline-danger delete-device-btn" data-id="${device.id}" title="删除">
                                    <i class="fa fa-trash"></i>
                                </button>
                            </div>
                        </div>
                    </div>
                </div>
            `;
        }

        // 更新设备统计信息
        function updateDeviceStats(stats) {
            $('#total-devices').text(stats.total || 0);
            $('#bark-devices').text(stats.bark || 0);
            $('#wxpush-devices').text(stats.wxpush || 0);
            $('#pushme-devices').text(stats.pushme || 0);
            $('#expired-devices').text(stats.expired || 0);
            $('#expiring-devices').text(stats.expiring || 0);

            // 显示统计信息区域
            if (stats.total > 0) {
                $('#device-stats').show();
            } else {
                $('#device-stats').hide();
            }
        }

        // 更新分页控件
        function updatePaginationControls(pagination) {
            if (pagination.total_pages <= 1) {
                $('#device-pagination').hide();
                return;
            }

            $('#device-pagination').show();

            // 更新分页信息
            const startItem = (pagination.page - 1) * pagination.per_page + 1;
            const endItem = Math.min(pagination.page * pagination.per_page, pagination.total_count);
            $('#pagination-info').text(`显示第 ${startItem}-${endItem} 项，共 ${pagination.total_count} 项`);

            // 生成分页按钮
            let paginationHtml = '';

            // 上一页按钮
            if (pagination.has_prev) {
                paginationHtml += `<li class="page-item"><a class="page-link" href="#" data-page="${pagination.page - 1}">上一页</a></li>`;
            } else {
                paginationHtml += `<li class="page-item disabled"><span class="page-link">上一页</span></li>`;
            }

            // 页码按钮
            const maxPagesToShow = 5;
            let startPage = Math.max(1, pagination.page - Math.floor(maxPagesToShow / 2));
            let endPage = Math.min(pagination.total_pages, startPage + maxPagesToShow - 1);

            // 调整起始页
            if (endPage - startPage + 1 < maxPagesToShow) {
                startPage = Math.max(1, endPage - maxPagesToShow + 1);
            }

            if (startPage > 1) {
                paginationHtml += `<li class="page-item"><a class="page-link" href="#" data-page="1">1</a></li>`;
                if (startPage > 2) {
                    paginationHtml += `<li class="page-item disabled"><span class="page-link">...</span></li>`;
                }
            }

            for (let i = startPage; i <= endPage; i++) {
                if (i === pagination.page) {
                    paginationHtml += `<li class="page-item active"><span class="page-link">${i}</span></li>`;
                } else {
                    paginationHtml += `<li class="page-item"><a class="page-link" href="#" data-page="${i}">${i}</a></li>`;
                }
            }

            if (endPage < pagination.total_pages) {
                if (endPage < pagination.total_pages - 1) {
                    paginationHtml += `<li class="page-item disabled"><span class="page-link">...</span></li>`;
                }
                paginationHtml += `<li class="page-item"><a class="page-link" href="#" data-page="${pagination.total_pages}">${pagination.total_pages}</a></li>`;
            }

            // 下一页按钮
            if (pagination.has_next) {
                paginationHtml += `<li class="page-item"><a class="page-link" href="#" data-page="${pagination.page + 1}">下一页</a></li>`;
            } else {
                paginationHtml += `<li class="page-item disabled"><span class="page-link">下一页</span></li>`;
            }

            $('#pagination-controls').html(paginationHtml);

            // 绑定分页点击事件
            $('#pagination-controls .page-link[data-page]').click(function(e) {
                e.preventDefault();
                const page = parseInt($(this).data('page'));
                if (page !== devicePagination.currentPage) {
                    devicePagination.currentPage = page;
                    loadDeviceList();
                }
            });
        }

        // 渲染房源列表
        function renderHouseList() {
            let html = '';

            if (houses.length === 0) {
                html = '<p class="text-muted col-12">暂无房源，请添加房源</p>';
            } else {
                for (let i = 0; i < houses.length; i++) {
                    let house = houses[i];
                    let enabled = house.enabled !== false;
                    let badgeClass = enabled ? 'badge-enabled' : 'badge-disabled';
                    let badgeText = enabled ? '已启用' : '已禁用';

                    // 获取关联的设备名称
                    let deviceNames = [];
                    if (house.device_ids && house.device_ids.length > 0) {
                        for (let deviceId of house.device_ids) {
                            let device = devices.find(d => d.id === deviceId);
                            if (device) {
                                deviceNames.push(device.name);
                            }
                        }
                    }

                    html += `<div class="col-md-6 col-lg-4">
                        <div class="card">
                            <div class="card-header d-flex justify-content-between align-items-center">
                                <h5 class="mb-0">${house.name}</h5>
                                <span class="badge ${badgeClass}">${badgeText}</span>
                    </div>
                            <div class="card-body">
                                <p><strong>推送设备:</strong> ${house.device_ids && house.device_ids.length ? house.device_ids.length + ' 个设备' : '无'}</p>
                                <div class="btn-group btn-block">
                                    <button class="btn btn-sm btn-outline-primary edit-house-btn" data-index="${i}">
                                        <i class="fa fa-edit"></i> 编辑
                    </button>
                                    <button class="btn btn-sm btn-outline-danger delete-house-btn" data-index="${i}">
                                        <i class="fa fa-trash"></i> 删除
                    </button>
                </div>
                            </div>
                        </div>
                    </div>`;
                }
            }

            $('#house-list').html(html);

            // 添加事件监听
            $('.edit-house-btn').click(function() {
                let index = $(this).data('index');
                showEditHouseModal(index);
            });

            $('.delete-house-btn').click(function() {
                let index = $(this).data('index');
                deleteHouse(index);
            });
        }

        // 显示添加设备模态框
        function showAddDeviceModal() {
            // 重置表单
            $('#device-form')[0].reset();
            $('#device-id').val('device' + Date.now()); // 生成新的设备ID
            $('#deviceModalTitle').text('添加设备');

            // 启用设备类型选择
            $('#device-type').prop('disabled', false);

            // 初始化字段显示
            let type = $('#device-type').val();
            if (type === 'bark') {
                $('#bark-fields').show();
                $('#wxpush-fields').hide();
            } else if (type === 'wxpush') {
                $('#bark-fields').hide();
                $('#wxpush-fields').show();
            }

            $('#deviceModal').modal('show');
        }

        // 显示编辑设备模态框
        function showEditDeviceModal(index) {
            let device = devices[index];

            // 填充表单
            $('#device-id').val(device.id);
            $('#device-name').val(device.name);
            $('#device-type').val(device.type);
            $('#device-expire-date').val(device.expire_date || '');
            // 禁用设备类型选择
            $('#device-type').prop('disabled', true);

            if (device.type === 'bark') {
                $('#bark-key').val(device.bark_key);

                // 从params中读取设置
                if (device.params) {
                    // 铃声重复播放
                    $('#bark-call').prop('checked', device.params.call === "1");
                    // 铃声类型
                    $('#bark-sound').val(device.params.sound || "bell");
                } else {
                    // 默认值
                    $('#bark-call').prop('checked', false);
                    $('#bark-sound').val("bell");
                }
            } else if (device.type === 'wxpush') {
                $('#wx-uid').val(device.uid);
            } else if (device.type === 'pushme') {
                $('#pushme-key').val(device.pushme_key);
            }

            $('#deviceModalTitle').text('编辑设备');
            $('#deviceModal').modal('show');
            // 确保在模态框显示后立即调用一次
            setTimeout(toggleDeviceFields, 100);
        }

        // 切换设备类型字段显示
        function toggleDeviceFields() {
            let type = $('#device-type').val();

            if (type === 'bark') {
                $('#bark-fields').show();
                $('#wxpush-fields').hide();
                $('#pushme-fields').hide();
            } else if (type === 'wxpush') {
                $('#bark-fields').hide();
                $('#wxpush-fields').show();
                $('#pushme-fields').hide();
            } else if (type === 'pushme') {
                $('#bark-fields').hide();
                $('#wxpush-fields').hide();
                $('#pushme-fields').show();
            }
        }

        // 保存设备
        function saveDevice() {
            try {
                let deviceData = {
                    id: $('#device-id').val(),
                    name: $('#device-name').val(),
                    type: $('#device-type').val(),
                    expire_date: $('#device-expire-date').val() || null
                };

                if (!deviceData.id || !deviceData.name || !deviceData.type) {
                    alert('请填写完整的设备信息');
                    return;
                }

                if (deviceData.type === 'bark') {
                    deviceData.bark_key = $('#bark-key').val();
                    let bark_call = $('#bark-call').is(':checked');
                    let bark_sound = $('#bark-sound').val() || "bell";

                    if (!deviceData.bark_key) {
                        alert('请填写Bark Key');
                        return;
                    }

                    // 确保params对象正确设置
                    deviceData.params = {
                        "call": bark_call ? "1" : "0",  // 一定要是字符串"1"或"0"
                        "sound": bark_sound
                    };

                    // 记录日志以便调试
                    console.log('Bark设备参数:', deviceData.params);

                } else if (deviceData.type === 'wxpush') {
                    deviceData.uid = $('#wx-uid').val();

                    if (!deviceData.uid) {
                        alert('请填写微信推送UID');
                        return;
                    }
                } else if (deviceData.type === 'pushme') {
                    deviceData.pushme_key = $('#pushme-key').val();

                    if (!deviceData.pushme_key) {
                        alert('请填写PushMe Key');
                        return;
                    }
                }

                // 检查是否是编辑现有设备
                let existingIndex = devices.findIndex(d => d.id === deviceData.id);

                toggleLoading(true);

                if (existingIndex >= 0) {
                    // 更新现有设备
                    $.ajax({
                        url: '/api/devices/' + deviceData.id,
                        method: 'PUT',
                        contentType: 'application/json',
                        data: JSON.stringify(deviceData),
                        success: function(result) {
                            devices[existingIndex] = result.device;
                            loadDeviceList();
                            $('#deviceModal').modal('hide');
                            toggleLoading(false);
                        },
                        error: function(xhr) {
                            alert('更新设备失败: ' + (xhr.responseJSON?.error || xhr.statusText));
                            toggleLoading(false);
                        }
                    });
                } else {
                    // 添加新设备
                    $.ajax({
                        url: '/api/devices',
                        method: 'POST',
                        contentType: 'application/json',
                        data: JSON.stringify(deviceData),
                        success: function(result) {
                            devices.push(result.device);
                            loadDeviceList();
                            $('#deviceModal').modal('hide');
                            toggleLoading(false);
                        },
                        error: function(xhr) {
                            alert('添加设备失败: ' + (xhr.responseJSON?.error || xhr.statusText));
                            toggleLoading(false);
                        }
                    });
                }
            } catch (e) {
                alert('保存设备时出错: ' + e.message);
                toggleLoading(false);
            }
        }

        // 测试设备
        function testDevice() {
            let deviceId = $('#device-id').val();
            let deviceName = $('#device-name').val() || '测试设备';
            let deviceType = $('#device-type').val();

            toggleLoading(true);

            $.ajax({
                url: '/api/test_notification',
                method: 'POST',
                contentType: 'application/json',
                data: JSON.stringify({
                    house_name: deviceName,
                    device_id: deviceId
                }),
                success: function(result) {
                    toggleLoading(false);

                    // 构建详细的成功信息
                    let successMsg = '测试推送发送成功，请检查设备是否收到';

                    // 为Bark设备显示详细参数
                    if (deviceType === 'bark' && result.params) {
                        let paramDetails = [];

                        // 检查铃声重复播放
                        if (result.params.call === '1') {
                            paramDetails.push('✓ 铃声重复播放');
                        } else {
                            paramDetails.push('✗ 铃声重复播放');
                        }

                        // 检查铃声类型
                        if (result.params.sound) {
                            paramDetails.push(`铃声类型: ${result.params.sound}`);
                        }

                        // 其他参数
                        if (result.params.subtitle) {
                            paramDetails.push(`副标题: ${result.params.subtitle}`);
                        }

                        let detailsText = paramDetails.join('\n');
                        successMsg = `测试推送发送成功!\n\n设备: ${result.device}\n${detailsText}\n\n请检查设备是否收到通知`;
                    } else if (result.message) {
                        successMsg = '测试推送发送成功: ' + result.message + '\n请检查设备是否收到';
                    }

                    // 显示成功消息
                    alert(successMsg);
                },
                error: function(xhr) {
                    toggleLoading(false);
                    alert('测试推送失败: ' + (xhr.responseJSON?.error || xhr.statusText));
                }
            });
        }

        // 删除设备
        function deleteDevice(deviceId) {
            if (!confirm('确定要删除此设备吗？如果此设备已经被房源使用，可能无法删除。')) {
                return;
            }

            // 根据ID找到设备
            let device = devices.find(d => d.id === deviceId);
            if (!device) {
                alert('找不到设备: ' + deviceId);
                return;
            }

            toggleLoading(true);

            $.ajax({
                url: '/api/devices/' + deviceId,
                method: 'DELETE',
                success: function() {
                    // 删除设备后从列表中移除
                    let index = devices.findIndex(d => d.id === deviceId);
                    if (index >= 0) {
                        devices.splice(index, 1);
                    }
                    loadDeviceList();
                    toggleLoading(false);
                },
                error: function(xhr) {
                    let error = xhr.responseJSON?.error || xhr.statusText;
                    let houses = xhr.responseJSON?.houses || [];

                    if (houses.length > 0) {
                        error += '\n以下房源正在使用此设备:\n' + houses.join('\n');
                    }

                    alert('删除设备失败: ' + error);
                    toggleLoading(false);
                }
            });
        }

        // 显示添加房源模态框
        function showAddHouseModal() {
            // 重置表单
            $('#house-form')[0].reset();
            $('#houseModalTitle').text('添加房源');

            // 重置全局selectedDeviceIds
            selectedDeviceIds = [];

            // 加载设备选择器
            renderDeviceSelector([]);

            currentHouseIndex = -1;
            $('#houseModal').modal('show');
        }

        // 显示编辑房源模态框
        function showEditHouseModal(index) {
            let house = houses[index];

            // 填充表单
            $('#house-name').val(house.name);
            $('#house-enabled').prop('checked', house.enabled !== false);

            // 重置全局selectedDeviceIds，确保每次编辑房源时都重新初始化
            selectedDeviceIds = [];

            // 加载设备选择器
            renderDeviceSelector(house.device_ids || []);

            currentHouseIndex = index;
            $('#houseModalTitle').text('编辑房源');
            $('#houseModal').modal('show');
        }

        // 渲染设备选择器
        function renderDeviceSelector(initialSelectedDeviceIds) {
            $('#device-selector').html('<p class="text-muted">加载设备列表中...</p>');

            // 初始化已选设备ID（只在首次加载时）
            if (selectedDeviceIds.length === 0 && initialSelectedDeviceIds && initialSelectedDeviceIds.length > 0) {
                selectedDeviceIds = [...initialSelectedDeviceIds];
            }

            // 初始化设备过滤器（如果没有初始化过）
            if (typeof(houseDeviceFilters) === 'undefined') {
                houseDeviceFilters = {
                    search: '',
                    type: '',
                    sort: 'created_desc'
                };
            }

            // 直接获取所有设备，不依赖分页
            $.ajax({
                url: '/api/devices?per_page=1000', // 使用大的per_page值获取所有设备
                method: 'GET',
                success: function(response) {
                    houseAllDevices = response.devices || [];

                    // 添加设备选择器的筛选器UI（如果还没有添加）
                    if ($('#house-device-search').length === 0) {
                        const filterHtml = `
                        <div class="row mb-2">
                            <div class="col-md-6">
                                <input type="text" class="form-control form-control-sm" id="house-device-search"
                                       placeholder="搜索设备名称...">
                            </div>
                            <div class="col-md-3">
                                <select class="form-control form-control-sm" id="house-device-type">
                                    <option value="">所有类型</option>
                                    <option value="bark">Bark</option>
                                    <option value="wxpush">微信推送</option>
                                    <option value="pushme">PushMe</option>
                                </select>
                            </div>
                            <div class="col-md-3">
                                <select class="form-control form-control-sm" id="house-device-sort">
                                    <option value="created_desc" selected>最新添加</option>
                                    <option value="name">按名称排序</option>
                                    <option value="expire_asc">过期时间升序</option>
                                    <option value="type">按类型排序</option>
                                </select>
                            </div>
                        </div>
                        <div class="mb-2">
                            <button type="button" class="btn btn-sm btn-outline-primary" id="house-select-all">全选</button>
                            <button type="button" class="btn btn-sm btn-outline-secondary ml-2" id="house-deselect-all">取消全选</button>
                        </div>`;

                        // 在设备选择器前插入筛选UI
                        $('#device-selector').before(filterHtml);

                        // 绑定筛选事件
                        bindHouseDeviceFilterEvents();
                    }

                    renderFilteredDeviceSelector();
                },
                error: function(xhr) {
                    $('#device-selector').html('<p class="text-danger">加载设备列表失败</p>');
                }
            });
        }

        // 绑定房源设备筛选器事件
        function bindHouseDeviceFilterEvents() {
            // 添加防抖功能，避免频繁筛选
            let searchTimeout;

            $('#house-device-search').off('input').on('input', function() {
                clearTimeout(searchTimeout);
                searchTimeout = setTimeout(() => {
                    houseDeviceFilters.search = $(this).val();
                    renderFilteredDeviceSelector();
                }, 300); // 300ms防抖
            });

            $('#house-device-type').off('change').on('change', function() {
                houseDeviceFilters.type = $(this).val();
                renderFilteredDeviceSelector();
            });

            $('#house-device-sort').off('change').on('change', function() {
                houseDeviceFilters.sort = $(this).val();
                renderFilteredDeviceSelector();
            });

            $('#house-select-all').off('click').on('click', function() {
                // 将所有可见且未禁用的设备添加到selectedDeviceIds中
                $('.device-checkbox:not(:disabled)').each(function() {
                    const deviceId = $(this).val();
                    if (!selectedDeviceIds.includes(deviceId)) {
                        selectedDeviceIds.push(deviceId);
                    }
                });
                // 更新UI
                $('.device-checkbox:not(:disabled)').prop('checked', true);
            });

            $('#house-deselect-all').off('click').on('click', function() {
                // 从selectedDeviceIds中移除所有可见的设备
                $('.device-checkbox').each(function() {
                    const deviceId = $(this).val();
                    const index = selectedDeviceIds.indexOf(deviceId);
                    if (index > -1) {
                        selectedDeviceIds.splice(index, 1);
                    }
                });
                // 更新UI
                $('.device-checkbox').prop('checked', false);
            });
        }

        // 渲染经过筛选的设备选择器
        function renderFilteredDeviceSelector() {
            let filteredDevices = filterAndSortDevices(houseAllDevices || [], houseDeviceFilters || {
                search: '',
                type: '',
                sort: 'created_desc'
            });
            let html = '';

            if (filteredDevices.length === 0) {
                html = '<p class="text-muted">没有找到匹配的设备</p>';
            } else {
                // 按设备类型分组
                let barkDevices = filteredDevices.filter(d => d.type === 'bark');
                let wxDevices = filteredDevices.filter(d => d.type === 'wxpush');
                let pushmeDevices = filteredDevices.filter(d => d.type === 'pushme');

                // Bark设备区块
                if (barkDevices.length > 0) {
                    html += '<h6 class="mt-2 mb-1 text-primary">Bark设备</h6>';
                    for (let device of barkDevices) {
                        let isSelected = selectedDeviceIds.includes(device.id);
                        let expireInfo = getDeviceExpireInfo(device);
                        html += `<div class="custom-control custom-checkbox mb-2">
                            <input type="checkbox" class="custom-control-input device-checkbox"
                                id="device-${device.id}"
                                value="${device.id}"
                                data-device-name="${device.name}"
                                data-device-type="${device.type}"
                                ${isSelected ? 'checked' : ''}
                                ${expireInfo.disabled ? 'disabled' : ''}>
                            <label class="custom-control-label ${expireInfo.labelClass}" for="device-${device.id}">
                                ${device.name} (Bark) ${expireInfo.badge}
                            </label>
                        </div>`;
                    }
                }

                // 微信推送设备区块
                if (wxDevices.length > 0) {
                    html += '<h6 class="mt-3 mb-1 text-primary">微信推送设备</h6>';
                    for (let device of wxDevices) {
                        let isSelected = selectedDeviceIds.includes(device.id);
                        let expireInfo = getDeviceExpireInfo(device);
                        html += `<div class="custom-control custom-checkbox mb-2">
                            <input type="checkbox" class="custom-control-input device-checkbox"
                                id="device-${device.id}"
                                value="${device.id}"
                                data-device-name="${device.name}"
                                data-device-type="${device.type}"
                                ${isSelected ? 'checked' : ''}
                                ${expireInfo.disabled ? 'disabled' : ''}>
                            <label class="custom-control-label ${expireInfo.labelClass}" for="device-${device.id}">
                                ${device.name} (微信推送) ${expireInfo.badge}
                            </label>
                        </div>`;
                    }
                }

                // PushMe设备区块
                if (pushmeDevices.length > 0) {
                    html += '<h6 class="mt-3 mb-1 text-primary">PushMe设备</h6>';
                    for (let device of pushmeDevices) {
                        let isSelected = selectedDeviceIds.includes(device.id);
                        let expireInfo = getDeviceExpireInfo(device);
                        html += `<div class="custom-control custom-checkbox mb-2">
                            <input type="checkbox" class="custom-control-input device-checkbox"
                                id="device-${device.id}"
                                value="${device.id}"
                                data-device-name="${device.name}"
                                data-device-type="${device.type}"
                                ${isSelected ? 'checked' : ''}
                                ${expireInfo.disabled ? 'disabled' : ''}>
                            <label class="custom-control-label ${expireInfo.labelClass}" for="device-${device.id}">
                                ${device.name} (PushMe) ${expireInfo.badge}
                            </label>
                        </div>`;
                    }
                }

                // 如果所有类型都没有设备，显示提示信息
                if (barkDevices.length === 0 && wxDevices.length === 0 && pushmeDevices.length === 0) {
                    html = '<p class="text-muted">没有找到匹配的设备</p>';
                }
            }

            $('#device-selector').html(html);

            // 添加设备勾选事件监听
            $('.device-checkbox').off('change').on('change', function() {
                const deviceId = $(this).val();
                if ($(this).is(':checked')) {
                    // 添加到已选设备
                    if (!selectedDeviceIds.includes(deviceId)) {
                        selectedDeviceIds.push(deviceId);
                    }
                } else {
                    // 从已选设备移除
                    const index = selectedDeviceIds.indexOf(deviceId);
                    if (index > -1) {
                        selectedDeviceIds.splice(index, 1);
                    }
                }
            });
        }

        // 筛选和排序设备
        function filterAndSortDevices(devices, filters) {
            let filtered = devices.slice();

            // 搜索筛选
            if (filters.search) {
                let searchTerm = filters.search.toLowerCase();
                filtered = filtered.filter(device =>
                    device.name.toLowerCase().includes(searchTerm) ||
                    (device.id && device.id.toLowerCase().includes(searchTerm))
                );
            }

            // 类型筛选
            if (filters.type) {
                filtered = filtered.filter(device => device.type === filters.type);
            }

            // 排序
            filtered.sort((a, b) => {
                switch (filters.sort) {
                    case 'name':
                        return a.name.localeCompare(b.name);
                    case 'type':
                        return a.type.localeCompare(b.type);
                    case 'created_desc':
                        return new Date(b.created_at || 0) - new Date(a.created_at || 0);
                    case 'expire_asc':
                        // 将过期日期放在前面，无过期日期的放在最后
                        if (!a.expire_date && !b.expire_date) return 0;
                        if (!a.expire_date) return 1;
                        if (!b.expire_date) return -1;
                        return new Date(a.expire_date) - new Date(b.expire_date);
                    default:
                        return 0;
                }
            });

            return filtered;
        }

        // 获取设备过期信息用于显示
        function getDeviceExpireInfo(device) {
            let info = {
                disabled: false,
                labelClass: '',
                badge: ''
            };

            // 使用后端提供的过期状态
            const expireStatus = device.expire_status || 'normal';

            if (expireStatus === 'expired') {
                info.disabled = true;
                info.labelClass = 'text-muted';
                info.badge = '<small class="text-danger">(已过期)</small>';
            } else if (expireStatus === 'expiring') {
                info.labelClass = 'text-warning';
                let daysText = device.expire_days_left !== null ? `剩余${device.expire_days_left}天` : '即将过期';
                info.badge = `<small class="text-warning">(${daysText})</small>`;
            }

            return info;
        }

        // 保存房源
        function saveHouse() {
            const houseName = $('#house-name').val().trim();
            if (!houseName) {
                alert('请输入房源名称');
                return;
            }

            const isNewHouse = currentHouseIndex < 0;

            // 检查房源名称唯一性（仅在添加新房源时）
            if (isNewHouse) {
                const nameExists = houses.some(house => house.name === houseName);
                if (nameExists) {
                    alert('房源名称已存在，请使用其他名称');
                    return;
                }
            }

            // 使用全局变量中的已选设备ID，而不是从DOM中收集
            const enabled = $('#house-enabled').is(':checked');

            let houseData = {
                name: houseName,
                enabled: enabled,
                device_ids: selectedDeviceIds
            };

            toggleLoading(true);

            let ajaxUrl, ajaxMethod;
            if (isNewHouse) {
                // 添加新房源
                ajaxUrl = '/api/houses';
                ajaxMethod = 'POST';
            } else {
                // 更新现有房源
                const originalName = houses[currentHouseIndex].name;
                ajaxUrl = '/api/houses/' + encodeURIComponent(originalName);
                ajaxMethod = 'PUT';
            }

            $.ajax({
                url: ajaxUrl,
                method: ajaxMethod,
                contentType: 'application/json',
                data: JSON.stringify(houseData),
                success: function(result) {
                    // 使用后端返回的最新数据更新前端
                    const updatedHouse = result.house;

                    // 添加调试信息
                    console.log('保存房源成功，后端返回数据:', updatedHouse);
                    console.log('设备ID列表:', updatedHouse.device_ids);
                    console.log('设备数量:', updatedHouse.device_ids ? updatedHouse.device_ids.length : 0);

                    // 确保device_ids是数组
                    if (updatedHouse.device_ids === undefined) {
                        console.warn('警告: 后端返回的房源数据中没有device_ids字段');
                        updatedHouse.device_ids = [];
                    } else if (!Array.isArray(updatedHouse.device_ids)) {
                        console.warn('警告: 后端返回的device_ids不是数组:', updatedHouse.device_ids);
                        updatedHouse.device_ids = [];
                    }

                    // 隐藏模态框
                    $('#houseModal').modal('hide');

                    // 重置已选设备ID
                    selectedDeviceIds = [];

                    // 重新加载配置，以获取最新的房源列表
                    loadConfig();
                },
                error: function(xhr) {
                    alert('保存房源失败: ' + (xhr.responseJSON?.error || xhr.statusText));
                    toggleLoading(false);
                }
            });
        }

        // 删除房源
        function deleteHouse(index) {
            const house = houses[index];
            if (!house) {
                alert('找不到要删除的房源');
                return;
            }

            if (!confirm(`确定要彻底删除房源 "${house.name}" 吗？\n此操作将删除所有相关数据且不可恢复。`)) {
                return;
            }

            toggleLoading(true);

            $.ajax({
                url: '/api/houses/' + encodeURIComponent(house.name),
                method: 'DELETE',
                success: function(response) {
                    toggleLoading(false);
                    alert(response.message || `房源 "${house.name}" 已成功删除`);
                    // 重新加载所有配置和列表，以确保UI完全同步
                    loadConfig();
                },
                error: function(xhr) {
                    toggleLoading(false);
                    alert('删除房源失败: ' + (xhr.responseJSON?.error || xhr.statusText));
                }
            });
        }

        // 保存系统设置
        function saveSettings(e) {
            e.preventDefault();

            // 获取表单数据
            let baseUrl = $('#base_url').val();
            let checkInterval = parseInt($('#check_interval').val());
            let maxRetries = parseInt($('#max_retries').val());
            let proxyApiUrl = $('#proxy_api_url').val();

            if (!baseUrl || !checkInterval || !maxRetries || !proxyApiUrl) {
                alert('请填写完整的系统设置');
                return;
            }

            if (checkInterval < 0) {
                alert('检查间隔不能小于0秒');
                return;
            }

            // 更新配置
            config.base_url = baseUrl;
            config.check_interval = checkInterval;
            config.max_retries = maxRetries;
            config.proxy_api_url = proxyApiUrl;

            toggleLoading(true);

            $.ajax({
                url: '/api/config',
                        method: 'POST',
                contentType: 'application/json',
                data: JSON.stringify(config),
                success: function() {
                    alert('系统设置已保存');
                    toggleLoading(false);
                },
                error: function(xhr) {
                    alert('保存系统设置失败: ' + (xhr.responseJSON?.error || xhr.statusText));
                    toggleLoading(false);
                }
            });
        }

        // 显示定时任务设置
        function showScheduleSettings() {
            $('#schedule-settings-tab').tab('show');
            // 确保配置已加载后再填充表单
            setTimeout(function() {
                loadScheduleSettings();
                updateScheduleStatus();
            }, 100);
        }

        // 加载定时任务设置
        function loadScheduleSettings() {
            // 确保全局配置已加载
            if (!config || !config.schedule) {
                console.log('全局配置未加载，等待配置加载完成...');
                // 如果配置尚未加载，延迟执行
                setTimeout(loadScheduleSettings, 200);
                return;
            }

            // 定时任务配置已在 loadConfig() 中加载到全局 config 对象
            const tasks = config.schedule && config.schedule.tasks ? config.schedule.tasks : {};
            const taskConfig = tasks.monitor_task || {
                enabled: false,
                start_time: "09:00",
                stop_time: "21:00",
                days: [0, 1, 2, 3, 4] // 默认周一到周五
            };

            console.log('正在加载定时任务配置:', taskConfig);

            // 填充表单
            $('#schedule-enabled').prop('checked', taskConfig.enabled);
            $('#start-time').val(taskConfig.start_time);
            $('#stop-time').val(taskConfig.stop_time);

            // 设置星期选择
            $('input[id^="day-"]').prop('checked', false);
            if (taskConfig.days) {
                for (let day of taskConfig.days) {
                    $(`#day-${day}`).prop('checked', true);
                }
            }

            console.log('定时任务表单已填充完成');
        }

        // 保存定时任务设置
        function saveScheduleSettings() {
            // 获取表单数据
            const enabled = $('#schedule-enabled').is(':checked');
            const startTime = $('#start-time').val();
            const stopTime = $('#stop-time').val();

            // 获取选中的星期
            const days = [];
            $('input[id^="day-"]:checked').each(function() {
                days.push(parseInt($(this).val()));
            });

            if (!startTime || !stopTime) {
                alert('请设置开始和结束时间');
                return;
            }

            // 构建请求数据
            const scheduleData = {
                tasks: {
                    monitor_task: {
                        enabled: enabled,
                        start_time: startTime,
                        stop_time: stopTime,
                        days: days
                    }
                }
            };

            toggleLoading(true);

            $.ajax({
                url: '/api/schedule/update',
                method: 'POST',
                contentType: 'application/json',
                data: JSON.stringify(scheduleData),
                success: function() {
                    toggleLoading(false);
                    alert('定时任务设置已保存');

                    // 重新加载全局配置以同步最新数据
                    loadConfig();

                    // 更新状态显示
                    updateScheduleStatus();
                },
                error: function(xhr) {
                    toggleLoading(false);
                    alert('保存定时任务设置失败: ' + (xhr.responseJSON?.error || xhr.statusText));
                }
            });
        }

        // 更新定时任务状态的函数
        function updateScheduleStatus() {
            fetch('/api/schedule/status')
                .then(response => response.json())
                .then(data => {
                    if (data.error) {
                        console.error('获取定时任务状态失败:', data.error);
                        return;
                    }

                    const statusText = document.getElementById('scheduleStatusText');
                    const statusIndicator = document.getElementById('statusIndicator');
                    const nextStartTime = document.getElementById('nextStartTime');
                    const nextStopTime = document.getElementById('nextStopTime');
                    const scheduleDays = document.getElementById('scheduleDays');
                    const scheduleTimeRange = document.getElementById('scheduleTimeRange');
                    const remainingTimeContainer = document.getElementById('remainingTimeContainer');
                    const remainingTime = document.getElementById('remainingTime');

                    // Helper to format ISO date string to local time
                    const formatToLocalDateTime = (isoString) => {
                        if (!isoString) return '-';
                        try {
                            const date = new Date(isoString);
                            // 格式化为 'YYYY-MM-DD HH:mm:ss'
                            return date.toLocaleString('zh-CN', { year: 'numeric', month: '2-digit', day: '2-digit', hour: '2-digit', minute: '2-digit', second: '2-digit', hour12: false }).replace(/\//g, '-');
                        } catch (e) {
                            return isoString; // Fallback
                        }
                    };


                    // 更新状态指示器和文本
                    statusIndicator.className = 'status-indicator';
                    if (!data.enabled) {
                        statusText.textContent = '未启用';
                        statusIndicator.classList.add('disabled');
                        remainingTimeContainer.style.display = 'none';
                        // 未启用时清空所有信息
                        nextStartTime.textContent = '-';
                        nextStopTime.textContent = '-';
                        scheduleDays.textContent = '未设置';
                        scheduleTimeRange.textContent = '未设置';
                        return;
                    } else if (data.is_running_period) {
                        statusText.textContent = '运行中';
                        statusIndicator.classList.add('running');
                        // 显示剩余时间
                        if (data.remaining_time) {
                            remainingTime.textContent = data.remaining_time;
                            remainingTimeContainer.style.display = 'block';
                        } else {
                            remainingTimeContainer.style.display = 'none';
                        }
                    } else {
                        statusText.textContent = '已停止';
                        statusIndicator.classList.add('stopped');
                        remainingTimeContainer.style.display = 'none';
                    }

                    // 更新下次启动和停止时间
                    nextStartTime.textContent = formatToLocalDateTime(data.next_start) || '-';
                    nextStopTime.textContent = formatToLocalDateTime(data.next_stop) || '-';

                    // 更新执行日期
                    if (data.days && data.days.length > 0) {
                        const dayNames = ['周一', '周二', '周三', '周四', '周五', '周六', '周日'];
                        // 按数字排序，确保星期顺序正确
                        const sortedDays = data.days.sort((a, b) => a - b);
                        const selectedDays = sortedDays.map(day => dayNames[day]).join('、');
                        scheduleDays.textContent = selectedDays;
                    } else {
                        scheduleDays.textContent = '未设置';
                    }

                    // 更新执行时段
                    if (data.start_time && data.stop_time) {
                        scheduleTimeRange.textContent = `${data.start_time} - ${data.stop_time}`;
                    } else {
                        scheduleTimeRange.textContent = '未设置';
                    }
                })
                .catch(error => {
                    console.error('获取定时任务状态失败:', error);
                });
        }

        // 页面加载完成后立即更新状态
        document.addEventListener('DOMContentLoaded', function() {
            updateScheduleStatus();
            // 每10秒更新一次状态（更频繁的更新以显示剩余时间）
            setInterval(updateScheduleStatus, 10000);
        });

        // 在保存定时任务设置后更新状态
        function saveSchedule() {
            // ... existing code ...
            fetch('/api/schedule/update', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify(scheduleData)
            })
            .then(response => response.json())
            .then(data => {
                if (data.error) {
                    showToast('保存失败', data.error, 'error');
                } else {
                    showToast('保存成功', '定时任务设置已更新', 'success');
                    // 保存成功后更新状态
                    updateScheduleStatus();
                }
            })
            .catch(error => {
                console.error('保存失败:', error);
                showToast('保存失败', '网络错误，请重试', 'error');
            });
        }

        // 显示设备使用情况模态框
        function showDeviceHousesModal(deviceId) {
            toggleLoading(true);

            // 清空之前的内容
            $('#device-houses-list').html('<p class="text-muted">加载中...</p>');

            // 获取设备使用情况
            $.ajax({
                url: `/api/devices/${deviceId}/houses`,
                method: 'GET',
                success: function(response) {
                    toggleLoading(false);

                    // 更新设备信息
                    const device = response.device;
                    $('#device-name-display').text(device.name);
                    $('#device-type-badge').text(device.type).addClass(`badge-${device.type}`);

                    // 清空容器
                    $('#device-houses-list').empty();

                    // 添加统计信息
                    if (response.houses.length === 0) {
                        $('#device-houses-list').html('<div class="alert alert-info">该设备未被任何房源使用</div>');
                    } else {
                        const houseCount = response.houses.length;
                        $('#device-houses-list').append(`
                            <div class="alert alert-success mb-3">
                                <i class="fa fa-info-circle"></i> 该设备被 ${houseCount} 个房源使用
                            </div>
                        `);
                    }

                    // 存储原始状态用于比较
                    const originalHouseStates = {};

                    // 添加房源列表
                    let listHtml = '';
                    for (const house of response.houses) {
                        originalHouseStates[house.name] = house.enabled;

                        listHtml += `
                            <div class="house-item d-flex justify-content-between align-items-center border-bottom py-2">
                                <div class="house-name">${house.name}</div>
                                <div class="custom-control custom-switch">
                                    <input type="checkbox" class="custom-control-input house-switch"
                                        id="house-${house.name.replace(/\s+/g, '-')}"
                                        data-house="${house.name}"
                                        data-device="${deviceId}"
                                        ${house.enabled ? 'checked' : ''}>
                                    <label class="custom-control-label" for="house-${house.name.replace(/\s+/g, '-')}">已启用</label>
                                </div>
                            </div>
                        `;
                    }

                    $('#device-houses-list').append(listHtml);
                    $('#deviceHousesModal').data('device-id', deviceId);
                    $('#deviceHousesModal').data('original-states', originalHouseStates);
                    $('#deviceHousesModal').modal('show');

                    // 为开关添加事件
                    $('.house-switch').change(function() {
                        const houseName = $(this).data('house');
                        const deviceId = $(this).data('device');
                        const isEnabled = $(this).prop('checked');

                        // 在这里我们不直接发送请求，而是在保存按钮点击时处理
                        console.log(`房源 ${houseName} 使用设备 ${deviceId} 的状态改为: ${isEnabled ? '启用' : '禁用'}`);
                    });
                },
                error: function(xhr) {
                    toggleLoading(false);
                    alert('获取设备使用情况失败: ' + (xhr.responseJSON?.error || xhr.statusText));
                }
            });
        }

        // 保存设备使用的房源状态更改
        $('#save-device-houses-btn').click(function() {
            toggleLoading(true);

            const deviceId = $('#deviceHousesModal').data('device-id');
            const originalStates = $('#deviceHousesModal').data('original-states');
            const promises = [];

            // 获取所有开关的当前状态
            $('.house-switch').each(function() {
                const houseName = $(this).data('house');
                const isEnabled = $(this).prop('checked');
                const wasEnabled = originalStates[houseName];

                // 如果状态变化了，需要发送请求
                if (isEnabled !== wasEnabled) {
                    let promise;

                    if (isEnabled) {
                        // 向房源添加设备
                        promise = $.ajax({
                            url: `/api/houses/${houseName}/devices/${deviceId}`,
                            method: 'PUT',
                            contentType: 'application/json'
                        });
                    } else {
                        // 从房源移除设备
                        promise = $.ajax({
                            url: `/api/houses/${houseName}/devices/${deviceId}`,
                            method: 'DELETE'
                        });
                    }

                    promises.push(promise);
                }
            });

            // 等待所有请求完成
            if (promises.length > 0) {
                Promise.all(promises)
                    .then(() => {
                        toggleLoading(false);
                        $('#deviceHousesModal').modal('hide');
                        alert('设备使用情况已更新');

                        // 重新加载设备列表和房源列表
                        loadConfig();
                    })
                    .catch(error => {
                        toggleLoading(false);
                        alert('更新设备使用情况失败: ' + (error.responseJSON?.error || error.statusText));
                    });
            } else {
                // 没有变化
                toggleLoading(false);
                $('#deviceHousesModal').modal('hide');
            }
        });

        // 页面加载完成后初始化
        $(document).ready(function() {
            // 调用初始化函数
            initPage();

            // 这些绑定已经在initPage中完成，不需要重复
            // $('#start-btn').click(startMonitor);
            // $('#stop-btn').click(stopMonitor);
            // $('#add-house-btn').click(showAddHouseModal);
            // $('#save-house-btn').click(saveHouse);
            // $('#add-device-btn').click(showAddDeviceModal);
            // $('#save-device-btn').click(saveDevice);
            // $('#test-device-btn').click(testDevice);
            // $('#settings-form').submit(saveSettings);
            // $('#save-schedule-btn').click(saveScheduleSettings);

            // 绑定设备类型切换事件 - 这个在initPage中没有，需要保留
            $('#schedule-form').submit(function(e){
                    e.preventDefault();
                    saveScheduleSettings();
            });
            $('#device-type').on('change', function() {
                let type = $(this).val();
                if (type === 'bark') {
                    $('#bark-fields').show();
                    $('#wxpush-fields').hide();
                    $('#pushme-fields').hide();
                } else if (type === 'wxpush') {
                    $('#bark-fields').hide();
                    $('#wxpush-fields').show();
                    $('#pushme-fields').hide();
                } else if (type === 'pushme') {
                    $('#bark-fields').hide();
                    $('#wxpush-fields').hide();
                    $('#pushme-fields').show();
                }
            });

            // 初始化自定义推送相关功能
            $('#custom-push-tab').click(renderPushDeviceSelector);
            $('#custom-push-form').submit(function(e) {
                e.preventDefault();
                sendCustomPush();
            });

            // 初始化定时任务选项卡，确保切换时重新加载最新配置
            $('#schedule-settings-tab').on('show.bs.tab', function() {
                // 稍微延迟加载，确保选项卡已切换
                setTimeout(function() {
                    console.log('切换到定时任务选项卡，重新加载配置');

                    // 重新加载配置以确保显示最新数据
                    $.ajax({
                        url: '/api/config',
                        method: 'GET',
                        success: function(data) {
                            console.log('重新加载配置成功', data.schedule);
                            // 更新全局配置
                            config = data;
                            devices = config.device_list || [];
                            houses = config.monitor_configs || [];

                            // 加载定时任务设置
                            loadScheduleSettings();

                            // 更新状态显示
                            updateScheduleStatus();
                        },
                        error: function(xhr) {
                            console.error('重新加载配置失败:', xhr.responseJSON?.error || xhr.statusText);
                            // 即使配置加载失败，也尝试更新状态显示
                            updateScheduleStatus();
                        }
                    });
                }, 100);
            });

            // 初始化抢房设备相关功能
            $('#grab-devices-tab').click(function() {
                loadGrabDeviceList();
                checkAutoLoginStatus(); // 加载抢房设备时同时检查自动登录状态
            });

            // 自动登录管理器控制按钮
            $('#check-auto-login-status-btn').click(checkAutoLoginStatus);
            $('#start-auto-login-btn').click(startAutoLoginManager);
            $('#stop-auto-login-btn').click(stopAutoLoginManager);

            // 抢房设备筛选和搜索事件监听
            $('#grab-device-search').on('input', function() {
                clearTimeout(window.grabSearchTimeout);
                window.grabSearchTimeout = setTimeout(function() {
                    grabDevicePagination.currentSearch = $('#grab-device-search').val();
                    grabDevicePagination.currentPage = 1;
                    loadGrabDeviceList();
                }, 500);
            });

            $('#clear-grab-search').click(function() {
                $('#grab-device-search').val('');
                grabDevicePagination.currentSearch = '';
                grabDevicePagination.currentPage = 1;
                loadGrabDeviceList();
            });

            $('#grab-device-enabled-filter').change(function() {
                grabDevicePagination.enabledFilter = $(this).val();
                grabDevicePagination.currentPage = 1;
                loadGrabDeviceList();
            });

            $('#grab-devices-per-page').change(function() {
                grabDevicePagination.perPage = parseInt($(this).val());
                grabDevicePagination.currentPage = 1;
                loadGrabDeviceList();
            });

            // 预上传缓存相关事件监听
            $('#refresh-preupload-cache-btn').click(function() {
                const modalTitle = $('#preuploadCacheModalTitle').text();
                const username = modalTitle.split(' - ')[1];
                if (username) {
                    // 从当前显示的设备中找到对应的设备ID
                    const device = grabDevices.find(d => d.username === username);
                    if (device) {
                        refreshPreuploadCache(device.id, username);
                    }
                }
            });

            // 这些加载已经在initPage中完成，不需要重复
            // loadConfig();
            // loadStatus();
            // loadHouseCounts();

            // 定时更新在initPage中已经设置，这里不需要重复
            // setInterval(loadStatus, 5000);
            // setInterval(loadHouseCounts, 10000);

            // 批量设置过期时间按钮事件
            $('#batch-expire-btn').click(showBatchExpireModal);

            // 一键检测过期设备按钮事件
            $('#check-device-expiry-btn').click(checkDeviceExpiry);

            // 一键检测关联关系按钮事件
            $('#check-device-relations-btn').click(checkDeviceRelations);

            // 设备筛选和搜索事件监听
            $('#device-type-filter').change(function() {
                devicePagination.currentType = $(this).val();
                devicePagination.currentPage = 1; // 重置到第一页
                loadDeviceList();
            });

            $('#device-search').on('input', function() {
                clearTimeout(window.searchTimeout);
                window.searchTimeout = setTimeout(function() {
                    devicePagination.currentSearch = $('#device-search').val();
                    devicePagination.currentPage = 1; // 重置到第一页
                    loadDeviceList();
                }, 500); // 500ms 延迟搜索
            });

            $('#clear-search').click(function() {
                $('#device-search').val('');
                devicePagination.currentSearch = '';
                devicePagination.currentPage = 1;
                loadDeviceList();
            });

            $('#device-sort-filter').change(function() {
                devicePagination.currentSort = $(this).val();
                devicePagination.currentPage = 1; // 重置到第一页
                loadDeviceList();
            });

            $('#devices-per-page').change(function() {
                devicePagination.perPage = parseInt($(this).val());
                devicePagination.currentPage = 1; // 重置到第一页
                loadDeviceList();
            });

            // 定时检查等待短信验证码的设备（每30秒检查一次）
            setInterval(checkPendingSmsDevices, 30000);
        });

        // ===========================================
        // 自动登录管理器相关功能
        // ===========================================

        // 检查自动登录管理器状态
        function checkAutoLoginStatus() {
            $.ajax({
                url: '/api/grab_devices/auto_login/status',
                method: 'GET',
                success: function(response) {
                    updateAutoLoginStatusDisplay(response);

                    // 同时检查等待短信验证码的设备
                    checkPendingSmsDevices();
                },
                error: function(xhr) {
                    $('#auto-login-status').show();
                    $('#auto-login-manager-status').removeClass('badge-success badge-danger').addClass('badge-danger').text('检查失败');
                    console.error('检查自动登录状态失败:', xhr.responseJSON?.error || xhr.statusText);
                }
            });
        }

        // 更新自动登录状态显示
        function updateAutoLoginStatusDisplay(response) {
            $('#auto-login-status').show();

            if (response.status === 'not_created') {
                $('#auto-login-manager-status').removeClass('badge-success badge-danger').addClass('badge-warning').text('未创建');
                $('#start-auto-login-btn').hide();
                $('#stop-auto-login-btn').hide();
                $('#auto-login-details').hide();
            } else if (response.manager_status) {
                const managerStatus = response.manager_status;

                if (managerStatus.running) {
                    $('#auto-login-manager-status').removeClass('badge-warning badge-danger').addClass('badge-success').text('运行中');
                    $('#start-auto-login-btn').hide();
                    $('#stop-auto-login-btn').show();
                } else {
                    $('#auto-login-manager-status').removeClass('badge-success badge-danger').addClass('badge-warning').text('已停止');
                    $('#start-auto-login-btn').show();
                    $('#stop-auto-login-btn').hide();
                }

                // 显示详细信息
                $('#check-interval-text').text(`${managerStatus.check_interval_seconds}秒`);
                $('#cookie-threshold-text').text(`${managerStatus.cookie_threshold_minutes}分钟`);
                $('#pending-devices-count').text(managerStatus.pending_devices_count || 0);
                $('#auto-login-details').show();
            }
        }

        // 启动自动登录管理器
        function startAutoLoginManager() {
            const button = $('#start-auto-login-btn');
            button.prop('disabled', true).html('<i class="fa fa-spin fa-spinner"></i> 启动中...');

            $.ajax({
                url: '/api/grab_devices/auto_login/start',
                method: 'POST',
                success: function(response) {
                    if (response.status === 'success') {
                        updateAutoLoginStatusDisplay(response);
                        alert('自动Cookie延长管理器启动成功');
                    } else {
                        alert(response.message || '启动失败');
                    }
                },
                error: function(xhr) {
                    alert('启动自动登录管理器失败: ' + (xhr.responseJSON?.error || xhr.statusText));
                },
                complete: function() {
                    button.prop('disabled', false).html('<i class="fa fa-play"></i> 启动');
                }
            });
        }

        // 停止自动登录管理器
        function stopAutoLoginManager() {
            const button = $('#stop-auto-login-btn');
            button.prop('disabled', true).html('<i class="fa fa-spin fa-spinner"></i> 停止中...');

            $.ajax({
                url: '/api/grab_devices/auto_login/stop',
                method: 'POST',
                success: function(response) {
                    if (response.status === 'success') {
                        updateAutoLoginStatusDisplay(response);
                        alert('自动Cookie延长管理器已停止');
                    } else {
                        alert(response.message || '停止失败');
                    }
                },
                error: function(xhr) {
                    alert('停止自动登录管理器失败: ' + (xhr.responseJSON?.error || xhr.statusText));
                },
                complete: function() {
                    button.prop('disabled', false).html('<i class="fa fa-stop"></i> 停止');
                }
            });
        }

        // 检查等待短信验证码的设备
        function checkPendingSmsDevices() {
            $.ajax({
                url: '/api/grab_devices/auto_login/pending',
                method: 'GET',
                success: function(response) {
                    if (response.status === 'success') {
                        updatePendingSmsDevicesDisplay(response.pending_devices);
                    }
                },
                error: function(xhr) {
                    console.error('检查等待短信验证码设备失败:', xhr.responseJSON?.error || xhr.statusText);
                }
            });
        }

        // 更新等待短信验证码设备的显示
        function updatePendingSmsDevicesDisplay(pendingDevices) {
            const count = Object.keys(pendingDevices).length;

            if (count === 0) {
                $('#pending-sms-devices').hide();
                return;
            }

            $('#pending-sms-devices').show();

            let html = '';
            for (const [deviceId, info] of Object.entries(pendingDevices)) {
                const waitingTime = Math.floor(info.waiting_minutes || 0);
                const deviceName = info.device_name || `设备${deviceId}`;
                const devicePhone = info.device_phone || '未知';
                const retryCount = info.retry_count || 0;

                // 计算下次重试时间
                let nextRetryText = '';
                if (info.next_retry_time) {
                    const nextRetry = new Date(info.next_retry_time);
                    const now = new Date();
                    const minutesToRetry = Math.max(0, Math.floor((nextRetry - now) / 60000));
                    nextRetryText = minutesToRetry > 0 ? `${minutesToRetry}分钟后重试` : '即将重试';
                }

                html += `
                    <div class="card mb-2">
                        <div class="card-body py-2">
                            <div class="row align-items-center">
                                <div class="col-md-4">
                                    <strong>${deviceName}</strong>
                                    <br><small class="text-muted">手机: ${devicePhone}</small>
                                </div>
                                <div class="col-md-3">
                                    <small class="text-info">等待时间: ${waitingTime}分钟</small>
                                    <br><small class="text-muted">重试次数: ${retryCount}</small>
                                </div>
                                <div class="col-md-3">
                                    <small class="text-warning">${nextRetryText}</small>
                                    <br><small class="text-muted">状态: ${info.status === 'waiting_sms' ? '等待短信' : '自动重试中'}</small>
                                </div>
                                <div class="col-md-2">
                                    <a href="${info.sms_form_url}" target="_blank" class="btn btn-sm btn-primary">
                                        <i class="fa fa-mobile"></i> 填写验证码
                                    </a>
                                </div>
                            </div>
                        </div>
                    </div>
                `;
            }

            $('#pending-devices-list').html(html);
        }

        // 渲染推送设备选择器
        function renderPushDeviceSelector() {
            $('#push-device-selector').html('<p class="text-muted">加载设备列表中...</p>');

            // 初始化推送设备过滤器
            if (typeof(pushDeviceFilters) === 'undefined') {
                pushDeviceFilters = {
                    search: '',
                    type: '',
                    sort: 'created_desc'  // 默认按最新添加排序
                };
            }

            // 添加设备选择器的筛选器UI（如果还没有添加）
            if ($('#push-device-search').length === 0) {
                const filterHtml = `
                <div class="row mb-2">
                    <div class="col-md-6">
                        <input type="text" class="form-control form-control-sm" id="push-device-search"
                               placeholder="搜索设备名称...">
                    </div>
                    <div class="col-md-3">
                        <select class="form-control form-control-sm" id="push-device-type">
                            <option value="">所有类型</option>
                            <option value="bark">Bark</option>
                            <option value="wxpush">微信推送</option>
                            <option value="pushme">PushMe</option>
                        </select>
                    </div>
                    <div class="col-md-3">
                        <select class="form-control form-control-sm" id="push-device-sort">
                            <option value="created_desc" selected>最新添加</option>
                            <option value="expire_asc">过期时间升序</option>
                            <option value="type">按类型排序</option>
                        </select>
                    </div>
                </div>`;

                // 在设备选择器前插入筛选UI
                $('#push-device-selector').before(filterHtml);
            }

            // 直接获取所有设备，不依赖分页
            $.ajax({
                url: '/api/devices?per_page=1000', // 使用大的per_page值获取所有设备
                method: 'GET',
                success: function(response) {
                    pushAllDevices = response.devices || [];
                    renderFilteredPushDeviceSelector();
                    bindPushDeviceFilterEvents();
                },
                error: function(xhr) {
                    $('#push-device-selector').html('<p class="text-danger">加载设备列表失败</p>');
                }
            });
        }

        // 绑定推送设备筛选器事件
        function bindPushDeviceFilterEvents() {
            let searchTimeout;

            $('#push-device-search').off('input').on('input', function() {
                clearTimeout(searchTimeout);
                searchTimeout = setTimeout(() => {
                    pushDeviceFilters.search = $(this).val();
                    renderFilteredPushDeviceSelector();
                }, 300); // 300ms防抖
            });

            $('#push-device-type').off('change').on('change', function() {
                pushDeviceFilters.type = $(this).val();
                renderFilteredPushDeviceSelector();
            });

            $('#push-device-sort').off('change').on('change', function() {
                pushDeviceFilters.sort = $(this).val();
                renderFilteredPushDeviceSelector();
            });

            $('#push-select-all').off('click').on('click', function() {
                $('.push-device-checkbox:not(:disabled)').prop('checked', true);
            });

            $('#push-deselect-all').off('click').on('click', function() {
                $('.push-device-checkbox').prop('checked', false);
            });
        }

        // 渲染经过筛选的推送设备选择器
        function renderFilteredPushDeviceSelector() {
            let filteredDevices = filterAndSortDevices(pushAllDevices, pushDeviceFilters);
            let html = '';

            if (filteredDevices.length === 0) {
                html = '<p class="text-muted">没有找到匹配的设备</p>';
            } else {
                // 添加全选/取消全选按钮
                html += `
                <div class="mb-3">
                    <button type="button" class="btn btn-sm btn-outline-primary mr-2" id="push-select-all">全选</button>
                    <button type="button" class="btn btn-sm btn-outline-secondary" id="push-deselect-all">取消全选</button>
                </div>`;

                // 按设备类型分组
                let barkDevices = filteredDevices.filter(d => d.type === 'bark');
                let wxpushDevices = filteredDevices.filter(d => d.type === 'wxpush');
                let pushmeDevices = filteredDevices.filter(d => d.type === 'pushme');

                // Bark设备区块
                if (barkDevices.length > 0) {
                    html += '<h6 class="mt-2 mb-1 text-primary">Bark设备</h6>';
                    for (let device of barkDevices) {
                        let expireInfo = getDeviceExpireInfo(device);
                        html += `<div class="custom-control custom-checkbox mb-2">
                            <input type="checkbox" class="custom-control-input push-device-checkbox"
                                id="push-device-${device.id}"
                                value="${device.id}"
                                ${expireInfo.disabled ? 'disabled' : ''}
                                data-expired="${expireInfo.disabled}">
                            <label class="custom-control-label ${expireInfo.labelClass}" for="push-device-${device.id}">
                                ${device.name} (Bark) ${expireInfo.badge}
                            </label>
                        </div>`;
                    }
                }

                // 微信推送设备区块
                if (wxpushDevices.length > 0) {
                    html += '<h6 class="mt-3 mb-1 text-primary">微信推送设备</h6>';
                    for (let device of wxpushDevices) {
                        let expireInfo = getDeviceExpireInfo(device);
                        html += `<div class="custom-control custom-checkbox mb-2">
                            <input type="checkbox" class="custom-control-input push-device-checkbox"
                                id="push-device-${device.id}"
                                value="${device.id}"
                                ${expireInfo.disabled ? 'disabled' : ''}
                                data-expired="${expireInfo.disabled}">
                            <label class="custom-control-label ${expireInfo.labelClass}" for="push-device-${device.id}">
                                ${device.name} (微信推送) ${expireInfo.badge}
                            </label>
                        </div>`;
                    }
                }

                // PushMe设备区块
                if (pushmeDevices.length > 0) {
                    html += '<h6 class="mt-3 mb-1 text-primary">PushMe设备</h6>';
                    for (let device of pushmeDevices) {
                        let expireInfo = getDeviceExpireInfo(device);
                        html += `<div class="custom-control custom-checkbox mb-2">
                            <input type="checkbox" class="custom-control-input push-device-checkbox"
                                id="push-device-${device.id}"
                                value="${device.id}"
                                ${expireInfo.disabled ? 'disabled' : ''}
                                data-expired="${expireInfo.disabled}">
                            <label class="custom-control-label ${expireInfo.labelClass}" for="push-device-${device.id}">
                                ${device.name} (PushMe) ${expireInfo.badge}
                            </label>
                        </div>`;
                    }
                }

                // 如果所有类型都没有设备，显示提示信息
                if (barkDevices.length === 0 && wxpushDevices.length === 0 && pushmeDevices.length === 0) {
                    html = '<p class="text-danger">暂无设备可选</p>';
                }
            }

            $('#push-device-selector').html(html);

            // 绑定全选/取消全选按钮事件
            $('#push-select-all').off('click').on('click', function() {
                $('.push-device-checkbox:not(:disabled)').prop('checked', true);
            });

            $('#push-deselect-all').off('click').on('click', function() {
                $('.push-device-checkbox').prop('checked', false);
            });
        }

        // 发送自定义推送
        function sendCustomPush() {
            const title = $('#push-title').val();
            const content = $('#push-content').val();

            // 验证标题和内容
            if (!title || !content) {
                alert('请填写完整的推送标题和内容');
                return;
            }

            // 获取选中的设备ID
            const deviceIds = [];
            $('.push-device-checkbox:checked').each(function() {
                deviceIds.push($(this).val());
            });

            if (deviceIds.length === 0) {
                alert('请至少选择一个设备');
                return;
            }

            // 构建请求数据
            const pushData = {
                title: title,
                content: content,
                device_ids: deviceIds
            };

            // 显示加载中
            toggleLoading(true);

            // 发送请求
            $.ajax({
                url: '/api/custom_push',
                method: 'POST',
                contentType: 'application/json',
                data: JSON.stringify(pushData),
                success: function(response) {
                    toggleLoading(false);

                    // 显示推送结果
                    $('#push-success-count').text(response.success_count);
                    $('#push-fail-count').text(response.fail_count);

                    // 构建结果表格
                    let tableHtml = '';
                    response.results.forEach(result => {
                        let device = devices.find(d => d.id === result.device_id) || {name: result.device_id, type: '未知'};
                        let statusClass = result.status === 'success' ? 'text-success' : 'text-danger';
                        let statusIcon = result.status === 'success' ?
                            '<i class="fa fa-check-circle"></i> 成功' :
                            '<i class="fa fa-times-circle"></i> 失败';

                        tableHtml += `
                            <tr>
                                <td>${result.device_name || device.name}</td>
                                <td>${result.device_type || device.type}</td>
                                <td class="${statusClass}">${statusIcon}</td>
                                <td>${result.message || '-'}</td>
                            </tr>
                        `;
                    });

                    $('#push-result-table').html(tableHtml);
                    $('#pushResultModal').modal('show');
                },
                error: function(xhr) {
                    toggleLoading(false);
                    alert('发送推送失败: ' + (xhr.responseJSON?.error || xhr.statusText));
                }
            });
        }

        // 显示设备续费模态框
        function showRenewDeviceModal(index) {
            let device = devices[index];

            // 创建一个临时模态框用于续费
            let modalHtml = `
                <div class="modal fade" id="renewDeviceModal" tabindex="-1" role="dialog" aria-hidden="true">
                    <div class="modal-dialog" role="document">
                        <div class="modal-content">
                            <div class="modal-header">
                                <h5 class="modal-title">设备续费</h5>
                                <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                                    <span aria-hidden="true">&times;</span>
                                </button>
                            </div>
                            <div class="modal-body">
                                <p>设备名称: <strong>${device.name}</strong></p>
                                <p>当前过期时间: <strong>${device.expire_date || '未设置'}</strong></p>

                                <div class="form-group">
                                    <label for="renew-expire-date">新的过期时间</label>
                                    <input type="date" class="form-control" id="renew-expire-date" value="${device.expire_date || ''}">
                                </div>

                                <div class="form-group">
                                    <label>快速选择</label>
                                    <div class="btn-group d-flex">
                                        <button type="button" class="btn btn-outline-primary renew-btn" data-days="30">1个月</button>
                                        <button type="button" class="btn btn-outline-primary renew-btn" data-days="90">3个月</button>
                                        <button type="button" class="btn btn-outline-primary renew-btn" data-days="180">6个月</button>
                                        <button type="button" class="btn btn-outline-primary renew-btn" data-days="365">1年</button>
                                    </div>
                                </div>
                            </div>
                            <div class="modal-footer">
                                <button type="button" class="btn btn-secondary" data-dismiss="modal">取消</button>
                                <button type="button" class="btn btn-success" id="confirm-renew-btn">确认续费</button>
                            </div>
                        </div>
                    </div>
                </div>
            `;

            // 添加到页面
            $('body').append(modalHtml);

            // 显示模态框
            $('#renewDeviceModal').modal('show');

            // 快速选择按钮事件
            $('.renew-btn').click(function() {
                const days = parseInt($(this).data('days'));
                const today = new Date();

                // 如果设备已经有过期时间，从过期时间开始计算
                let startDate;
                if (device.expire_date) {
                    const expireDate = new Date(device.expire_date);
                    // 如果过期时间已经过去，则从今天开始计算
                    startDate = expireDate > today ? expireDate : today;
                } else {
                    startDate = today;
                }

                // 计算新的过期时间
                const newExpireDate = new Date(startDate);
                newExpireDate.setDate(newExpireDate.getDate() + days);

                // 格式化日期为YYYY-MM-DD
                const formattedDate = newExpireDate.toISOString().split('T')[0];
                $('#renew-expire-date').val(formattedDate);
            });

            // 确认续费按钮事件
            $('#confirm-renew-btn').click(function() {
                const newExpireDate = $('#renew-expire-date').val();

                if (!newExpireDate) {
                    alert('请选择新的过期时间');
                    return;
                }

                // 更新设备过期时间
                device.expire_date = newExpireDate;

                // 保存设备
                toggleLoading(true);

                $.ajax({
                    url: '/api/devices/' + device.id,
                    method: 'PUT',
                    contentType: 'application/json',
                    data: JSON.stringify(device),
                    success: function(result) {
                        // 更新设备列表中的设备
                        devices[index] = result.device;
                        loadDeviceList();

                        // 关闭并移除模态框
                        $('#renewDeviceModal').modal('hide');
                        $('#renewDeviceModal').on('hidden.bs.modal', function() {
                            $(this).remove();
                        });

                        toggleLoading(false);
                        alert('设备续费成功');
                    },
                    error: function(xhr) {
                        toggleLoading(false);
                        alert('设备续费失败: ' + (xhr.responseJSON?.error || xhr.statusText));
                    }
                });
            });

            // 模态框关闭时移除
            $('#renewDeviceModal').on('hidden.bs.modal', function() {
                $(this).remove();
            });
        }

        // 显示批量设置过期时间模态框
        function showBatchExpireModal() {
            // 重置表单
            $('#batch-expire-date').val('');

            // 渲染设备选择器
            renderBatchDeviceSelector();

            // 显示模态框
            $('#batchExpireModal').modal('show');

            // 快速选择按钮事件
            $('.batch-renew-btn').click(function() {
                const days = parseInt($(this).data('days'));
                const today = new Date();

                // 计算新的过期时间
                const newExpireDate = new Date(today);
                newExpireDate.setDate(newExpireDate.getDate() + days);

                // 格式化日期为YYYY-MM-DD
                const formattedDate = newExpireDate.toISOString().split('T')[0];
                $('#batch-expire-date').val(formattedDate);
            });

            // 全选/取消全选按钮事件
            $('#batch-select-all').click(function() {
                $('.batch-device-checkbox').prop('checked', true);
            });

            $('#batch-deselect-all').click(function() {
                $('.batch-device-checkbox').prop('checked', false);
            });

            // 仅选择即将过期按钮事件
            $('#batch-select-expiring').click(function() {
                $('.batch-device-checkbox').prop('checked', false);
                $('.batch-device-checkbox[data-expire-status="expiring"]').prop('checked', true);

                // 如果没有即将过期的设备，显示提示
                if ($('.batch-device-checkbox[data-expire-status="expiring"]:checked').length === 0) {
                    alert('当前没有即将过期的设备');
                }
            });

            // 仅选择已过期按钮事件
            $('#batch-select-expired').click(function() {
                $('.batch-device-checkbox').prop('checked', false);
                $('.batch-device-checkbox[data-expire-status="expired"]').prop('checked', true);

                // 如果没有已过期的设备，显示提示
                if ($('.batch-device-checkbox[data-expire-status="expired"]:checked').length === 0) {
                    alert('当前没有已过期的设备');
                }
            });

            // 确认按钮事件
            $('#confirm-batch-expire-btn').click(batchSetExpireDate);
        }

        // 获取设备过期状态（使用后端提供的状态）
        function getDeviceExpireStatus(device) {
            return device.expire_status || 'normal';
        }

        // 渲染批量设备选择器
        function renderBatchDeviceSelector() {
            $('#batch-device-selector').html('<p class="text-muted">加载设备列表中...</p>');

            // 直接获取所有设备，不依赖分页
            $.ajax({
                url: '/api/devices?per_page=1000', // 使用大的per_page值获取所有设备
                method: 'GET',
                success: function(response) {
                    let allDevices = response.devices;
                    let html = '';

                    if (allDevices.length === 0) {
                        html = '<p class="text-muted">暂无设备，请先添加设备</p>';
                    } else {
                        // 按类型分组设备
                        let barkDevices = allDevices.filter(d => d.type === 'bark');
                        let wxpushDevices = allDevices.filter(d => d.type === 'wxpush');
                        let pushmeDevices = allDevices.filter(d => d.type === 'pushme');

                        // Bark设备区块
                        if (barkDevices.length > 0) {
                            html += '<h6 class="mt-2 mb-1 text-primary">Bark设备</h6>';
                            for (let device of barkDevices) {
                                // 检查设备是否过期
                                let expireStatus = getDeviceExpireStatus(device);
                                let expireClass = '';
                                let expireLabel = '';

                                if (expireStatus === 'expired') {
                                    expireClass = 'text-danger';
                                    expireLabel = '<span class="badge badge-danger ml-2">已过期</span>';
                                } else if (expireStatus === 'expiring') {
                                    expireClass = 'text-warning';
                                    expireLabel = '<span class="badge badge-warning ml-2">即将过期</span>';
                                }

                                html += `<div class="custom-control custom-checkbox mb-2">
                                    <input type="checkbox" class="custom-control-input batch-device-checkbox"
                                        id="batch-device-${device.id}"
                                        value="${device.id}"
                                        data-expire-status="${expireStatus}">
                                    <label class="custom-control-label ${expireClass}" for="batch-device-${device.id}">
                                        ${device.name} ${expireLabel} ${device.expire_date ? `(当前过期时间: ${device.expire_date})` : '(无过期时间)'}
                                    </label>
                                </div>`;
                            }
                        }

                        // 微信推送设备区块
                        if (wxpushDevices.length > 0) {
                            html += '<h6 class="mt-3 mb-1 text-primary">微信推送设备</h6>';
                            for (let device of wxpushDevices) {
                                // 检查设备是否过期
                                let expireStatus = getDeviceExpireStatus(device);
                                let expireClass = '';
                                let expireLabel = '';

                                if (expireStatus === 'expired') {
                                    expireClass = 'text-danger';
                                    expireLabel = '<span class="badge badge-danger ml-2">已过期</span>';
                                } else if (expireStatus === 'expiring') {
                                    expireClass = 'text-warning';
                                    expireLabel = '<span class="badge badge-warning ml-2">即将过期</span>';
                                }

                                html += `<div class="custom-control custom-checkbox mb-2">
                                    <input type="checkbox" class="custom-control-input batch-device-checkbox"
                                        id="batch-device-${device.id}"
                                        value="${device.id}"
                                        data-expire-status="${expireStatus}">
                                    <label class="custom-control-label ${expireClass}" for="batch-device-${device.id}">
                                        ${device.name} ${expireLabel} ${device.expire_date ? `(当前过期时间: ${device.expire_date})` : '(无过期时间)'}
                                    </label>
                                </div>`;
                            }
                        }

                        // PushMe设备区块
                        if (pushmeDevices.length > 0) {
                            html += '<h6 class="mt-3 mb-1 text-primary">PushMe设备</h6>';
                            for (let device of pushmeDevices) {
                                // 检查设备是否过期
                                let expireStatus = getDeviceExpireStatus(device);
                                let expireClass = '';
                                let expireLabel = '';

                                if (expireStatus === 'expired') {
                                    expireClass = 'text-danger';
                                    expireLabel = '<span class="badge badge-danger ml-2">已过期</span>';
                                } else if (expireStatus === 'expiring') {
                                    expireClass = 'text-warning';
                                    expireLabel = '<span class="badge badge-warning ml-2">即将过期</span>';
                                }

                                html += `<div class="custom-control custom-checkbox mb-2">
                                    <input type="checkbox" class="custom-control-input batch-device-checkbox"
                                        id="batch-device-${device.id}"
                                        value="${device.id}"
                                        data-expire-status="${expireStatus}">
                                    <label class="custom-control-label ${expireClass}" for="batch-device-${device.id}">
                                        ${device.name} ${expireLabel} ${device.expire_date ? `(当前过期时间: ${device.expire_date})` : '(无过期时间)'}
                                    </label>
                                </div>`;
                            }
                        }
                    }

                    $('#batch-device-selector').html(html);
                },
                error: function(xhr) {
                    $('#batch-device-selector').html('<p class="text-danger">加载设备列表失败</p>');
                }
            });
        }

        // 批量设置过期时间
        function batchSetExpireDate() {
            const newExpireDate = $('#batch-expire-date').val();

            if (!newExpireDate) {
                alert('请选择过期时间');
                return;
            }

            // 获取选中的设备ID
            const selectedDeviceIds = [];
            $('.batch-device-checkbox:checked').each(function() {
                selectedDeviceIds.push($(this).val());
            });

            if (selectedDeviceIds.length === 0) {
                alert('请至少选择一个设备');
                return;
            }

            // 确认操作
            if (!confirm(`确定要为选中的 ${selectedDeviceIds.length} 个设备设置过期时间为 ${newExpireDate} 吗？`)) {
                return;
            }

            toggleLoading(true);

            // 使用优化的批量更新API，一次请求完成所有更新
            $.ajax({
                url: '/api/devices/batch_update_expire',
                method: 'POST',
                contentType: 'application/json',
                data: JSON.stringify({
                    device_ids: selectedDeviceIds,
                    expire_date: newExpireDate
                }),
                success: function(result) {
                    toggleLoading(false);
                    $('#batchExpireModal').modal('hide');

                    // 更新设备列表显示
                    loadDeviceList();

                    // 显示详细的更新结果
                    let message = `批量更新完成！\n`;
                    message += `成功更新: ${result.updated} 个设备\n`;

                    if (result.not_found > 0) {
                        message += `未找到: ${result.not_found} 个设备\n`;
                    }

                    if (result.errors > 0) {
                        message += `更新失败: ${result.errors} 个设备\n`;
                    }

                    alert(message);
                },
                error: function(xhr) {
                    toggleLoading(false);
                    alert('批量设置过期时间失败: ' + (xhr.responseJSON?.error || xhr.statusText));
                }
            });
        }

        // ===========================================
        // 预上传缓存管理功能
        // ===========================================

        // 查看预上传缓存状态
        function viewPreuploadCache(deviceId, username) {
            $('#preuploadCacheModalTitle').text(`预上传缓存状态 - ${username}`);
            $('#preupload-cache-content').html('<div class="text-center"><i class="fa fa-spin fa-spinner"></i> 加载中...</div>');

            // 显示模态框
            $('#preuploadCacheModal').modal('show');

            // 加载缓存数据
            loadPreuploadCacheData(deviceId, username);
        }

        // 加载预上传缓存数据
        function loadPreuploadCacheData(deviceId, username) {
            $.ajax({
                url: `/api/grab_devices/${deviceId}/preupload_cache`,
                method: 'GET',
                success: function(response) {
                    if (response.status === 'success') {
                        renderPreuploadCacheData(response.cache_data, username);
                    } else {
                        $('#preupload-cache-content').html(`
                            <div class="alert alert-warning">
                                <i class="fa fa-exclamation-triangle"></i>
                                ${response.message || '无法获取预上传缓存数据'}
                            </div>
                        `);
                    }
                },
                error: function(xhr) {
                    let errorMsg = '加载预上传缓存数据失败';
                    if (xhr.responseJSON && xhr.responseJSON.error) {
                        errorMsg += ': ' + xhr.responseJSON.error;
                    }
                    $('#preupload-cache-content').html(`
                        <div class="alert alert-danger">
                            <i class="fa fa-exclamation-circle"></i> ${errorMsg}
                        </div>
                    `);
                }
            });
        }

        // 渲染预上传缓存数据
        function renderPreuploadCacheData(cacheData, username) {
            if (!cacheData || Object.keys(cacheData).length === 0) {
                $('#preupload-cache-content').html(`
                    <div class="alert alert-info">
                        <i class="fa fa-info-circle"></i>
                        用户 <strong>${username}</strong> 暂无预上传缓存数据
                        <br><small class="text-muted">系统启动时会自动为已登录的设备创建预上传缓存</small>
                    </div>
                `);
                return;
            }

            let html = `
                <div class="mb-3">
                    <h6><i class="fa fa-cloud-upload text-primary"></i> 用户: <strong>${username}</strong></h6>
                    <small class="text-muted">预上传缓存可以大幅提升抢房速度（从7.75秒降至0.75秒）</small>
                </div>

                <div class="table-responsive">
                    <table class="table table-sm table-bordered">
                        <thead class="thead-light">
                            <tr>
                                <th width="25%">文件名</th>
                                <th width="20%">服务器文件名</th>
                                <th width="15%">文件大小</th>
                                <th width="20%">缓存时间</th>
                                <th width="20%">状态</th>
                            </tr>
                        </thead>
                        <tbody>
            `;

            for (const [filePath, fileData] of Object.entries(cacheData)) {
                const fileName = filePath.split('\\').pop() || filePath.split('/').pop();
                const serverName = fileData.server_name || '未知';
                const fileSize = fileData.file_size ? formatFileSize(fileData.file_size) : '未知';
                const cacheTime = fileData.upload_time ? formatDateTime(fileData.upload_time) : '未知';
                const expiresAt = fileData.expires_at ? new Date(fileData.expires_at) : null;
                const isExpired = expiresAt && expiresAt < new Date();

                let statusHtml = '';
                if (isExpired) {
                    statusHtml = '<span class="badge badge-danger">已过期</span>';
                } else if (expiresAt) {
                    const remainingHours = Math.max(0, (expiresAt - new Date()) / (1000 * 60 * 60));
                    if (remainingHours < 1) {
                        statusHtml = '<span class="badge badge-warning">即将过期</span>';
                    } else {
                        statusHtml = `<span class="badge badge-success">有效 (${remainingHours.toFixed(1)}h)</span>`;
                    }
                } else {
                    statusHtml = '<span class="badge badge-secondary">未知</span>';
                }

                html += `
                    <tr ${isExpired ? 'class="table-warning"' : ''}>
                        <td>
                            <i class="fa ${getFileIcon(fileName)}"></i>
                            <span title="${filePath}">${fileName}</span>
                        </td>
                        <td><code>${serverName}</code></td>
                        <td>${fileSize}</td>
                        <td><small>${cacheTime}</small></td>
                        <td>${statusHtml}</td>
                    </tr>
                `;
            }

            html += `
                        </tbody>
                    </table>
                </div>

                <div class="mt-3">
                    <div class="row">
                        <div class="col-md-4">
                            <div class="text-center p-2 bg-light rounded">
                                <div class="font-weight-bold text-primary">${Object.keys(cacheData).length}</div>
                                <div class="text-muted small">缓存文件数</div>
                            </div>
                        </div>
                        <div class="col-md-4">
                            <div class="text-center p-2 bg-light rounded">
                                <div class="font-weight-bold text-success">${Object.values(cacheData).filter(f => {
                                    const expiresAt = f.expires_at ? new Date(f.expires_at) : null;
                                    return expiresAt && expiresAt > new Date();
                                }).length}</div>
                                <div class="text-muted small">有效缓存</div>
                            </div>
                        </div>
                        <div class="col-md-4">
                            <div class="text-center p-2 bg-light rounded">
                                <div class="font-weight-bold text-warning">${Object.values(cacheData).filter(f => {
                                    const expiresAt = f.expires_at ? new Date(f.expires_at) : null;
                                    return !expiresAt || expiresAt <= new Date();
                                }).length}</div>
                                <div class="text-muted small">过期缓存</div>
                            </div>
                        </div>
                    </div>
                </div>
            `;

            $('#preupload-cache-content').html(html);
        }

        // 获取文件图标
        function getFileIcon(fileName) {
            const ext = fileName.split('.').pop().toLowerCase();
            switch (ext) {
                case 'jpg':
                case 'jpeg':
                case 'png':
                case 'gif':
                    return 'fa-file-image-o text-success';
                case 'pdf':
                    return 'fa-file-pdf-o text-danger';
                case 'doc':
                case 'docx':
                    return 'fa-file-word-o text-primary';
                case 'xls':
                case 'xlsx':
                    return 'fa-file-excel-o text-success';
                default:
                    return 'fa-file-o text-muted';
            }
        }

        // 格式化文件大小
        function formatFileSize(bytes) {
            if (bytes === 0) return '0 B';
            const k = 1024;
            const sizes = ['B', 'KB', 'MB', 'GB'];
            const i = Math.floor(Math.log(bytes) / Math.log(k));
            return parseFloat((bytes / Math.pow(k, i)).toFixed(1)) + ' ' + sizes[i];
        }

        // 格式化日期时间
        function formatDateTime(dateString) {
            const date = new Date(dateString);
            return date.toLocaleString('zh-CN', {
                year: 'numeric',
                month: '2-digit',
                day: '2-digit',
                hour: '2-digit',
                minute: '2-digit',
                second: '2-digit'
            });
        }

        // 刷新预上传缓存
        function refreshPreuploadCache(deviceId, username) {
            const button = $('#refresh-preupload-cache-btn');
            const originalHtml = button.html();
            button.prop('disabled', true).html('<i class="fa fa-spin fa-spinner"></i> 刷新中...');

            $.ajax({
                url: `/api/grab_devices/${deviceId}/refresh_preupload_cache`,
                method: 'POST',
                success: function(response) {
                    button.prop('disabled', false).html(originalHtml);

                    if (response.status === 'success') {
                        // 显示刷新结果
                        let message = '预上传缓存刷新完成！\n';
                        if (response.result) {
                            message += `成功: ${response.result.success || 0}个文件\n`;
                            message += `失败: ${response.result.failed || 0}个文件\n`;
                            message += `总计: ${response.result.total || 0}个文件\n`;
                            message += `耗时: ${response.result.duration || 0}秒`;
                        }
                        alert(message);

                        // 重新加载缓存数据
                        loadPreuploadCacheData(deviceId, username);
                    } else {
                        alert('刷新预上传缓存失败: ' + (response.message || '未知错误'));
                    }
                },
                error: function(xhr) {
                    button.prop('disabled', false).html(originalHtml);
                    let errorMsg = '刷新预上传缓存失败';
                    if (xhr.responseJSON && xhr.responseJSON.error) {
                        errorMsg += ': ' + xhr.responseJSON.error;
                    }
                    alert(errorMsg);
                }
            });
        }

        // ===========================================
        // 抢房设备管理功能
        // ===========================================

        // 加载列表
        function loadGrabDeviceList() {
            const params = new URLSearchParams({
                page: grabDevicePagination.currentPage,
                per_page: grabDevicePagination.perPage,
                search: grabDevicePagination.currentSearch,
                enabled: grabDevicePagination.enabledFilter
            });

            toggleLoading(true);

            $.ajax({
                url: '/api/grab_devices?' + params.toString(),
                method: 'GET',
                success: function(response) {
                    grabDevices = response.devices;

                    // 更新分页信息
                    grabDevicePagination.totalPages = response.pagination.total_pages;
                    grabDevicePagination.totalCount = response.pagination.total_count;
                    grabDevicePagination.currentPage = response.pagination.page;

                    // 渲染设备列表
                    renderGrabDeviceList(response.devices);

                    // 更新统计信息
                    updateGrabDeviceStats(response.stats);

                    // 更新分页控件
                    updateGrabPaginationControls(response.pagination);

                    toggleLoading(false);
                },
                error: function(xhr) {
                    $('#grab-device-list').html('<p class="text-danger">加载抢房设备列表失败: ' + (xhr.responseJSON?.error || xhr.statusText) + '</p>');
                    toggleLoading(false);
                }
            });
        }

        // 渲染抢房设备列表
        function renderGrabDeviceList(deviceList) {
            let html = '';

            if (deviceList.length === 0) {
                if (grabDevicePagination.currentSearch || grabDevicePagination.enabledFilter) {
                    html = '<p class="text-muted">未找到符合条件的抢房设备</p>';
                } else {
                    html = '<p class="text-muted">暂无抢房设备，请添加抢房设备</p>';
                }
            } else {
                html += '<div class="row">';

                deviceList.forEach((device, i) => {
                    html += renderGrabDeviceCard(device, i);
                });

                html += '</div>';
            }

            $('#grab-device-list').html(html);

            // 添加按钮事件监听
            $('.edit-grab-device-btn').click(function() {
                let index = $(this).data('index');
                showEditGrabDeviceModal(index);
            });

            $('.delete-grab-device-btn').click(function() {
                let deviceId = $(this).data('id');
                deleteGrabDevice(deviceId);
            });

            $('.test-cookie-btn').click(function() {
                let deviceId = $(this).data('id');
                testDeviceCookie(deviceId);
            });

            $('.extend-cookie-btn').click(function(e) {
                e.preventDefault();

                // 检查按钮是否被禁用
                if ($(this).prop('disabled')) {
                    console.log('延长Cookie按钮被禁用，不执行操作');
                    return false;
                }

                let deviceId = $(this).data('id');
                console.log('延长Cookie按钮被点击，设备ID:', deviceId);
                extendDeviceCookie(deviceId);
            });

            $('.view-application-records-btn').click(function(e) {
                e.preventDefault();

                // 检查按钮是否被禁用
                if ($(this).prop('disabled')) {
                    console.log('查看申请记录按钮被禁用，不执行操作');
                    return false;
                }

                let deviceId = $(this).data('id');
                console.log('查看申请记录按钮被点击，设备ID:', deviceId);
                viewApplicationRecords(deviceId);
            });

            $('.view-user-profile-btn').click(function(e) {
                e.preventDefault();

                // 检查按钮是否被禁用
                if ($(this).prop('disabled')) {
                    console.log('查看用户主页按钮被禁用，不执行操作');
                    return false;
                }

                let deviceId = $(this).data('id');
                console.log('查看用户主页按钮被点击，设备ID:', deviceId);
                viewUserProfile(deviceId);
            });

            $('.view-preupload-cache-btn').click(function() {
                let deviceId = $(this).data('id');
                let username = $(this).data('username');
                console.log('查看预上传缓存按钮被点击，设备ID:', deviceId, '用户名:', username);
                viewPreuploadCache(deviceId, username);
            });
        }

        // 渲染抢房设备卡片
        function renderGrabDeviceCard(device, index) {
            // 调试输出设备登录状态
            console.log(`设备 ${device.username} (ID: ${device.id}) 状态:`, {
                access_token: !!device.access_token,
                sessioncode: !!device.sessioncode,
                access_token_length: device.access_token ? device.access_token.length : 0,
                sessioncode_value: device.sessioncode || 'undefined'
            });

            let enabledBadge = device.enabled ?
                '<span class="badge badge-success">已启用</span>' :
                '<span class="badge badge-warning">已禁用</span>';

            let profileBadge = device.profile_exists ?
                '<span class="badge badge-info ml-1">已配置</span>' :
                '<span class="badge badge-secondary ml-1">未配置</span>';

            let loginStatus = device.access_token ?
                '<small class="text-success">已登录</small>' :
                '<small class="text-danger">未登录</small>';

            // 添加cookie剩余有效期显示
            let cookieTimeStatus = '';
            if (device.access_token && device.cookie_remaining_hours !== undefined) {
                if (device.cookie_remaining_hours <= 0) {
                    cookieTimeStatus = '<small class="text-danger ml-1">(已过期)</small>';
                } else if (device.cookie_remaining_hours < 1) {
                    const minutes = Math.floor(device.cookie_remaining_hours * 60);
                    const seconds = Math.floor((device.cookie_remaining_hours * 3600) % 60);

                    if (minutes > 0) {
                        cookieTimeStatus = `<small class="text-warning ml-1">(剩余${minutes}分钟)</small>`;
                    } else {
                        cookieTimeStatus = `<small class="text-danger ml-1">(剩余${seconds}秒)</small>`;
                    }
                } else if (device.cookie_remaining_hours < 24) {
                    const hours = Math.floor(device.cookie_remaining_hours);
                    const minutes = Math.floor((device.cookie_remaining_hours % 1) * 60);

                    if (minutes > 0) {
                        cookieTimeStatus = `<small class="text-info ml-1">(剩余${hours}小时${minutes}分钟)</small>`;
                    } else {
                        cookieTimeStatus = `<small class="text-info ml-1">(剩余${hours}小时)</small>`;
                    }
                } else {
                    const days = Math.floor(device.cookie_remaining_hours / 24);
                    const hours = Math.floor(device.cookie_remaining_hours % 24);
                    const minutes = Math.floor(((device.cookie_remaining_hours % 24) % 1) * 60);

                    if (hours > 0 && minutes > 0) {
                        cookieTimeStatus = `<small class="text-info ml-1">(剩余${days}天${hours}小时${minutes}分钟)</small>`;
                    } else if (hours > 0) {
                        cookieTimeStatus = `<small class="text-info ml-1">(剩余${days}天${hours}小时)</small>`;
                    } else {
                        cookieTimeStatus = `<small class="text-info ml-1">(剩余${days}天)</small>`;
                    }
                }
            }

            let conditionsText = '';
            if (device.conditions && device.conditions.length > 0) {
                conditionsText = device.conditions.map(c => `${c.type}:${c.value}`).join(', ');
            } else {
                conditionsText = '无条件抢房';
            }

            return `
                <div class="col-md-6 col-lg-4 mb-3">
                    <div class="card device-card h-100 ${device.enabled ? 'border-success' : 'border-warning'}">
                        <div class="card-header d-flex justify-content-between align-items-center">
                            <div>
                                <strong class="card-title mb-0">${device.username}</strong>
                            </div>
                            <div>
                                ${enabledBadge}
                                ${profileBadge}
                            </div>
                        </div>
                        <div class="card-body">
                            <div class="mb-2">
                                <small class="text-muted">设备ID:</small> ${device.id}<br>
                                <small class="text-muted">手机号:</small> ${device.phone}<br>
                                <small class="text-muted">目标小区:</small> ${device.target_estate || '未设置'}<br>
                                <small class="text-muted">登录状态:</small> ${loginStatus}${cookieTimeStatus}<br>
                                <small class="text-muted">Session状态:</small> ${device.sessioncode ? '<small class="text-success">有效</small>' : '<small class="text-warning">缺失</small>'}<br>
                                <small class="text-muted">抢房条件:</small> <span class="text-info">${conditionsText}</span>
                            </div>
                            <div class="device-actions d-flex flex-wrap gap-1">
                                <button class="btn btn-sm btn-outline-primary edit-grab-device-btn" data-index="${index}" title="编辑">
                                    <i class="fa fa-edit"></i>
                                </button>
                                <button class="btn btn-sm btn-outline-info test-cookie-btn" data-id="${device.id}" title="测试Cookie">
                                    <i class="fa fa-flask"></i>
                                </button>
                                <button class="btn btn-sm btn-outline-success extend-cookie-btn" data-id="${device.id}"
                                        title="${device.access_token && device.sessioncode ? '延长Cookie' : '需要先登录获取sessioncode'}"
                                        ${!device.access_token || !device.sessioncode ? 'disabled="disabled"' : ''}>
                                    <i class="fa fa-refresh"></i>
                                </button>
                                                                <button class="btn btn-sm btn-outline-warning view-application-records-btn" data-id="${device.id}"
                                        title="${device.access_token ? '查看申请记录' : '需要先登录才能查看申请记录'}"
                                        ${!device.access_token ? 'disabled="disabled"' : ''}>
                                    <i class="fa fa-file-text-o"></i>
                                </button>
                                <button class="btn btn-sm btn-outline-secondary view-user-profile-btn" data-id="${device.id}"
                                        title="${device.access_token ? '查看用户主页' : '需要先登录才能查看用户主页'}"
                                        ${!device.access_token ? 'disabled="disabled"' : ''}>
                                    <i class="fa fa-user"></i>
                                </button>
                                <button class="btn btn-sm btn-outline-warning view-preupload-cache-btn" data-id="${device.id}" data-username="${device.username}" title="查看预上传缓存">
                                    <i class="fa fa-cloud-upload"></i>
                                </button>
                                <button class="btn btn-sm btn-outline-danger delete-grab-device-btn" data-id="${device.id}" title="删除">
                                    <i class="fa fa-trash"></i>
                                </button>
                            </div>
                        </div>
                    </div>
                </div>
            `;
        }

        // 更新抢房设备统计信息
        function updateGrabDeviceStats(stats) {
            $('#total-grab-devices').text(stats.total || 0);
            $('#enabled-grab-devices').text(stats.enabled || 0);
            $('#disabled-grab-devices').text(stats.disabled || 0);
            $('#logged-grab-devices').text(stats.logged || 0);

            if (stats.total > 0) {
                $('#grab-device-stats').show();
            } else {
                $('#grab-device-stats').hide();
            }
        }

        // 更新抢房设备分页控件
        function updateGrabPaginationControls(pagination) {
            if (pagination.total_pages <= 1) {
                $('#grab-device-pagination').hide();
                return;
            }

            $('#grab-device-pagination').show();

            const startItem = (pagination.page - 1) * pagination.per_page + 1;
            const endItem = Math.min(pagination.page * pagination.per_page, pagination.total_count);
            $('#grab-pagination-info').text(`显示第 ${startItem}-${endItem} 项，共 ${pagination.total_count} 项`);

            let paginationHtml = '';

            // 上一页按钮
            if (pagination.has_prev) {
                paginationHtml += `<li class="page-item"><a class="page-link grab-page-link" href="#" data-page="${pagination.page - 1}">上一页</a></li>`;
            } else {
                paginationHtml += `<li class="page-item disabled"><span class="page-link">上一页</span></li>`;
            }

            // 页码按钮
            const maxPagesToShow = 5;
            let startPage = Math.max(1, pagination.page - Math.floor(maxPagesToShow / 2));
            let endPage = Math.min(pagination.total_pages, startPage + maxPagesToShow - 1);

            if (endPage - startPage + 1 < maxPagesToShow) {
                startPage = Math.max(1, endPage - maxPagesToShow + 1);
            }

            if (startPage > 1) {
                paginationHtml += `<li class="page-item"><a class="page-link grab-page-link" href="#" data-page="1">1</a></li>`;
                if (startPage > 2) {
                    paginationHtml += `<li class="page-item disabled"><span class="page-link">...</span></li>`;
                }
            }

            for (let i = startPage; i <= endPage; i++) {
                if (i === pagination.page) {
                    paginationHtml += `<li class="page-item active"><span class="page-link">${i}</span></li>`;
                } else {
                    paginationHtml += `<li class="page-item"><a class="page-link grab-page-link" href="#" data-page="${i}">${i}</a></li>`;
                }
            }

            if (endPage < pagination.total_pages) {
                if (endPage < pagination.total_pages - 1) {
                    paginationHtml += `<li class="page-item disabled"><span class="page-link">...</span></li>`;
                }
                paginationHtml += `<li class="page-item"><a class="page-link grab-page-link" href="#" data-page="${pagination.total_pages}">${pagination.total_pages}</a></li>`;
            }

            // 下一页按钮
            if (pagination.has_next) {
                paginationHtml += `<li class="page-item"><a class="page-link grab-page-link" href="#" data-page="${pagination.page + 1}">下一页</a></li>`;
            } else {
                paginationHtml += `<li class="page-item disabled"><span class="page-link">下一页</span></li>`;
            }

            $('#grab-pagination-controls').html(paginationHtml);

            // 绑定分页点击事件
            $('.grab-page-link').click(function(e) {
                e.preventDefault();
                const page = parseInt($(this).data('page'));
                if (page !== grabDevicePagination.currentPage) {
                    grabDevicePagination.currentPage = page;
                    loadGrabDeviceList();
                }
            });
        }

        // 加载小区列表
        function loadEstatesList() {
            $.ajax({
                url: '/api/grab_devices/estates',
                method: 'GET',
                success: function(response) {
                    if (response.success) {
                        const select = $('#grab-target-estate');
                        const currentValue = select.val();

                        // 清空现有选项（保留第一个默认选项）
                        select.html('<option value="">请选择目标小区</option>');

                        // 添加小区选项
                        if (Array.isArray(response.data)) {
                            response.data.forEach(estateName => {
                                const option = `<option value="${estateName}">${estateName}</option>`;
                                select.append(option);
                            });
                        }

                        // 恢复之前选择的值
                        if (currentValue) {
                            select.val(currentValue);
                        }
                    } else {
                        console.error('获取小区列表失败:', response.message || '未知错误');
                    }
                },
                error: function(xhr) {
                    console.error('网络错误:', xhr.statusText);
                    // 如果API失败，添加一些默认选项
                    const select = $('#grab-target-estate');
                    select.html(`
                        <option value="">请选择目标小区</option>
                        <option value="青年人才公寓">青年人才公寓</option>
                        <option value="青年公租房">青年公租房</option>
                        <option value="创客公寓">创客公寓</option>
                        <option value="人才公寓">人才公寓</option>
                    `);
                }
            });
        }

        // 显示添加抢房设备模态框
        function showAddGrabDeviceModal() {
            console.log('打开添加抢房设备模态框'); // 添加调试日志

            $('#grab-device-form')[0].reset();
            $('#grab-device-id').val('');
            $('#grabDeviceModalTitle').text('添加抢房设备');

            // 重置抢房条件
            resetGrabConditions();

            // 隐藏登录状态部分
            $('#login-status-section').hide();
            $('#view-profile-btn').hide();

            // 加载小区列表
            loadEstatesList();

            currentGrabDeviceIndex = -1;

            // 修复模态框显示问题
            setTimeout(function() {
                $('#grabDeviceModal').modal({
                    backdrop: 'static',
                    keyboard: false,
                    show: true
                });
                // 确保模态框正确定位
                $('#grabDeviceModal').on('shown.bs.modal', function() {
                    console.log('模态框已显示');
                    $(this).find('.modal-dialog').css('margin-top', '50px');
                });
            }, 100);
        }

        // 显示编辑抢房设备模态框
        function showEditGrabDeviceModal(index) {
            let device = grabDevices[index];

            $('#grab-device-id').val(device.id);
            $('#grab-device-username').val(device.username);
            $('#grab-device-phone').val(device.phone);
            $('#grab-house-id').val(device.house_id || '');
            $('#grab-device-enabled').prop('checked', device.enabled);

            // 设置抢房条件
            setGrabConditions(device.conditions || []);

            // 显示登录状态
            $('#login-status-section').show();
            $('#cookie-status').text(device.access_token ? '已登录' : '未登录');

            // 显示cookie剩余有效期
            if (device.cookie_remaining_hours !== undefined) {
                updateCookieRemainingTime(device.cookie_remaining_hours);
            } else {
                $('#cookie-remaining-time').text('').removeClass('badge-success badge-warning badge-danger');
            }

            // 显示Cookie管理按钮（仅当设备已登录且有sessioncode时）
            if (device.access_token && device.sessioncode) {
                $('.cookie-management-buttons').show();
            } else {
                $('.cookie-management-buttons').hide();
            }

            // 显示profile按钮
            if (device.profile_exists) {
                $('#view-profile-btn').show();
            } else {
                $('#view-profile-btn').hide();
            }

            // 加载小区列表，然后设置选中值
            loadEstatesList();
            // 使用setTimeout确保选项加载完成后再设置值
            setTimeout(() => {
                $('#grab-target-estate').val(device.target_estate || '');
            }, 100);

            $('#grabDeviceModalTitle').text('编辑抢房设备');
            currentGrabDeviceIndex = index;

            // 修复模态框显示问题
            setTimeout(function() {
                $('#grabDeviceModal').modal({
                    backdrop: 'static',
                    keyboard: false,
                    show: true
                });
                // 确保模态框正确定位
                $('#grabDeviceModal').on('shown.bs.modal', function() {
                    console.log('编辑模态框已显示');
                    $(this).find('.modal-dialog').css('margin-top', '50px');
                });
            }, 100);
        }

        // 保存抢房设备
        function saveGrabDevice() {
            console.log('saveGrabDevice 函数被调用'); // 添加调试日志

            const deviceData = {
                username: $('#grab-device-username').val().trim(),
                phone: $('#grab-device-phone').val().trim(),
                target_estate: $('#grab-target-estate').val().trim(),
                house_id: $('#grab-house-id').val().trim(),
                enabled: $('#grab-device-enabled').is(':checked'),
                conditions: getGrabConditions()
            };

            // 验证和调试条件数据
            console.log('抢房条件数据:', deviceData.conditions);
            if (deviceData.conditions && deviceData.conditions.length > 0) {
                console.log('条件数量:', deviceData.conditions.length);
                deviceData.conditions.forEach((condition, index) => {
                    console.log(`条件${index + 1}: ${condition.type} = ${condition.value}`);
                });
            } else {
                console.log('无抢房条件或条件为空');
            }

            if (!deviceData.username || !deviceData.phone) {
                alert('请填写用户名和手机号');
                return;
            }

            const deviceId = $('#grab-device-id').val();
            if (deviceId) {
                deviceData.id = parseInt(deviceId);
            }

            console.log('提交的设备数据:', JSON.stringify(deviceData, null, 2));
            toggleLoading(true);

            const url = deviceId ? `/api/grab_devices/${deviceId}` : '/api/grab_devices';
            const method = deviceId ? 'PUT' : 'POST';

            $.ajax({
                url: url,
                method: method,
                contentType: 'application/json',
                data: JSON.stringify(deviceData),
                success: function(result) {
                    toggleLoading(false);
                    console.log('保存成功返回结果:', result);

                    // 验证保存的条件数据
                    if (result.device && result.device.conditions) {
                        console.log('已保存的条件数据:', result.device.conditions);
                        const conditionCount = result.device.conditions.length;
                        const message = deviceId ?
                            `抢房设备更新成功，已保存${conditionCount}个抢房条件` :
                            `抢房设备添加成功，已保存${conditionCount}个抢房条件`;
                        alert(message);
                    } else {
                        alert(deviceId ? '抢房设备更新成功' : '抢房设备添加成功');
                    }

                    $('#grabDeviceModal').modal('hide');
                    loadGrabDeviceList();
                },
                error: function(xhr) {
                    toggleLoading(false);
                    alert('保存抢房设备失败: ' + (xhr.responseJSON?.error || xhr.statusText));
                }
            });
        }

        // 删除抢房设备
        function deleteGrabDevice(deviceId) {
            if (!confirm('确定要删除此抢房设备吗？')) {
                return;
            }

            toggleLoading(true);

            $.ajax({
                url: `/api/grab_devices/${deviceId}`,
                method: 'DELETE',
                success: function() {
                    toggleLoading(false);
                    loadGrabDeviceList();
                    alert('抢房设备删除成功');
                },
                error: function(xhr) {
                    toggleLoading(false);
                    alert('删除抢房设备失败: ' + (xhr.responseJSON?.error || xhr.statusText));
                }
            });
        }

        // 添加抢房条件
        function addGrabCondition() {
            console.log('addGrabCondition 函数被调用'); // 添加调试日志

            const conditionHtml = `
                <div class="row mb-2 grab-condition-row">
                    <div class="col-md-4">
                        <select class="form-control condition-type">
                            <option value="">选择条件类型</option>
                            <option value="area">面积</option>
                            <option value="direction">朝向</option>
                            <option value="building">楼号</option>
                            <option value="floor">楼层</option>
                            <option value="roomno">房间号</option>
                            <option value="outtype">租赁类型</option>
                            <option value="rent">租金</option>
                        </select>
                    </div>
                    <div class="col-md-6">
                        <input type="text" class="form-control condition-value" placeholder="输入条件值，如: >50, 南向, 1号楼">
                    </div>
                    <div class="col-md-2">
                        <button type="button" class="btn btn-danger btn-sm remove-condition">
                            <i class="fa fa-times"></i>
                        </button>
                    </div>
                </div>
            `;

            $('#grab-conditions-container').append(conditionHtml);
            updateRemoveConditionButtons();
        }

        // 更新删除条件按钮显示
        function updateRemoveConditionButtons() {
            const rows = $('.grab-condition-row');
            if (rows.length > 1) {
                $('.remove-condition').show();
            } else {
                $('.remove-condition').hide();
            }
        }

        // 重置抢房条件
        function resetGrabConditions() {
            $('#grab-conditions-container').html(`
                <div class="row mb-2 grab-condition-row">
                    <div class="col-md-4">
                        <select class="form-control condition-type">
                            <option value="">选择条件类型</option>
                            <option value="area">面积</option>
                            <option value="direction">朝向</option>
                            <option value="building">楼号</option>
                            <option value="floor">楼层</option>
                            <option value="roomno">房间号</option>
                            <option value="outtype">租赁类型</option>
                            <option value="rent">租金</option>
                        </select>
                    </div>
                    <div class="col-md-6">
                        <input type="text" class="form-control condition-value" placeholder="输入条件值，如: >50, 南向, 1号楼">
                    </div>
                    <div class="col-md-2">
                        <button type="button" class="btn btn-danger btn-sm remove-condition" style="display: none;">
                            <i class="fa fa-times"></i>
                        </button>
                    </div>
                </div>
            `);
        }

        // 设置抢房条件
        function setGrabConditions(conditions) {
            $('#grab-conditions-container').empty();

            if (conditions.length === 0) {
                resetGrabConditions();
                return;
            }

            conditions.forEach((condition, index) => {
                const conditionHtml = `
                    <div class="row mb-2 grab-condition-row">
                        <div class="col-md-4">
                            <select class="form-control condition-type">
                                <option value="">选择条件类型</option>
                                <option value="area" ${condition.type === 'area' ? 'selected' : ''}>面积</option>
                                <option value="direction" ${condition.type === 'direction' ? 'selected' : ''}>朝向</option>
                                <option value="building" ${condition.type === 'building' ? 'selected' : ''}>楼号</option>
                                <option value="floor" ${condition.type === 'floor' ? 'selected' : ''}>楼层</option>
                                <option value="roomno" ${condition.type === 'roomno' ? 'selected' : ''}>房间号</option>
                                <option value="outtype" ${condition.type === 'outtype' ? 'selected' : ''}>租赁类型</option>
                                <option value="rent" ${condition.type === 'rent' ? 'selected' : ''}>租金</option>
                            </select>
                        </div>
                        <div class="col-md-6">
                            <input type="text" class="form-control condition-value" value="${condition.value}" placeholder="输入条件值，如: >50, 南向, 1号楼">
                        </div>
                        <div class="col-md-2">
                            <button type="button" class="btn btn-danger btn-sm remove-condition">
                                <i class="fa fa-times"></i>
                            </button>
                        </div>
                    </div>
                `;
                $('#grab-conditions-container').append(conditionHtml);
            });

            updateRemoveConditionButtons();
        }

        // 获取抢房条件
        function getGrabConditions() {
            const conditions = [];
            $('.grab-condition-row').each(function() {
                const type = $(this).find('.condition-type').val();
                const value = $(this).find('.condition-value').val().trim();

                if (type && value) {
                    conditions.push({ type: type, value: value });
                }
            });
            return conditions;
        }

        // 获取验证码图片
        function getCaptchaImage() {
            let deviceId = $('#grab-device-id').val();
            if (!deviceId) {
                alert('请先保存设备信息');
                return;
            }

            let button = $('#get-captcha-btn');
            button.prop('disabled', true).text('获取中...');

            $.ajax({
                url: `/api/grab_devices/${deviceId}/get_captcha`,
                method: 'POST',
                contentType: 'application/json',
                data: JSON.stringify({}),
                success: function(response) {
                    if (response.status === 'success') {
                        // 显示验证码图片
                        $('#captcha-image').attr('src', response.image);
                        $('#captcha-display').show();

                        // 显示OCR识别候选结果
                        if (response.candidates && response.candidates.length > 0) {
                            let candidatesHtml = '<small class="text-muted">识别建议: ';
                            response.candidates.forEach((candidate, index) => {
                                candidatesHtml += `<span class="badge badge-light captcha-candidate" style="cursor: pointer; margin-right: 5px;">${candidate}</span>`;
                            });
                            candidatesHtml += '</small>';
                            $('#captcha-candidates').html(candidatesHtml);

                            // 自动填充第一个候选结果
                            const firstCandidate = response.candidates[0];
                            $('#img-verification-code').val(firstCandidate);

                            // 显示自动填充提示
                            $('#captcha-candidates').prepend('<small class="text-success">已自动填充: ' + firstCandidate + ' | </small>');

                            // 自动发送短信验证码
                            setTimeout(() => {
                                button.prop('disabled', false).text('获取验证码图片');
                                sendSmsCode();
                            }, 500);

                            // 添加点击候选结果自动填入的功能
                            $('.captcha-candidate').click(function() {
                                $('#img-verification-code').val($(this).text());
                            });
                        } else {
                            $('#captcha-candidates').html('<small class="text-muted">自动识别失败，请手动输入</small>');
                        }
                    } else {
                        alert('获取验证码失败: ' + (response.error || '未知错误'));
                    }
                },
                error: function(xhr) {
                    let error = xhr.responseJSON?.error || xhr.statusText;
                    alert('获取验证码失败: ' + error);
                },
                complete: function() {
                    // 如果没有候选结果，重置按钮状态
                    if (!$('#captcha-candidates').text().includes('已自动填充')) {
                        button.prop('disabled', false).text('获取验证码图片');
                    }
                }
            });
        }

        // 发送短信验证码
        function sendSmsCode() {
            let deviceId = $('#grab-device-id').val();
            let imgCode = $('#img-verification-code').val().trim();

            if (!deviceId) {
                alert('请先保存设备信息');
                return;
            }

            if (!imgCode) {
                alert('请先输入图形验证码');
                return;
            }

            let button = $('#send-sms-btn');
            button.prop('disabled', true).text('发送中...');

            $.ajax({
                url: `/api/grab_devices/${deviceId}/send_sms`,
                method: 'POST',
                contentType: 'application/json',
                data: JSON.stringify({
                    captcha: imgCode
                }),
                success: function(response) {
                    if (response.status === 'success') {
                        alert(response.message);
                        // 自动聚焦到短信验证码输入框
                        $('#sms-verification-code').focus();
                    } else {
                        alert('发送失败: ' + (response.error || '未知错误'));
                    }
                },
                error: function(xhr) {
                    let error = xhr.responseJSON?.error || xhr.statusText;
                    alert('发送短信验证码失败: ' + error);
                },
                complete: function() {
                    button.prop('disabled', false).text('发送短信验证码');
                }
            });
        }

        // 登录抢房设备
        function loginGrabDevice() {
            let deviceId = $('#grab-device-id').val();
            let imgCode = $('#img-verification-code').val().trim();
            let smsCode = $('#sms-verification-code').val().trim();

            if (!deviceId) {
                alert('请先保存设备信息');
                return;
            }

            if (!imgCode) {
                alert('请先输入图形验证码');
                return;
            }

            if (!smsCode) {
                alert('请先输入短信验证码');
                return;
            }

            let button = $('#login-device-btn');
            button.prop('disabled', true).text('登录中...');

            $.ajax({
                url: `/api/grab_devices/${deviceId}/login`,
                method: 'POST',
                contentType: 'application/json',
                data: JSON.stringify({
                    img_code: imgCode,
                    sms_code: smsCode
                }),
                success: function(response) {
                    if (response.status === 'success') {
                        alert(response.message);

                        // 更新cookie状态显示
                        $('#cookie-status').text('已登录');

                        // 显示cookie剩余有效期
                        if (response.device && response.device.cookie_remaining_hours !== undefined) {
                            updateCookieRemainingTime(response.device.cookie_remaining_hours);
                        }

                        // 刷新设备列表
                        loadGrabDeviceList();
                    } else {
                        alert('登录失败: ' + (response.error || '未知错误'));
                    }
                },
                error: function(xhr) {
                    let error = xhr.responseJSON?.error || xhr.statusText;
                    alert('登录失败: ' + error);
                },
                complete: function() {
                    button.prop('disabled', false).text('完成登录');
                }
            });
        }

        // 更新cookie剩余有效期显示
        function updateCookieRemainingTime(remainingHours) {
            const remainingTimeElement = $('#cookie-remaining-time');
            const remainingMinutes = remainingHours * 60;

            console.log(`更新Cookie剩余时间显示: ${remainingHours} 小时 (${remainingMinutes} 分钟)`);

            if (remainingHours <= 0) {
                remainingTimeElement.text('已过期').removeClass('badge-success badge-warning badge-info').addClass('badge-danger');
            } else if (remainingMinutes <= 10) {
                // 剩余10分钟内，显示危险状态
                const minutes = Math.floor(remainingMinutes);
                const seconds = Math.floor((remainingMinutes % 1) * 60);

                if (minutes > 0) {
                    remainingTimeElement.text(`剩余${minutes}分钟 (即将过期)`).removeClass('badge-success badge-warning badge-info').addClass('badge-danger');
                } else {
                    remainingTimeElement.text(`剩余${seconds}秒 (即将过期)`).removeClass('badge-success badge-warning badge-info').addClass('badge-danger');
                }
            } else if (remainingHours < 1) {
                // 剩余1小时内但超过10分钟
                const minutes = Math.floor(remainingMinutes);
                remainingTimeElement.text(`剩余${minutes}分钟`).removeClass('badge-success badge-danger badge-info').addClass('badge-warning');
            } else if (remainingHours < 24) {
                const hours = Math.floor(remainingHours);
                const minutes = Math.floor((remainingHours % 1) * 60);

                if (minutes > 0) {
                    remainingTimeElement.text(`剩余${hours}小时${minutes}分钟`).removeClass('badge-danger badge-warning badge-info').addClass('badge-success');
                } else {
                    remainingTimeElement.text(`剩余${hours}小时`).removeClass('badge-danger badge-warning badge-info').addClass('badge-success');
                }
            } else {
                const days = Math.floor(remainingHours / 24);
                const hours = Math.floor(remainingHours % 24);
                const minutes = Math.floor(((remainingHours % 24) % 1) * 60);

                if (hours > 0 && minutes > 0) {
                    remainingTimeElement.text(`剩余${days}天${hours}小时${minutes}分钟`).removeClass('badge-danger badge-warning badge-info').addClass('badge-success');
                } else if (hours > 0) {
                    remainingTimeElement.text(`剩余${days}天${hours}小时`).removeClass('badge-danger badge-warning badge-info').addClass('badge-success');
                } else {
                    remainingTimeElement.text(`剩余${days}天`).removeClass('badge-danger badge-warning badge-info').addClass('badge-success');
                }
            }

            // 如果剩余时间少于10分钟，显示延长建议
            if (remainingMinutes > 0 && remainingMinutes <= 10) {
                console.log('Cookie即将过期，建议重新登录');
                // 可以在这里添加自动延长提示或按钮
            }
        }

        // 显示抢房设备登录模态框
        function showGrabDeviceLoginModal(deviceId) {
            // 这里可以实现一个简化的登录界面
            const device = grabDevices.find(d => d.id === deviceId);
            if (!device) {
                alert('设备不存在');
                return;
            }

            // 打开编辑模态框并显示登录区域
            const index = grabDevices.findIndex(d => d.id === deviceId);
            showEditGrabDeviceModal(index);
        }

        // 智能一键自动登录抢房设备
        function autoLoginGrabDevice() {
            let deviceId = $('#grab-device-id').val();
            if (!deviceId) {
                alert('请先保存设备信息');
                return;
            }

            let button = $('#auto-login-btn');
            let originalText = button.text();
            button.prop('disabled', true).text('🤖 智能识别中...');

            // 隐藏手动登录区域
            $('#manual-login-section').hide();

            // 显示智能自动登录进度
            $('#auto-login-progress').show().html(
                '<div class="alert alert-info">' +
                '<i class="fa fa-spin fa-spinner"></i> 🤖 智能验证码识别中...' +
                '<br><small class="text-muted">正在使用多种算法分析验证码图片，请稍候...</small>' +
                '</div>'
            );

            $.ajax({
                url: `/api/grab_devices/${deviceId}/auto_login`,
                method: 'POST',
                contentType: 'application/json',
                data: JSON.stringify({}),
                success: function(response) {
                    if (response.status === 'sms_sent') {
                        // 智能识别成功，短信发送成功
                        let qualityIcon = response.recognition_quality === 'high' ? '🎯' : '✅';
                        let qualityText = response.recognition_quality === 'high' ? '高质量识别' : '标准识别';

                        let successHtml =
                            '<div class="alert alert-success">' +
                            '<i class="fa fa-check"></i> ' + qualityIcon + ' ' + response.message +
                            '<br><small>识别质量: ' + qualityText + ' | 尝试次数: ' + response.attempt + '/' + response.total_attempts + '</small>';

                        // 显示识别的验证码
                        if (response.img_code) {
                            successHtml += '<br><small>识别结果: <strong>' + response.img_code + '</strong></small>';
                        }

                        successHtml += '</div>';

                        // 添加短信验证码输入区域
                        successHtml +=
                            '<div class="form-group">' +
                            '<label for="auto-sms-code">📱 短信验证码:</label>' +
                            '<input type="text" class="form-control" id="auto-sms-code" placeholder="请输入收到的6位短信验证码" maxlength="6">' +
                            '</div>' +
                            '<button type="button" class="btn btn-primary btn-block" id="complete-auto-login-btn" onclick="completeAutoLogin(\'' + response.img_code + '\')">🚀 完成登录</button>';

                        // 添加统计信息
                        if (response.failed_attempts && response.failed_attempts.length > 0) {
                            successHtml += '<br><small class="text-muted">跳过了 ' + response.failed_attempts.length + ' 个低质量候选</small>';
                        }

                        $('#auto-login-progress').html(successHtml);

                        // 自动聚焦到短信验证码输入框
                        setTimeout(() => {
                            $('#auto-sms-code').focus();
                        }, 100);

                    } else {
                        $('#auto-login-progress').html(
                            '<div class="alert alert-danger">' +
                            '<i class="fa fa-times"></i> ❌ ' + (response.error || '智能识别失败') +
                            '</div>'
                        );
                    }
                },
                error: function(xhr) {
                    let errorData = xhr.responseJSON || {};
                    let errorMessage = errorData.error || xhr.statusText;

                    let errorHtml = '<div class="alert alert-danger">' +
                        '<i class="fa fa-times"></i> ❌ ' + errorMessage;

                    // 显示详细信息和建议
                    if (errorData.details) {
                        errorHtml += '<br><small>📋 详细: ' + errorData.details + '</small>';
                    }

                    if (errorData.suggestion) {
                        errorHtml += '<br><small>💡 建议: ' + errorData.suggestion + '</small>';
                    }

                    // 显示失败的尝试统计
                    if (errorData.failed_attempts && errorData.failed_attempts.length > 0) {
                        errorHtml += '<br><small>📊 尝试了 ' + errorData.failed_attempts.length + ' 个候选验证码</small>';

                        // 显示尝试的验证码（限制显示数量）
                        let codes = errorData.failed_attempts.map(attempt => attempt.code).slice(0, 5);
                        if (codes.length > 0) {
                            errorHtml += '<br><small>🔍 识别结果: ' + codes.join(', ');
                            if (errorData.failed_attempts.length > 5) {
                                errorHtml += ' 等' + errorData.failed_attempts.length + '个';
                            }
                            errorHtml += '</small>';
                        }
                    }

                    // 显示最后一张验证码图片供参考
                    if (errorData.image) {
                        errorHtml += '<br><div style="margin-top: 10px;"><small>最后的验证码图片:</small><br>';
                        errorHtml += '<img src="' + errorData.image + '" alt="验证码" style="max-width: 200px; border: 1px solid #ddd; border-radius: 4px;"></div>';
                    }

                    errorHtml += '</div>';

                    // 添加重试和手动登录选项
                    errorHtml +=
                        '<div class="mt-3">' +
                        '<button type="button" class="btn btn-outline-primary btn-sm me-2" onclick="autoLoginGrabDevice()">🔄 重新尝试</button>' +
                        '<button type="button" class="btn btn-outline-secondary btn-sm" onclick="showManualLogin()">👤 手动登录</button>' +
                        '</div>';

                    $('#auto-login-progress').html(errorHtml);
                },
                complete: function() {
                    button.prop('disabled', false).text(originalText);
                }
            });
        }

        // 显示手动登录区域
        function showManualLogin() {
            $('#auto-login-progress').hide();
            $('#manual-login-section').show();
        }

        // 查看抢房设备配置
        function viewGrabDeviceProfile() {
            let deviceId = $('#grab-device-id').val();
            if (!deviceId) {
                alert('设备信息不存在');
                return;
            }

            // 打开一个新窗口显示配置信息
            let profileUrl = `/api/grab_devices/${deviceId}/profile`;
            window.open(profileUrl, '_blank', 'width=800,height=600,scrollbars=yes,resizable=yes');
        }

        // 完成自动登录
        function completeAutoLogin(imgCode) {
            let deviceId = $('#grab-device-id').val();
            let smsCode = $('#auto-sms-code').val().trim();

            if (!smsCode) {
                alert('请输入短信验证码');
                return;
            }

            let button = $('#complete-auto-login-btn');
            button.prop('disabled', true).text('登录中...');

            $.ajax({
                url: `/api/grab_devices/${deviceId}/complete_auto_login`,
                method: 'POST',
                contentType: 'application/json',
                data: JSON.stringify({
                    sms_code: smsCode,
                    img_code: imgCode
                }),
                success: function(response) {
                    if (response.status === 'success') {
                        $('#auto-login-progress').html(
                            '<div class="alert alert-success">' +
                            '<i class="fa fa-check"></i> ' + response.message +
                            '</div>'
                        );

                        // 更新cookie状态显示
                        $('#cookie-status').text('已登录');

                        // 显示cookie剩余有效期
                        if (response.device && response.device.cookie_remaining_hours !== undefined) {
                            updateCookieRemainingTime(response.device.cookie_remaining_hours);
                        }

                        // 刷新设备列表
                        loadGrabDeviceList();

                        // 3秒后隐藏进度区域
                        setTimeout(() => {
                            $('#auto-login-progress').hide();
                            $('#manual-login-section').show();
                        }, 3000);

                    } else {
                        $('#auto-login-progress').html(
                            '<div class="alert alert-danger">' +
                            '<i class="fa fa-times"></i> 登录失败: ' + (response.error || '未知错误') +
                            '</div>'
                        );
                    }
                },
                error: function(xhr) {
                    let error = xhr.responseJSON?.error || xhr.statusText;
                    $('#auto-login-progress').html(
                        '<div class="alert alert-danger">' +
                        '<i class="fa fa-times"></i> 登录失败: ' + error +
                        '</div>'
                    );
                },
                complete: function() {
                    button.prop('disabled', false).text('完成登录');
                }
            });
        }

        // 测试设备Cookie有效性
        function testDeviceCookie(deviceId) {
            if (!deviceId) {
                alert('设备ID无效');
                return;
            }

            // 显示测试状态
            const button = $(`.test-cookie-btn[data-id="${deviceId}"]`);
            const originalHtml = button.html();
            button.prop('disabled', true).html('<i class="fa fa-spin fa-spinner"></i>');

            $.ajax({
                url: `/api/grab_devices/${deviceId}/test_cookie`,
                method: 'POST',
                contentType: 'application/json',
                data: JSON.stringify({}),
                success: function(response) {
                    if (response.status === 'success') {
                        if (response.valid) {
                            alert(`Cookie测试成功: ${response.message}`);
                        } else {
                            alert(`Cookie已失效: ${response.message}\n请重新登录。`);
                        }
                    } else {
                        alert(`Cookie测试失败: ${response.message}`);
                    }

                    // 刷新设备列表以获取最新状态
                    loadGrabDeviceList();
                },
                error: function(xhr) {
                    let error = xhr.responseJSON?.error || xhr.statusText;
                    alert(`Cookie测试失败: ${error}`);
                },
                complete: function() {
                    button.prop('disabled', false).html(originalHtml);
                }
            });
        }

        // 延长设备Cookie（从设备列表调用）
        function extendDeviceCookie(deviceId) {
            if (!deviceId) {
                alert('设备ID无效');
                return;
            }

            // 获取设备信息
            const device = grabDevices.find(d => d.id == deviceId);
            if (!device) {
                alert('找不到设备信息');
                return;
            }

            // 详细的状态检查和用户反馈
            console.log('延长Cookie - 设备详细信息:', {
                id: device.id,
                username: device.username,
                access_token: !!device.access_token,
                sessioncode: !!device.sessioncode,
                access_token_preview: device.access_token ? device.access_token.substring(0, 20) + '...' : null,
                sessioncode_value: device.sessioncode
            });

            if (!device.access_token) {
                alert('设备未登录，缺少access_token\n\n请先进行登录操作。');
                return;
            }

            if (!device.sessioncode) {
                alert('设备缺少sessioncode\n\n这通常是因为设备使用旧版登录方式。\n请重新登录以获取sessioncode。');
                return;
            }

            // 显示延长状态
            const button = $(`.extend-cookie-btn[data-id="${deviceId}"]`);
            const originalHtml = button.html();
            button.prop('disabled', true).html('<i class="fa fa-spin fa-spinner"></i>');

            performExtendCookie(deviceId, device.phone, device.access_token, device.sessioncode, function() {
                // 恢复按钮状态
                button.prop('disabled', false).html(originalHtml);
            });
        }

        // 从模态框延长Cookie
        function extendCookieFromModal() {
            const deviceId = $('#grab-device-id').val();
            if (!deviceId) {
                alert('设备ID无效');
                return;
            }

            // 获取设备信息
            const device = grabDevices.find(d => d.id == deviceId);
            if (!device) {
                alert('找不到设备信息');
                return;
            }

            if (!device.access_token || !device.sessioncode) {
                alert('设备缺少必要的登录信息（access_token或sessioncode），请重新登录');
                return;
            }

            // 显示延长状态
            const button = $('#extend-cookie-modal-btn');
            const originalHtml = button.html();
            button.prop('disabled', true).html('<i class="fa fa-spin fa-spinner"></i> 延长中...');

            performExtendCookie(deviceId, device.phone, device.access_token, device.sessioncode, function() {
                // 恢复按钮状态
                button.prop('disabled', false).html(originalHtml);
            });
        }

        // 从模态框测试Cookie
        function testCookieFromModal() {
            const deviceId = $('#grab-device-id').val();
            if (!deviceId) {
                alert('设备ID无效');
                return;
            }

            // 显示测试状态
            const button = $('#test-cookie-modal-btn');
            const originalHtml = button.html();
            button.prop('disabled', true).html('<i class="fa fa-spin fa-spinner"></i> 测试中...');

            $.ajax({
                url: `/api/grab_devices/${deviceId}/test_cookie`,
                method: 'POST',
                contentType: 'application/json',
                data: JSON.stringify({}),
                success: function(response) {
                    if (response.status === 'success') {
                        if (response.valid) {
                            alert(`Cookie测试成功: ${response.message}`);
                        } else {
                            alert(`Cookie已失效: ${response.message}\n请重新登录。`);
                        }
                    } else {
                        alert(`Cookie测试失败: ${response.message}`);
                    }

                    // 更新模态框中的Cookie状态显示
                    updateModalCookieStatus(response);
                },
                error: function(xhr) {
                    let error = xhr.responseJSON?.error || xhr.statusText;
                    alert(`Cookie测试失败: ${error}`);
                },
                complete: function() {
                    button.prop('disabled', false).html(originalHtml);
                }
            });
        }

        // 执行Cookie延长操作
        function performExtendCookie(deviceId, phone, accessToken, sessioncode, onComplete) {
            if (!confirm('确定要延长Cookie有效期吗？\n\n此操作将使用autologin接口获取新的accesstoken。')) {
                onComplete();
                return;
            }

            $.ajax({
                url: `/api/grab_devices/${deviceId}/extend_cookie`,
                method: 'POST',
                contentType: 'application/json',
                data: JSON.stringify({
                    phone: phone,
                    access_token: accessToken,
                    sessioncode: sessioncode
                }),
                success: function(response) {
                    if (response.status === 'success') {
                        let message = `Cookie延长成功！\n\n`;
                        message += `新的accesstoken已获取并保存到数据库\n`;

                        if (response.cookie_remaining_hours !== undefined) {
                            if (response.cookie_remaining_hours > 24) {
                                const days = Math.floor(response.cookie_remaining_hours / 24);
                                const hours = Math.floor(response.cookie_remaining_hours % 24);
                                message += `Cookie剩余有效期: ${days}天${hours}小时\n`;
                            } else if (response.cookie_remaining_hours >= 1) {
                                const hours = Math.floor(response.cookie_remaining_hours);
                                const minutes = Math.floor((response.cookie_remaining_hours % 1) * 60);
                                message += `Cookie剩余有效期: ${hours}小时${minutes}分钟\n`;
                            } else {
                                const minutes = Math.floor(response.cookie_remaining_hours * 60);
                                message += `Cookie剩余有效期: ${minutes}分钟\n`;
                            }
                        }

                        alert(message);

                        // 更新模态框中的Cookie状态显示（如果模态框是打开的）
                        if ($('#grabDeviceModal').hasClass('show')) {
                            $('#cookie-status').text('已登录');
                            if (response.cookie_remaining_hours !== undefined) {
                                updateCookieRemainingTime(response.cookie_remaining_hours);
                            }
                        }

                        // 刷新设备列表以获取最新状态
                        loadGrabDeviceList();
                    } else {
                        alert(`Cookie延长失败: ${response.message || response.error || '未知错误'}`);
                    }
                },
                error: function(xhr) {
                    let error = xhr.responseJSON?.error || xhr.statusText;
                    let message = `Cookie延长失败: ${error}`;

                    // 显示详细错误信息
                    if (xhr.responseJSON && xhr.responseJSON.details) {
                        message += `\n详细信息: ${xhr.responseJSON.details}`;
                    }

                    if (xhr.responseJSON && xhr.responseJSON.suggestion) {
                        message += `\n建议: ${xhr.responseJSON.suggestion}`;
                    }

                    alert(message);
                },
                complete: function() {
                    onComplete();
                }
            });
        }

        // 更新模态框中的Cookie状态显示
        function updateModalCookieStatus(response) {
            if (response.valid) {
                $('#cookie-status').text('已登录');
                if (response.cookie_remaining_hours !== undefined) {
                    updateCookieRemainingTime(response.cookie_remaining_hours);
                }
            } else {
                $('#cookie-status').text('Cookie已失效');
                $('#cookie-remaining-time').text('').removeClass('badge-success badge-warning badge-danger badge-info');
            }
        }

        // 调试Cookie状态
        function debugCookieStatus() {
            const deviceId = $('#grab-device-id').val();
            if (!deviceId) {
                alert('设备ID无效');
                return;
            }

            // 获取设备信息
            const device = grabDevices.find(d => d.id == deviceId);
            if (!device) {
                alert('找不到设备信息');
                return;
            }

            // 生成调试报告
            let debugInfo = `设备调试信息\n\n`;
            debugInfo += `设备ID: ${device.id}\n`;
            debugInfo += `用户名: ${device.username}\n`;
            debugInfo += `手机号: ${device.phone}\n`;
            debugInfo += `启用状态: ${device.enabled ? '启用' : '禁用'}\n`;
            debugInfo += `Access Token: ${device.access_token ? '有效 (长度: ' + device.access_token.length + ')' : '缺失'}\n`;
            debugInfo += `Session Code: ${device.sessioncode ? '有效 (' + device.sessioncode + ')' : '缺失'}\n`;

            if (device.cookie_remaining_hours !== undefined) {
                debugInfo += `Cookie剩余时间: ${device.cookie_remaining_hours.toFixed(2)} 小时\n`;
            } else {
                debugInfo += `Cookie剩余时间: 未知\n`;
            }

            debugInfo += `\n延长Cookie按钮状态:\n`;
            const canExtend = device.access_token && device.sessioncode;
            debugInfo += `可延长: ${canExtend ? '是' : '否'}\n`;

            if (!canExtend) {
                debugInfo += `原因: `;
                if (!device.access_token) debugInfo += `缺少access_token `;
                if (!device.sessioncode) debugInfo += `缺少sessioncode `;
                debugInfo += `\n`;
                debugInfo += `\n解决方案: 重新登录以获取完整的认证信息`;
            }

            alert(debugInfo);

            // 同时输出到控制台
            console.log('设备调试信息:', device);
        }

        // ===========================================
        // 申请记录查看功能
        // ===========================================

        // 查看申请记录
        function viewApplicationRecords(deviceId) {
            if (!deviceId) {
                alert('设备ID无效');
                return;
            }

            // 重置模态框内容
            $('#application-device-info').hide();
            $('#application-records-list').html(`
                <div class="text-center">
                    <div class="spinner-border" role="status">
                        <span class="sr-only">加载中...</span>
                    </div>
                    <p class="mt-2 text-muted">正在获取申请记录...</p>
                </div>
            `);

            // 显示模态框
            $('#applicationRecordsModal').modal('show');

            // 获取申请记录数据
            fetchApplicationRecords(deviceId);

            // 绑定刷新按钮事件
            $('#refresh-application-records-btn').off('click').on('click', function() {
                fetchApplicationRecords(deviceId);
            });
        }

        // 获取申请记录数据
        function fetchApplicationRecords(deviceId) {
            $.ajax({
                url: `/api/grab_devices/${deviceId}/application_records`,
                method: 'GET',
                success: function(response) {
                    if (response.status === 'success') {
                        // 更新设备信息
                        const deviceInfo = response.device_info;
                        $('#app-device-username').text(deviceInfo.username || '-');
                        $('#app-device-phone').text(deviceInfo.phone || '-');
                        $('#app-records-count').text(response.records.length);
                        $('#application-device-info').show();

                        // 渲染申请记录
                        renderApplicationRecords(response.records, response.pagination);
                    } else {
                        // 显示错误信息
                        const errorHtml = `
                            <div class="alert alert-danger">
                                <h6><i class="fa fa-exclamation-triangle"></i> 获取申请记录失败</h6>
                                <p class="mb-1"><strong>错误:</strong> ${response.error}</p>
                                ${response.suggestion ? `<p class="mb-0"><strong>建议:</strong> ${response.suggestion}</p>` : ''}
                            </div>
                        `;
                        $('#application-records-list').html(errorHtml);
                        $('#application-device-info').hide();
                    }
                },
                error: function(xhr) {
                    const error = xhr.responseJSON?.error || xhr.statusText;
                    const suggestion = xhr.responseJSON?.suggestion || '请稍后重试或联系管理员';

                    const errorHtml = `
                        <div class="alert alert-danger">
                            <h6><i class="fa fa-exclamation-triangle"></i> 请求失败</h6>
                            <p class="mb-1"><strong>错误:</strong> ${error}</p>
                            <p class="mb-0"><strong>建议:</strong> ${suggestion}</p>
                        </div>
                    `;
                    $('#application-records-list').html(errorHtml);
                    $('#application-device-info').hide();
                }
            });
        }

        // 渲染申请记录列表
        function renderApplicationRecords(records, pagination) {
            if (!records || records.length === 0) {
                $('#application-records-list').html(`
                    <div class="alert alert-info text-center">
                        <i class="fa fa-info-circle"></i> 该设备暂无申请记录
                    </div>
                `);
                return;
            }

            let html = '';

            // 分页信息
            if (pagination && pagination.total_count > 0) {
                html += `
                    <div class="alert alert-secondary mb-3">
                        <i class="fa fa-info-circle"></i>
                        共找到 <strong>${pagination.total_count}</strong> 条申请记录
                        ${pagination.page_count > 1 ? `，第 ${pagination.current_page}/${pagination.page_count} 页` : ''}
                    </div>
                `;
            }

            // 申请记录卡片
            records.forEach((record, index) => {
                html += renderSingleApplicationRecord(record, index);
            });

            $('#application-records-list').html(html);
        }

        // 渲染单个申请记录
        function renderSingleApplicationRecord(record, index) {
            // 格式化时间
            const formatTime = (timestamp) => {
                if (!timestamp) return '-';
                const date = new Date(timestamp);
                return date.toLocaleString('zh-CN');
            };

            // 格式化申请状态
            const getStatusBadge = (status) => {
                if (!status) return '<span class="badge badge-secondary">未知</span>';

                if (status.includes('通过')) {
                    return `<span class="badge badge-success">${status}</span>`;
                } else if (status.includes('失败') || status.includes('拒绝')) {
                    return `<span class="badge badge-danger">${status}</span>`;
                } else if (status.includes('审核') || status.includes('待')) {
                    return `<span class="badge badge-warning">${status}</span>`;
                } else {
                    return `<span class="badge badge-info">${status}</span>`;
                }
            };

            // 格式化分配状态
            const getAllotStatusBadge = (status) => {
                if (!status) return '<span class="badge badge-secondary">未知</span>';

                if (status.includes('合同')) {
                    return `<span class="badge badge-primary">${status}</span>`;
                } else if (status.includes('完成')) {
                    return `<span class="badge badge-success">${status}</span>`;
                } else {
                    return `<span class="badge badge-info">${status}</span>`;
                }
            };

            return `
                <div class="card mb-3">
                    <div class="card-header">
                        <div class="row align-items-center">
                            <div class="col-md-4">
                                <h6 class="mb-0">
                                    <i class="fa fa-id-card"></i> ${record.name || '未知姓名'}
                                </h6>
                                <small class="text-muted">申请编号: ${record.approveno || '-'}</small>
                            </div>
                            <div class="col-md-4 text-center">
                                <div class="mb-1">申请状态: ${getStatusBadge(record.status)}</div>
                                <div>分配状态: ${getAllotStatusBadge(record.allotlstatus)}</div>
                            </div>
                            <div class="col-md-4 text-right">
                                <small class="text-muted">
                                    申请时间: ${formatTime(record.created)}
                                </small>
                            </div>
                        </div>
                    </div>
                    <div class="card-body">
                        <div class="row">
                            <!-- 个人信息 -->
                            <div class="col-md-6">
                                <h6 class="text-primary"><i class="fa fa-user"></i> 个人信息</h6>
                                <table class="table table-sm table-borderless">
                                    <tr>
                                        <td class="text-muted" width="30%">手机号:</td>
                                        <td>${record.phone || '-'}</td>
                                    </tr>
                                    <tr>
                                        <td class="text-muted">身份证号:</td>
                                        <td>${record.cardno ? record.cardno.replace(/(\d{6})\d{8}(\d{4})/, '$1****$2') : '-'}</td>
                                    </tr>
                                    <tr>
                                        <td class="text-muted">学历:</td>
                                        <td>${record.education || '-'}</td>
                                    </tr>
                                    <tr>
                                        <td class="text-muted">婚姻状况:</td>
                                        <td>${record.marriage || '-'}</td>
                                    </tr>
                                    <tr>
                                        <td class="text-muted">人才类型:</td>
                                        <td>${record.group || '-'}</td>
                                    </tr>
                                    <tr>
                                        <td class="text-muted">工作单位:</td>
                                        <td>${record.company || '-'}</td>
                                    </tr>
                                </table>
                            </div>

                            <!-- 房源信息 -->
                            <div class="col-md-6">
                                <h6 class="text-success"><i class="fa fa-home"></i> 房源信息</h6>
                                <table class="table table-sm table-borderless">
                                    <tr>
                                        <td class="text-muted" width="30%">小区名称:</td>
                                        <td><strong>${record.houseestatename || '-'}</strong></td>
                                    </tr>
                                    <tr>
                                        <td class="text-muted">房源编号:</td>
                                        <td>${record.housecode || '-'}</td>
                                    </tr>
                                    <tr>
                                        <td class="text-muted">房间数:</td>
                                        <td>${record.room || '-'}</td>
                                    </tr>
                                    <tr>
                                        <td class="text-muted">厕所数:</td>
                                        <td>${record.tollet || '-'}</td>
                                    </tr>
                                    <tr>
                                        <td class="text-muted">租期:</td>
                                        <td>${record.leasetime ? record.leasetime + '年' : '-'}</td>
                                    </tr>
                                    <tr>
                                        <td class="text-muted">地址:</td>
                                        <td>${record.addr || '-'}</td>
                                    </tr>
                                </table>
                            </div>
                        </div>

                        ${record.remark ? `
                            <div class="row mt-3">
                                <div class="col-12">
                                    <h6 class="text-info"><i class="fa fa-comment"></i> 备注</h6>
                                    <div class="alert alert-light">
                                        ${record.remark}
                                    </div>
                                </div>
                            </div>
                        ` : ''}

                        ${record.appointtime ? `
                            <div class="row mt-2">
                                <div class="col-12">
                                    <small class="text-muted">
                                        <i class="fa fa-calendar"></i> 预约时间: ${formatTime(record.appointtime)}
                                    </small>
                                </div>
                            </div>
                        ` : ''}
                    </div>
                </div>
            `;
        }

        // ===========================================
        // 用户主页查看功能
        // ===========================================

        // 查看用户主页
        function viewUserProfile(deviceId) {
            if (!deviceId) {
                alert('设备ID无效');
                return;
            }

            // 重置模态框内容
            $('#profile-device-info').hide();
            $('#user-profile-content').html(`
                <div class="text-center">
                    <div class="spinner-border" role="status">
                        <span class="sr-only">加载中...</span>
                    </div>
                    <p class="mt-2 text-muted">正在获取用户主页信息...</p>
                </div>
            `);

            // 显示模态框
            $('#userProfileModal').modal('show');

            // 获取用户主页数据
            fetchUserProfile(deviceId);

            // 绑定刷新按钮事件
            $('#refresh-user-profile-btn').off('click').on('click', function() {
                fetchUserProfile(deviceId);
            });
        }

        // 获取用户主页数据
        function fetchUserProfile(deviceId) {
            $.ajax({
                url: `/api/grab_devices/${deviceId}/user_profile`,
                method: 'GET',
                success: function(response) {
                    if (response.status === 'success') {
                        // 更新设备信息
                        const deviceInfo = response.device_info;
                        $('#profile-device-username').text(deviceInfo.username || '-');
                        $('#profile-device-phone').text(deviceInfo.phone || '-');
                        $('#profile-fetch-time').text(new Date().toLocaleString('zh-CN'));
                        $('#profile-device-info').show();

                        // 渲染用户主页信息
                        renderUserProfile(response.profile);
                    } else {
                        // 显示错误信息
                        const errorHtml = `
                            <div class="alert alert-danger">
                                <h6><i class="fa fa-exclamation-triangle"></i> 获取用户主页信息失败</h6>
                                <p class="mb-1"><strong>错误:</strong> ${response.error}</p>
                                ${response.suggestion ? `<p class="mb-0"><strong>建议:</strong> ${response.suggestion}</p>` : ''}
                            </div>
                        `;
                        $('#user-profile-content').html(errorHtml);
                        $('#profile-device-info').hide();
                    }
                },
                error: function(xhr) {
                    const error = xhr.responseJSON?.error || xhr.statusText;
                    const suggestion = xhr.responseJSON?.suggestion || '请稍后重试或联系管理员';

                    const errorHtml = `
                        <div class="alert alert-danger">
                            <h6><i class="fa fa-exclamation-triangle"></i> 请求失败</h6>
                            <p class="mb-1"><strong>错误:</strong> ${error}</p>
                            <p class="mb-0"><strong>建议:</strong> ${suggestion}</p>
                        </div>
                    `;
                    $('#user-profile-content').html(errorHtml);
                    $('#profile-device-info').hide();
                }
            });
        }

        // 渲染用户主页信息
        function renderUserProfile(profile) {
            if (!profile) {
                $('#user-profile-content').html(`
                    <div class="alert alert-info text-center">
                        <i class="fa fa-info-circle"></i> 暂无用户主页信息
                    </div>
                `);
                return;
            }

            // 格式化时间
            const formatTime = (timestamp) => {
                if (!timestamp) return '-';
                const date = new Date(timestamp);
                return date.toLocaleString('zh-CN');
            };

            // 格式化认证状态
            const getVerifyStatusBadge = (status) => {
                if (!status) return '<span class="badge badge-secondary">未知</span>';

                if (status.includes('通过')) {
                    return `<span class="badge badge-success">${status}</span>`;
                } else if (status.includes('失败') || status.includes('拒绝')) {
                    return `<span class="badge badge-danger">${status}</span>`;
                } else if (status.includes('审核') || status.includes('待')) {
                    return `<span class="badge badge-warning">${status}</span>`;
                } else {
                    return `<span class="badge badge-info">${status}</span>`;
                }
            };

            // 格式化状态
            const getStatusBadge = (status) => {
                if (status === 'true') {
                    return '<span class="badge badge-success">正常</span>';
                } else if (status === 'false') {
                    return '<span class="badge badge-danger">禁用</span>';
                } else {
                    return `<span class="badge badge-secondary">${status || '未知'}</span>`;
                }
            };

            // 格式化身份证号（脱敏）
            const formatCardNo = (cardno) => {
                if (!cardno) return '-';
                return cardno.replace(/(\d{6})\d{8}(\d{4})/, '$1****$2');
            };

            // 处理证件照片
            const formatCardImages = (cardImages) => {
                if (!cardImages || cardImages.length === 0) return '未上传';

                return cardImages.map(img => {
                    const imageUrl = `https://www.huhhothome.cn${img.path}`;
                    return `<a href="${imageUrl}" target="_blank" class="badge badge-primary mr-1">${img.name}</a>`;
                }).join('');
            };

            const html = `
                <div class="row">
                    <!-- 基本信息 -->
                    <div class="col-md-6">
                        <div class="card h-100">
                            <div class="card-header bg-primary text-white">
                                <h6 class="mb-0"><i class="fa fa-user"></i> 基本信息</h6>
                            </div>
                            <div class="card-body">
                                <table class="table table-sm table-borderless">
                                    <tr>
                                        <td class="text-muted font-weight-bold" width="30%">姓名:</td>
                                        <td><strong>${profile.name || '-'}</strong></td>
                                    </tr>
                                    <tr>
                                        <td class="text-muted font-weight-bold">性别:</td>
                                        <td>${profile.sex || '-'}</td>
                                    </tr>
                                    <tr>
                                        <td class="text-muted font-weight-bold">手机号:</td>
                                        <td>${profile.phone || '-'}</td>
                                    </tr>
                                    <tr>
                                        <td class="text-muted font-weight-bold">邮箱:</td>
                                        <td>${profile.email || '-'}</td>
                                    </tr>
                                    <tr>
                                        <td class="text-muted font-weight-bold">固定电话:</td>
                                        <td>${profile.tel || '-'}</td>
                                    </tr>
                                    <tr>
                                        <td class="text-muted font-weight-bold">用户类型:</td>
                                        <td>${profile.types || '-'}</td>
                                    </tr>
                                    <tr>
                                        <td class="text-muted font-weight-bold">账户状态:</td>
                                        <td>${getStatusBadge(profile.status)}</td>
                                    </tr>
                                </table>
                            </div>
                        </div>
                    </div>

                    <!-- 身份信息 -->
                    <div class="col-md-6">
                        <div class="card h-100">
                            <div class="card-header bg-success text-white">
                                <h6 class="mb-0"><i class="fa fa-id-card"></i> 身份信息</h6>
                            </div>
                            <div class="card-body">
                                <table class="table table-sm table-borderless">
                                    <tr>
                                        <td class="text-muted font-weight-bold" width="30%">证件类型:</td>
                                        <td>${profile.cardtype || '-'}</td>
                                    </tr>
                                    <tr>
                                        <td class="text-muted font-weight-bold">证件号码:</td>
                                        <td>${formatCardNo(profile.cardno)}</td>
                                    </tr>
                                    <tr>
                                        <td class="text-muted font-weight-bold">证件地址:</td>
                                        <td>${profile.cardaddr || '-'}</td>
                                    </tr>
                                    <tr>
                                        <td class="text-muted font-weight-bold">现住址:</td>
                                        <td>${profile.addr || '-'}</td>
                                    </tr>
                                    <tr>
                                        <td class="text-muted font-weight-bold">认证状态:</td>
                                        <td>${getVerifyStatusBadge(profile.vstatus)}</td>
                                    </tr>
                                    <tr>
                                        <td class="text-muted font-weight-bold">认证方式:</td>
                                        <td>${profile.vstatustype || '-'}</td>
                                    </tr>
                                    <tr>
                                        <td class="text-muted font-weight-bold">注册类型:</td>
                                        <td>${profile.regtype || '-'}</td>
                                    </tr>
                                </table>
                            </div>
                        </div>
                    </div>

                    <!-- 工作信息 -->
                    <div class="col-md-6 mt-3">
                        <div class="card h-100">
                            <div class="card-header bg-info text-white">
                                <h6 class="mb-0"><i class="fa fa-building"></i> 工作信息</h6>
                            </div>
                            <div class="card-body">
                                <table class="table table-sm table-borderless">
                                    <tr>
                                        <td class="text-muted font-weight-bold" width="30%">工作单位:</td>
                                        <td>${profile.workname || '-'}</td>
                                    </tr>
                                    <tr>
                                        <td class="text-muted font-weight-bold">公司ID:</td>
                                        <td>${profile.companyid || '-'}</td>
                                    </tr>
                                    <tr>
                                        <td class="text-muted font-weight-bold">管理部门:</td>
                                        <td>${profile.managerdept || '-'}</td>
                                    </tr>
                                </table>
                            </div>
                        </div>
                    </div>

                    <!-- 证件照片 -->
                    <div class="col-md-6 mt-3">
                        <div class="card h-100">
                            <div class="card-header bg-warning text-white">
                                <h6 class="mb-0"><i class="fa fa-image"></i> 证件照片</h6>
                            </div>
                            <div class="card-body">
                                <table class="table table-sm table-borderless">
                                    <tr>
                                        <td class="text-muted font-weight-bold" width="30%">身份证正面:</td>
                                        <td>${formatCardImages(profile.cardt)}</td>
                                    </tr>
                                    <tr>
                                        <td class="text-muted font-weight-bold">身份证反面:</td>
                                        <td>${formatCardImages(profile.cardb)}</td>
                                    </tr>
                                </table>
                            </div>
                        </div>
                    </div>

                    <!-- 系统信息 -->
                    <div class="col-md-12 mt-3">
                        <div class="card">
                            <div class="card-header bg-dark text-white">
                                <h6 class="mb-0"><i class="fa fa-cogs"></i> 系统信息</h6>
                            </div>
                            <div class="card-body">
                                <div class="row">
                                    <div class="col-md-4">
                                        <table class="table table-sm table-borderless">
                                            <tr>
                                                <td class="text-muted font-weight-bold" width="40%">表单ID:</td>
                                                <td><small>${profile.formid || '-'}</small></td>
                                            </tr>
                                            <tr>
                                                <td class="text-muted font-weight-bold">应用ID:</td>
                                                <td><small>${profile.applicationid || '-'}</small></td>
                                            </tr>
                                            <tr>
                                                <td class="text-muted font-weight-bold">域ID:</td>
                                                <td><small>${profile.domainid || '-'}</small></td>
                                            </tr>
                                        </table>
                                    </div>
                                    <div class="col-md-4">
                                        <table class="table table-sm table-borderless">
                                            <tr>
                                                <td class="text-muted font-weight-bold" width="40%">创建时间:</td>
                                                <td><small>${formatTime(profile.created)}</small></td>
                                            </tr>
                                            <tr>
                                                <td class="text-muted font-weight-bold">修改时间:</td>
                                                <td><small>${formatTime(profile.lastmodified)}</small></td>
                                            </tr>
                                            <tr>
                                                <td class="text-muted font-weight-bold">表单名称:</td>
                                                <td><small>${profile.formname || '-'}</small></td>
                                            </tr>
                                        </table>
                                    </div>
                                    <div class="col-md-4">
                                        <table class="table table-sm table-borderless">
                                            <tr>
                                                <td class="text-muted font-weight-bold" width="40%">会话代码:</td>
                                                <td><small>${profile.sessioncode ? profile.sessioncode.substring(0, 20) + '...' : '-'}</small></td>
                                            </tr>
                                            <tr>
                                                <td class="text-muted font-weight-bold">微信OpenID:</td>
                                                <td><small>${profile.weixinopenid ? profile.weixinopenid.substring(0, 15) + '...' : '-'}</small></td>
                                            </tr>
                                            <tr>
                                                <td class="text-muted font-weight-bold">支付宝OpenID:</td>
                                                <td><small>${profile.aliopenid || '-'}</small></td>
                                            </tr>
                                        </table>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            `;

            $('#user-profile-content').html(html);
        }

        // ===========================================
        // 设备房源关联关系检测功能
        // ===========================================

        // 检测设备房源关联关系
        function checkDeviceRelations() {
            toggleLoading(true);
            const button = $('#check-device-relations-btn');
            const originalText = button.html();
            button.prop('disabled', true).html('<i class="fa fa-spin fa-spinner"></i> 检测中...');

            $.ajax({
                url: '/api/device_house_relations',
                method: 'GET',
                success: function(response) {
                    toggleLoading(false);
                    button.prop('disabled', false).html(originalText);

                    if (response.status === 'success') {
                        showDeviceRelationsModal(response);
                    } else {
                        alert('检测失败: ' + (response.error || '未知错误'));
                    }
                },
                error: function(xhr) {
                    toggleLoading(false);
                    button.prop('disabled', false).html(originalText);
                    alert('检测设备关联关系失败: ' + (xhr.responseJSON?.error || xhr.statusText));
                }
            });
        }

        // 显示设备关联关系检测结果模态框
        function showDeviceRelationsModal(data) {
            // 更新统计信息
            $('#total-devices-count').text(data.stats.total_devices);
            $('#linked-devices-count').text(data.stats.linked_devices);
            $('#orphaned-devices-count').text(data.stats.orphaned_devices);
            $('#total-relations-count').text(data.stats.total_relations);

            // 更新徽章
            $('#orphaned-badge').text(data.stats.orphaned_devices);
            $('#relations-badge').text(data.stats.total_relations);

            // 显示问题（如果有）
            if (data.issues && data.issues.length > 0) {
                let issuesHtml = '<ul class="mb-0">';
                data.issues.forEach(issue => {
                    let iconClass = issue.severity === 'high' ? 'fa-exclamation-circle text-danger' :
                                   issue.severity === 'medium' ? 'fa-exclamation-triangle text-warning' :
                                   'fa-info-circle text-info';
                    issuesHtml += `<li><i class="fa ${iconClass}"></i> ${issue.message}</li>`;
                });
                issuesHtml += '</ul>';
                $('#issues-list').html(issuesHtml);
                $('#relations-issues').show();
            } else {
                $('#relations-issues').hide();
            }

            // 渲染孤立设备
            renderOrphanedDevices(data.orphaned_devices, data.orphaned_by_type);

            // 渲染关联关系
            renderRelationsList(data.relations);

            // 渲染按房源分组
            renderRelationsByHouse(data.relations_by_house);

            // 绑定事件
            bindRelationsModalEvents(data);

            // 显示模态框
            $('#deviceRelationsModal').modal('show');
        }

        // 渲染孤立设备
        function renderOrphanedDevices(orphanedDevices, orphanedByType) {
            let html = '';

            if (orphanedDevices.length === 0) {
                html = '<div class="alert alert-success"><i class="fa fa-check"></i> 所有设备都已正确关联到房源！</div>';
                $('#fix-orphaned-btn').hide();
            } else {
                html = '<div class="alert alert-warning mb-3">';
                html += `<i class="fa fa-exclamation-triangle"></i> 发现 ${orphanedDevices.length} 个孤立设备，这些设备未关联到任何房源，无法接收推送通知。`;
                html += '</div>';

                // 按类型分组显示
                Object.keys(orphanedByType).forEach(deviceType => {
                    const devices = orphanedByType[deviceType];
                    const typeName = deviceType === 'bark' ? 'Bark' :
                                   deviceType === 'wxpush' ? '微信推送' :
                                   deviceType === 'pushme' ? 'PushMe' : deviceType;

                    html += `<h6 class="mt-3 mb-2 text-primary">${typeName}设备 (${devices.length}个)</h6>`;
                    html += '<div class="row">';

                    devices.forEach(device => {
                        let expireInfo = '';
                        if (device.expire_date) {
                            const today = new Date();
                            const expireDate = new Date(device.expire_date);
                            const isExpired = expireDate < today;
                            expireInfo = isExpired ?
                                '<small class="text-danger">(已过期)</small>' :
                                '<small class="text-muted">(过期: ' + device.expire_date + ')</small>';
                        }

                        html += `
                            <div class="col-md-6 mb-2">
                                <div class="card card-body py-2">
                                    <div class="d-flex justify-content-between align-items-center">
                                        <div>
                                            <strong>${device.name}</strong> ${expireInfo}
                                            <br><small class="text-muted">ID: ${device.id}</small>
                                        </div>
                                        <div class="custom-control custom-checkbox">
                                            <input type="checkbox" class="custom-control-input orphaned-device-checkbox"
                                                   id="orphaned-${device.id}" value="${device.id}">
                                            <label class="custom-control-label" for="orphaned-${device.id}"></label>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        `;
                    });

                    html += '</div>';
                });

                $('#fix-orphaned-btn').show();
            }

            $('#orphaned-devices-list').html(html);
        }

        // 渲染关联关系列表
        function renderRelationsList(relations) {
            let html = '';

            if (relations.length === 0) {
                html = '<div class="alert alert-info"><i class="fa fa-info-circle"></i> 暂无设备关联关系</div>';
            } else {
                html = '<div class="table-responsive">';
                html += '<table class="table table-sm table-bordered">';
                html += '<thead class="thead-light">';
                html += '<tr><th>设备名称</th><th>设备类型</th><th>房源名称</th><th>房源状态</th><th>设备过期时间</th><th>状态</th></tr>';
                html += '</thead><tbody>';

                relations.forEach(relation => {
                    let deviceStatusClass = '';
                    let deviceStatus = '正常';

                    // 检查设备过期状态
                    if (relation.device_expire_date) {
                        const today = new Date();
                        const expireDate = new Date(relation.device_expire_date);
                        if (expireDate < today) {
                            deviceStatusClass = 'text-danger';
                            deviceStatus = '已过期';
                        }
                    }

                    // 检查房源状态
                    const houseStatusClass = relation.house_enabled ? 'text-success' : 'text-warning';
                    const houseStatus = relation.house_enabled ? '已启用' : '已禁用';

                    const typeText = relation.device_type === 'bark' ? 'Bark' :
                                   relation.device_type === 'wxpush' ? '微信推送' :
                                   relation.device_type === 'pushme' ? 'PushMe' : relation.device_type;

                    html += `
                        <tr>
                            <td><strong>${relation.device_name}</strong></td>
                            <td><span class="badge badge-info">${typeText}</span></td>
                            <td><strong>${relation.house_name}</strong></td>
                            <td><span class="${houseStatusClass}">${houseStatus}</span></td>
                            <td>${relation.device_expire_date || '无'}</td>
                            <td><span class="${deviceStatusClass}">${deviceStatus}</span></td>
                        </tr>
                    `;
                });

                html += '</tbody></table></div>';
            }

            $('#relations-list').html(html);
        }

        // 渲染按房源分组的关联关系
        function renderRelationsByHouse(relationsByHouse) {
            let html = '';

            if (Object.keys(relationsByHouse).length === 0) {
                html = '<div class="alert alert-info"><i class="fa fa-info-circle"></i> 暂无房源关联设备</div>';
            } else {
                Object.keys(relationsByHouse).forEach(houseName => {
                    const houseRelations = relationsByHouse[houseName];
                    const enabledCount = houseRelations.filter(r => r.house_enabled).length;

                    html += `
                        <div class="card mb-3">
                            <div class="card-header d-flex justify-content-between align-items-center">
                                <h6 class="mb-0">${houseName}</h6>
                                <span class="badge badge-primary">${houseRelations.length} 个设备</span>
                            </div>
                            <div class="card-body">
                                <div class="row">
                    `;

                    houseRelations.forEach(relation => {
                        let deviceStatusClass = '';
                        let statusBadge = '';

                        if (relation.device_expire_date) {
                            const today = new Date();
                            const expireDate = new Date(relation.device_expire_date);
                            if (expireDate < today) {
                                deviceStatusClass = 'border-danger';
                                statusBadge = '<span class="badge badge-danger">已过期</span>';
                            }
                        }

                        if (!relation.house_enabled) {
                            statusBadge += ' <span class="badge badge-warning">房源已禁用</span>';
                        }

                        const typeText = relation.device_type === 'bark' ? 'Bark' :
                                       relation.device_type === 'wxpush' ? '微信推送' :
                                       relation.device_type === 'pushme' ? 'PushMe' : relation.device_type;

                        html += `
                            <div class="col-md-6 mb-2">
                                <div class="card card-body py-2 ${deviceStatusClass}">
                                    <div class="d-flex justify-content-between align-items-center">
                                        <div>
                                            <strong>${relation.device_name}</strong>
                                            <span class="badge badge-info badge-sm ml-1">${typeText}</span>
                                            <br><small class="text-muted">过期: ${relation.device_expire_date || '无'}</small>
                                        </div>
                                        <div>
                                            ${statusBadge}
                                        </div>
                                    </div>
                                </div>
                            </div>
                        `;
                    });

                    html += '</div></div></div>';
                });
            }

            $('#relations-by-house-list').html(html);
        }

        // 绑定关联关系模态框事件
        function bindRelationsModalEvents(data) {
            // 刷新检测按钮
            $('#refresh-relations-btn').off('click').on('click', function() {
                $('#deviceRelationsModal').modal('hide');
                checkDeviceRelations();
            });

            // 修复孤立设备按钮
            $('#fix-orphaned-btn').off('click').on('click', function() {
                showFixOrphanedModal(data.orphaned_devices);
            });
        }

        // 显示修复孤立设备模态框
        function showFixOrphanedModal(orphanedDevices) {
            // 加载房源列表 - 直接使用全局的houses变量（从monitor_configs表获取）
            let options = '<option value="">请选择房源</option>';

            if (houses && houses.length > 0) {
                houses.forEach(house => {
                    const deviceCount = house.device_ids ? house.device_ids.length : 0;
                    const enabledText = house.enabled ? '已启用' : '已禁用';
                    options += `<option value="${house.name}">${house.name} (${deviceCount} 个设备, ${enabledText})</option>`;
                });
            } else {
                // 如果全局houses为空，尝试从API获取
                console.warn('全局houses为空，尝试重新加载配置');
                loadConfig(); // 重新加载配置
                options += '<option value="">正在加载房源...</option>';
            }

            $('#target-house-select').html(options);

            // 渲染孤立设备选择器
            let html = '<div class="mb-2">';
            html += '<button type="button" class="btn btn-sm btn-outline-primary mr-2" id="select-all-orphaned">全选</button>';
            html += '<button type="button" class="btn btn-sm btn-outline-secondary" id="deselect-all-orphaned">取消全选</button>';
            html += '</div>';

            orphanedDevices.forEach(device => {
                let expireInfo = '';
                if (device.expire_date) {
                    const today = new Date();
                    const expireDate = new Date(device.expire_date);
                    const isExpired = expireDate < today;
                    expireInfo = isExpired ?
                        '<small class="text-danger">(已过期)</small>' :
                        '<small class="text-muted">(过期: ' + device.expire_date + ')</small>';
                }

                const typeText = device.type === 'bark' ? 'Bark' :
                               device.type === 'wxpush' ? '微信推送' :
                               device.type === 'pushme' ? 'PushMe' : device.type;

                html += `
                    <div class="custom-control custom-checkbox mb-2">
                        <input type="checkbox" class="custom-control-input fix-orphaned-device-checkbox"
                               id="fix-${device.id}" value="${device.id}">
                        <label class="custom-control-label" for="fix-${device.id}">
                            <strong>${device.name}</strong>
                            <span class="badge badge-info">${typeText}</span>
                            ${expireInfo}
                        </label>
                    </div>
                `;
            });

            $('#orphaned-devices-selector').html(html);

            // 绑定全选/取消全选事件
            $('#select-all-orphaned').off('click').on('click', function() {
                $('.fix-orphaned-device-checkbox').prop('checked', true);
            });

            $('#deselect-all-orphaned').off('click').on('click', function() {
                $('.fix-orphaned-device-checkbox').prop('checked', false);
            });

            // 绑定确认按钮事件
            $('#confirm-fix-orphaned-btn').off('click').on('click', fixOrphanedDevices);

            // 显示模态框
            $('#fixOrphanedModal').modal('show');
        }

        // 执行修复孤立设备
        function fixOrphanedDevices() {
            const targetHouse = $('#target-house-select').val();
            if (!targetHouse) {
                alert('请选择目标房源');
                return;
            }

            const selectedDeviceIds = [];
            $('.fix-orphaned-device-checkbox:checked').each(function() {
                selectedDeviceIds.push($(this).val());
            });

            if (selectedDeviceIds.length === 0) {
                alert('请至少选择一个设备');
                return;
            }

            if (!confirm(`确定要将选中的 ${selectedDeviceIds.length} 个设备关联到房源 "${targetHouse}" 吗？`)) {
                return;
            }

            toggleLoading(true);

            $.ajax({
                url: '/api/device_house_relations/fix_orphaned',
                method: 'POST',
                contentType: 'application/json',
                data: JSON.stringify({
                    device_ids: selectedDeviceIds,
                    house_name: targetHouse
                }),
                success: function(response) {
                    toggleLoading(false);
                    if (response.status === 'success') {
                        alert(response.message);
                        $('#fixOrphanedModal').modal('hide');
                        $('#deviceRelationsModal').modal('hide');

                        // 刷新设备列表和房源列表
                        loadConfig();
                    } else {
                        alert('修复失败: ' + (response.error || '未知错误'));
                    }
                },
                error: function(xhr) {
                    toggleLoading(false);
                    alert('修复孤立设备失败: ' + (xhr.responseJSON?.error || xhr.statusText));
                }
            });
        }
    </script>
</body>
</html>