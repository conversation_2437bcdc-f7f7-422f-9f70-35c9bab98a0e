"""
独立监控服务核心逻辑 - 从原Application类重构而来
移除了Flask依赖，保持核心监控功能
"""

import asyncio
import sys
import os
import signal
import platform
import time
from typing import Dict, Any, List

# 添加项目根目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__)))))

from config.unified_config import get_config
from core.utils.unified_logging import Logger
from core.data.history_manager import HistoryManager
from core.network.proxy_manager import ProxyManager
from core.network.http_client import HttpClient
from core.network.api_client import ApiClient
from core.network.optimized_connector import connector_factory
from core.monitor.state_manager import StateManager
from core.monitor.house_monitor import HouseMonitor
from services.notification.manager import NotificationManager


class MonitorService:
    """独立监控服务核心类，负责整个监控任务的生命周期管理"""

    def __init__(self):
        """初始化监控服务"""
        # 使用标准Logger，不再依赖外部logger
        self.logger = Logger("MonitorService")

        # 使用统一配置管理器
        self.unified_config = get_config()
        self.config = self.unified_config.get_all_config()

        # 获取数据管理器（用于历史记录）
        from core.data.manager import get_data_manager
        self.data_manager = get_data_manager()

        # 创建历史管理器包装器（保持向后兼容）
        class DatabaseHistoryWrapper:
            def __init__(self, data_manager, logger):
                self.data_manager = data_manager
                self.logger = logger

            def get_history(self):
                return self.data_manager.get_history()

            def update_history(self, name, count):
                return self.data_manager.update_history(name, count)

            def get_house_count(self, name):
                """获取房源的房屋数量"""
                history = self.data_manager.get_history()
                return history.get(name, 0)

            def set_house_count(self, name, count):
                """设置房源的房屋数量"""
                return self.data_manager.update_history(name, count)

            def save_to_file(self):
                """保存历史数据到文件（在数据库模式下，这个方法不做任何事情）"""
                return True

            def get_estate_ids(self):
                return self.data_manager.get_estate_ids()

            def set_estate_id(self, name, estate_id):
                return self.data_manager.set_estate_id(name, estate_id)

            def get_known_houses(self, estate_name=None):
                """获取已知房源ID列表"""
                if estate_name:
                    return self.data_manager.get_known_houses().get(estate_name, set())
                return self.data_manager.get_known_houses()

            def add_known_house(self, estate_name, house_id):
                return self.data_manager.add_known_house(estate_name, house_id)

            def set_known_houses(self, estate_name: str, house_ids: set):
                return self.data_manager.set_known_houses(estate_name, list(house_ids))

            def update_house_details_from_list(self, name: str, details: List):
                """从房源详情列表更新房源详情数据"""
                if details:
                    # 转换为字典列表
                    details_dict_list = []
                    for detail in details:
                        if hasattr(detail, 'to_dict'):
                            details_dict_list.append(detail.to_dict())
                        else:
                            details_dict_list.append(detail)

                    # 保存到数据库
                    self.data_manager.save_house_details(name, details_dict_list)

                    # 同时更新已知房源ID
                    house_ids = [detail.id for detail in details if hasattr(detail, 'id') and detail.id]
                    self.data_manager.set_known_houses(name, house_ids)

                    self.logger.debug(f"已更新 {name} 的房源详情 ({len(details)} 个)")
                return True

            def reset_house_details(self, name: str, details: List):
                """重置房源详情数据"""
                # 先清除旧数据
                self.data_manager.clear_house_details(name)

                # 添加新数据
                if details:
                    details_dict_list = []
                    for detail in details:
                        if hasattr(detail, 'to_dict'):
                            details_dict_list.append(detail.to_dict())
                        else:
                            details_dict_list.append(detail)

                    self.data_manager.save_house_details(name, details_dict_list)

                    house_ids = [detail.id for detail in details if hasattr(detail, 'id') and detail.id]
                    self.data_manager.set_known_houses(name, house_ids)
                else:
                    self.data_manager.set_known_houses(name, [])

                self.logger.debug(f"已重置 {name} 的房源详情 ({len(details) if details else 0} 个)")
                return True

            def get_house_details(self, estate_name: str) -> List:
                """获取房源详情列表"""
                details_dict_list = self.data_manager.get_house_details(estate_name)

                from core.data.models import HouseDetail
                details = []
                for detail_dict in details_dict_list:
                    details.append(HouseDetail.from_dict(detail_dict))

                return details

        self.history_manager = DatabaseHistoryWrapper(self.data_manager, self.logger)
        self.state_manager = StateManager(self.logger)

        # 初始化网络组件
        proxy_api_url = self.config.get('proxy_api_url')
        self.proxy_manager = ProxyManager(proxy_api_url, self.logger)
        self.http_client = HttpClient(self.proxy_manager, self.logger)

        # 设置代理管理器和HTTP客户端的双向引用
        self.proxy_manager.set_http_client(self.http_client)

        # 确保配置中包含设备列表
        if 'device_list' not in self.config:
            self.config['device_list'] = self.data_manager.get_devices()
            self.logger.info(f"从数据管理器加载了 {len(self.config['device_list'])} 个设备到配置中")

        # 初始化通知管理器
        self.notification_manager = NotificationManager(self.http_client, self.logger, self.config, self.data_manager)

        # 初始化API客户端
        base_url = self.config.get('base_url')
        common_params = self.config.get('common_params')
        self.api_client = ApiClient(self.http_client, base_url, common_params, self.proxy_manager, self.logger,
                                  self.notification_manager, self.config)

        # 初始化抢房设备管理器
        from core.monitor.house_monitor import GrabDeviceManager
        self.grab_device_manager = GrabDeviceManager(
            self.data_manager.grab_device_model,
            self.logger
        )

        # 初始化文件上传缓存管理器
        try:
            from cache.upload_cache import FileUploadCache
            self.cache_manager = FileUploadCache("cache/uploads")
            self.logger.info("✅ 文件上传缓存管理器初始化成功")
        except ImportError:
            self.cache_manager = None
            self.logger.warning(" 缓存模块不可用，将使用传统上传模式")
        except Exception as e:
            self.cache_manager = None
            self.logger.warning(f" 缓存管理器初始化失败: {e}，将使用传统上传模式")

        # 初始化房源监控器
        self.house_monitor = HouseMonitor(
            self.api_client,
            self.history_manager,
            self.notification_manager,
            self.logger,
            self.grab_device_manager,
            self.proxy_manager,
            self.config
        )

        # 将缓存管理器传递给房源监控器
        if self.cache_manager:
            self.house_monitor.cache_manager = self.cache_manager

        self.logger.info("监控服务初始化完成")

    def load_config(self) -> bool:
        """重新从统一配置管理器加载配置并更新相关组件"""
        try:
            old_config = self.config.copy()

            # 重新加载统一配置
            self.unified_config.reload()
            self.config = self.unified_config.get_all_config()

            # 检查关键配置是否发生变化
            old_interval = old_config.get('check_interval', 60)
            new_interval = self.config.get('check_interval', 60)
            old_proxy_url = old_config.get('proxy_api_url')
            new_proxy_url = self.config.get('proxy_api_url')

            # 记录配置变更
            if old_interval != new_interval:
                self.logger.info(f"检查间隔已更新: {old_interval}秒 -> {new_interval}秒")

            # 更新相关组件的配置
            if hasattr(self, 'notification_manager') and hasattr(self.notification_manager, 'update_config'):
                if 'device_list' not in self.config:
                    self.config['device_list'] = self.data_manager.get_devices()
                    self.logger.info(f"重新加载配置时，从数据管理器加载了 {len(self.config['device_list'])} 个设备到配置中")

                self.notification_manager.update_config(self.config)

            # 更新代理管理器配置
            if hasattr(self, 'proxy_manager') and old_proxy_url != new_proxy_url:
                self.logger.info(f"代理API URL已更新: {old_proxy_url} -> {new_proxy_url}")
                self.proxy_manager.api_url = new_proxy_url
                # 在后台线程中更新代理，避免阻塞主线程
                import threading
                threading.Thread(target=self.proxy_manager.update_proxy_sync, daemon=True).start()

            self.logger.info("配置已成功重新加载")
            return True

        except Exception as e:
            self.logger.error(f"重新加载配置失败: {str(e)}")
            return False

    def setup_signal_handlers(self):
        """设置信号处理器，用于优雅退出"""
        try:
            # 尝试使用asyncio信号处理器（仅在Unix/Linux系统上可用）
            loop = asyncio.get_running_loop()

            for sig in (signal.SIGINT, signal.SIGTERM):
                loop.add_signal_handler(sig, lambda s=sig: asyncio.create_task(self.shutdown(f"Signal {s}")))

            self.logger.info("信号处理器设置完成 (asyncio模式)")

        except NotImplementedError:
            # Windows系统不支持asyncio信号处理器，使用备用方案
            current_platform = platform.system()
            self.logger.warning(f"当前平台 ({current_platform}) 不支持asyncio信号处理器，使用备用信号处理方案")

            # 为Windows系统设置基本的信号处理
            if current_platform == "Windows":
                def signal_handler(signum, frame):
                    """Windows平台信号处理器"""
                    self.logger.info(f"收到信号 {signum}，请求优雅关闭")
                    if hasattr(self, 'state_manager'):
                        self.state_manager.stop()

                # 注册信号处理器
                signal.signal(signal.SIGINT, signal_handler)
                signal.signal(signal.SIGTERM, signal_handler)

                self.logger.info("Windows信号处理器设置完成")
            else:
                self.logger.warning("无法在当前平台设置信号处理器")

        except Exception as e:
            # 处理其他可能的异常
            self.logger.error(f"设置信号处理器时发生错误: {str(e)}")
            self.logger.warning("系统将继续运行，但可能无法优雅处理退出信号")

    async def _monitor_cycle(self):
        """执行一个完整的监控周期"""
        if self.state_manager.is_stop_requested():
            return

        self.state_manager.record_cycle_start()

        # 更新代理
        if self.proxy_manager.is_proxy_expired():
            if self.state_manager.is_stop_requested():
                return
            # 只在代理确实需要更新时才输出日志
            if not self.proxy_manager.current_proxy:
                self.logger.info("当前无可用代理，正在获取新代理...")
            else:
                self.logger.debug(f"代理 {self.proxy_manager.current_proxy} 可能已过期，尝试更新...")
            await self.proxy_manager.update_proxy_async()

        # 再次检查停止信号
        if self.state_manager.is_stop_requested():
            return

        # 获取启用的房源配置
        monitor_configs = self.data_manager.get_enabled_monitor_configs()
        if not monitor_configs:
            self.logger.warning("没有启用的房源配置，跳过此轮监控")
            return

        # 并发执行监控任务，但允许中断
        tasks = [self.house_monitor.monitor_async(conf) for conf in monitor_configs]

        try:
            results = await asyncio.gather(*tasks, return_exceptions=True)
        except asyncio.CancelledError:
            self.logger.info("监控周期被取消")
            return

        # 检查是否有代理相关异常，并触发刷新（由底层的锁机制确保单次执行）
        for r in results:
            if isinstance(r, Exception):
                exception_type = type(r).__name__
                if self.proxy_manager._is_proxy_related_exception(exception_type, str(r)):
                    self.logger.warning(f"检测到代理相关异常 {exception_type}，将尝试刷新代理。")
                    # 创建一个任务去刷新代理，不阻塞当前流程
                    # 底层的锁机制会防止多个任务同时刷新
                    asyncio.create_task(self.proxy_manager.update_proxy_async(force=True))
                    # 只要检测到一个代理问题，就触发一次刷新尝试，然后跳出循环
                    break

        # 处理任务结果
        has_errors = any(isinstance(r, Exception) for r in results)
        self.state_manager.record_cycle_end(success=not has_errors)

    async def run(self):
        """启动并运行监控服务"""
        if self.state_manager.is_running:
            self.logger.warning("监控服务已在运行中")
            return

        self.state_manager.start()
        self.setup_signal_handlers()
        self.logger.info("监控服务已启动")

        # 首次运行时，建立所有基线
        await self.build_all_baselines()

        while not self.state_manager.is_stop_requested():
            await self._monitor_cycle()

            # 等待下一次轮询
            interval = self.config.get('check_interval', 60)
            self.logger.info(f"等待 {interval} 秒后开始下一轮...")

            # 分段等待，以便及时响应停止请求
            for _ in range(interval):
                if self.state_manager.is_stop_requested():
                    break
                try:
                    await asyncio.sleep(1)
                except asyncio.CancelledError:
                    break

        # 主循环结束，执行清理
        await self.shutdown("main_loop")

    async def shutdown(self, signal_name: str = "signal"):
        """优雅地关闭监控服务"""
        if not self.state_manager.start_shutdown():
            return

        self.logger.warning(f"收到停止信号 ({signal_name})，开始优雅退出...")

        # 停止监控循环
        self.state_manager.stop()

        # 关闭网络连接，采用分阶段清理策略
        cleanup_start_time = time.time()
        try:
            # 第一阶段：关闭通知服务
            if hasattr(self, 'notification_manager'):
                self.logger.info("正在关闭通知服务...")
                await self.notification_manager.close_service()
                # 等待通知服务完全关闭
                await asyncio.sleep(0.1)

            # 第二阶段：关闭HTTP客户端会话
            if hasattr(self, 'http_client'):
                self.logger.info("正在关闭HTTP客户端会话...")
                await self.http_client.close_all()
                # 等待HTTP客户端完全关闭
                await asyncio.sleep(0.1)

            # 第三阶段：关闭所有缓存的连接器和会话，解决aiohttp资源泄漏问题
            self.logger.info("正在关闭所有缓存的连接器...")
            await connector_factory.close_all_cached_connectors()

            # 最终等待，确保所有异步清理完成
            await asyncio.sleep(0.3)

            cleanup_duration = time.time() - cleanup_start_time
            self.logger.info(f"网络服务清理完成，耗时 {cleanup_duration:.2f} 秒")

        except Exception as e:
            cleanup_duration = time.time() - cleanup_start_time
            # 使用统一异常处理
            from core.exceptions import handle_exception
            handled_e = handle_exception(e, context="关闭网络服务", logger=self.logger)
            self.logger.error(f"关闭网络服务时出错(耗时 {cleanup_duration:.2f} 秒): {handled_e}")

        # 执行同步清理操作
        try:
            if hasattr(self, 'history_manager'):
                self.history_manager.save_to_file()

            self.logger.info("配置由统一配置管理器自动管理，无需手动保存")
        except Exception as e:
            self.logger.error(f"保存数据时出错: {str(e)}")

        self.logger.info("监控服务优雅关闭完成")

    async def build_all_baselines(self):
        """为所有启用的房源建立基线"""
        self.logger.info("开始为所有房源建立/更新基线...")

        monitor_configs = self.data_manager.get_enabled_monitor_configs()

        tasks = []
        for config in monitor_configs:
            name = config.get("name")
            if self.history_manager.get_house_count(name) is None:
                 tasks.append(self.house_monitor.monitor_async(config))

        if tasks:
            await asyncio.gather(*tasks)
            self.logger.info("所有新房源基线建立完成")
        else:
            self.logger.info("没有需要建立基线的新房源")

    # API服务器可能需要的额外方法
    def get_status(self) -> Dict[str, Any]:
        """获取监控服务状态信息"""
        proxy_info = {}
        if hasattr(self, 'proxy_manager'):
            proxy_info = {
                'current_proxy': self.proxy_manager.current_proxy,
                'last_fetch_time': self.proxy_manager.last_fetch_time,
                'remaining_count': self.proxy_manager.remaining_count
            }

        return {
            'is_running': self.state_manager.is_running if hasattr(self, 'state_manager') else False,
            'proxy': proxy_info,
            'config_interval': self.config.get('check_interval', 60),
            'total_cycles': getattr(self.state_manager, 'cycle_count', 0) if hasattr(self, 'state_manager') else 0
        }