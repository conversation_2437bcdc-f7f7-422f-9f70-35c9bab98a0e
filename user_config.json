{"web_auth": {"username": "92easy", "password_hash": "e671c632cf1914529a04c81fb3ef3417be430b163361790b2ce008c4de05ca6b", "secret_key": "c661a9bf5872fdf5170837248100b9be"}, "admin_notifier": {"device_ids": ["device1745334492891"], "enabled": true, "notification_templates": {"cookie_expiring": {"include_device_info": true, "include_sms_form_link": true, "title": "🔐 抢房设备Cookie即将过期"}, "login_success": {"include_cookie_info": true, "title": "✅ 抢房设备Cookie延长成功"}}}, "api_error_monitoring": {"consecutive_error_threshold": 500, "error_duration_threshold": 200, "notification_interval": 60, "recovery_notification": true}, "base_url": "https://www.huhhothome.cn/api/dynamicapi/apiview/viewdata", "check_interval": 1, "common_params": {"_localversion": "", "apiview": "houseEstate", "application": "jqHj7ddxI1smOEkmKSD", "domainId": "LAMDyAh4HSdnug1KdKL"}, "database": {"file": "monitoring.db"}, "development": {"debug_mode": true, "hot_reload": true, "verbose_logging": true}, "estate_ids": {}, "history": {}, "known_houses": {}, "logging": {"date_format": "%Y-%m-%d %H:%M:%S", "enable_file_logging": true, "enable_masking": true, "file_log_level": "INFO", "file_rotation": {"backup_count": 30, "interval": 1, "when": "midnight"}, "format": "%(asctime)s,%(msecs)03d - %(name)s - %(levelname)s - %(message)s", "level": "INFO", "log_dir": "log", "log_file_name": "monitor", "use_colors": true}, "max_retries": 5, "monitor_service": {"health_url": "http://127.0.0.1:8089/health", "host": "127.0.0.1", "max_startup_wait": 30, "port": 8089, "script": "start_monitor_service.py", "startup_delay": 3, "health_path": "/health"}, "monitor_service_url": "http://127.0.0.1:8089", "proxy_api_url": "http://api1.ydaili.cn/tools/MeasureApi.ashx?action=EAPI&secret=75A9B01A7021F22592E35707C1119B43894970A47AB6E989117D57F2420081E6E8A82FBB158ACD6B&number=1&orderId=SH20230412183940319&format=json&province=150000", "schedule": {"tasks": {"monitor_task": {"days": [0, 1, 2, 3, 4, 5, 6], "enabled": false, "start_time": "14:48", "stop_time": "14:46"}}}, "service_management": {"check_interval": 5, "health_check_timeout": 3}, "web_service": {"depends_on": "monitor", "health_url": "http://127.0.0.1:50011/", "host": "0.0.0.0", "max_startup_wait": 20, "port": 50011, "script": "web_control_new.py", "startup_delay": 2, "health_path": "/"}}