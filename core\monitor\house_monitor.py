"""房源监控器实现"""

from typing import Dict, Any, Optional, Set, List
import asyncio
from datetime import datetime
import re

from .base import BaseMonitor
from core.data.models import MonitorResult, HouseDetail, ChangeType
from core.network.api_client import ApiClient
from core.data.history_manager import HistoryManager
from services.notification.manager import NotificationManager
from core.utils.unified_logging import Logger


class HouseMonitor(BaseMonitor):
    """房源监控器，负责监控单个房源的变化"""

    def __init__(self, api_client: ApiClient, history_manager: HistoryManager,
                 notification_manager: NotificationManager, logger: Logger,
                 grab_device_manager=None, proxy_manager=None, config=None):
        self.api_client = api_client
        self.history_manager = history_manager
        self.notification_manager = notification_manager
        self.logger = logger
        self.grab_device_manager = grab_device_manager
        self.proxy_manager = proxy_manager
        self.config = config or {}  # 添加配置参数

    def validate_config(self, config: Dict[str, Any]) -> tuple[bool, str]:
        if not config.get('name'):
            return False, "房源配置缺少 'name' 字段"
        return True, ""

    async def monitor_async(self, config: Dict[str, Any]) -> Optional[MonitorResult]:
        """执行单个房源的监控"""
        is_valid, error = self.validate_config(config)
        if not is_valid:
            self.logger.error(f"无效的房源配置: {error}", context=config)
            return None

        name = config["name"]
        device_ids = config.get("device_ids", [])

        # 确保房源名称是UTF-8编码的字符串
        try:
            if isinstance(name, str):
                # 测试UTF-8编码，如果失败则进行转换
                name.encode('utf-8')
            else:
                name = str(name)
        except UnicodeEncodeError:
            # 如果编码失败，使用安全转换
            safe_chars = []
            for char in name:
                try:
                    char.encode('utf-8')
                    safe_chars.append(char)
                except UnicodeEncodeError:
                    safe_chars.append(f'\\u{ord(char):04x}')
            name = ''.join(safe_chars)

        try:
            # 1. 获取最新房源数量
            new_count, estate_id = await self.api_client.fetch_house_count_async(name)
            if new_count is None:
                return MonitorResult(house_name=name, old_count=-1, new_count=-1, error_message="获取房源数量失败")

            # 2. 获取历史数量
            old_count = self.history_manager.get_house_count(name)
            if old_count is None:
                self.logger.info(f"[{name}] 首次监控，初始化数量为: {new_count}")
                self.history_manager.set_house_count(name, new_count)
                self.history_manager.set_estate_id(name, estate_id)
                # 首次运行时建立基线
                await self._build_baseline(name, estate_id)
                self.history_manager.save_to_file()
                return MonitorResult(house_name=name, old_count=0, new_count=new_count)

            # 3. 对比数量变化
            if new_count == old_count:
                self.logger.info(f"[{name}] 当前数量: {new_count} (无变化)")
                return MonitorResult(house_name=name, old_count=old_count, new_count=new_count)

            self.logger.info(f"[{name}] 变化检测: {old_count} → {new_count}")
            result = MonitorResult(house_name=name, old_count=old_count, new_count=new_count, estate_id=estate_id)

            # 4. 如果数量增加，获取详情并推送
            if result.change_type == ChangeType.INCREASE:
                # 先推送简单的变化通知
                await self.notification_manager.send_notification_for_result(result, device_ids)

                # 然后获取详情并推送详情通知
                details = await self.api_client.fetch_house_details_async(estate_id)
                if details:
                    new_houses = self._identify_new_houses(name, details)
                    if new_houses:
                        # 创建包含详情的结果对象用于详情推送
                        detail_result = MonitorResult(
                            house_name=name,
                            old_count=old_count,
                            new_count=new_count,
                            estate_id=estate_id,
                            details=new_houses
                        )
                        await self.notification_manager.send_notification_for_result(detail_result, device_ids)

                        # 检查并执行抢房逻辑（不影响通知设备）
                        await self._process_grab_devices(name, new_houses)

            # 5. 如果数量减少，直接推送
            elif result.change_type == ChangeType.DECREASE:
                await self._update_baseline_after_decrease(name, estate_id)
                await self.notification_manager.send_notification_for_result(result, device_ids)

            # 6. 更新历史记录
            self.history_manager.set_house_count(name, new_count)
            self.history_manager.set_estate_id(name, estate_id)
            self.history_manager.save_to_file()

            return result

        except Exception as e:
            self.logger.error(f"监控房源 [{name}] 时出错: {e}", context=e)
            return MonitorResult(house_name=name, old_count=-1, new_count=-1, error_message=str(e))

    async def _process_grab_devices(self, estate_name: str, new_houses: List[HouseDetail]):
        """处理抢房设备逻辑"""
        if not self.grab_device_manager or not new_houses:
            return

        try:
            # 获取该小区的所有启用的抢房设备
            grab_devices = self.grab_device_manager.get_devices_by_estate(estate_name)
            if not grab_devices:
                return

            self.logger.info(f"[{estate_name}] 发现 {len(grab_devices)} 个抢房设备和 {len(new_houses)} 个新房源，开始进行匹配...")

            # 复制一份待处理房源列表，并打乱设备列表顺序以实现公平分配
            available_houses = list(new_houses)
            import random
            random.shuffle(grab_devices)

            assigned_tasks = []

            # 当还有可用房源和可用设备时，进行匹配
            while available_houses and grab_devices:
                device = grab_devices.pop(0) # 取出一个设备

                if not device.get('enabled') or not device.get('cookie'):
                    self.logger.debug(f"跳过设备 {device.get('username', '未知')}: 未启用或无Cookie")
                    continue

                # 筛选当前设备可以抢的、尚未被分配的房源
                matching_houses = [
                    house for house in available_houses
                    if self._check_grab_conditions(device, house)
                ]

                if not matching_houses:
                    self.logger.info(f"[{estate_name}] 设备 {device['username']} 在剩余房源中无匹配项")
                    continue

                self.logger.info(f"[{estate_name}] 设备 {device['username']} 匹配到 {len(matching_houses)} 个可用房源")

                # 随机选择一个房源进行分配
                selected_house = self._select_random_house(matching_houses)
                self.logger.info(f"[{estate_name}] 分配 房源 {selected_house.id} 给 设备 {device['username']}，准备抢房...")

                # 记录分配任务
                assigned_tasks.append(
                    self._execute_grab_house(device, selected_house, estate_name)
                )

                # 将已分配的房源从列表中移除
                available_houses.remove(selected_house)

            # 如果还有剩余房源但没有设备了，记录日志
            if available_houses and not grab_devices:
                self.logger.warning(f"[{estate_name}] 还有 {len(available_houses)} 个房源未处理，但已无可用抢房设备。")

            # 并发执行所有已分配的抢房任务
            if assigned_tasks:
                self.logger.info(f"[{estate_name}] 共分配了 {len(assigned_tasks)} 个抢房任务，开始并发执行...")
                await asyncio.gather(*assigned_tasks)
                self.logger.info(f"[{estate_name}] 所有抢房任务执行完毕。")

        except Exception as e:
            self.logger.error(f"处理抢房设备时出错: {e}")

    def _check_grab_conditions(self, device: Dict[str, Any], house: HouseDetail) -> bool:
        """检查房源是否符合抢房条件"""
        conditions = device.get('conditions', [])
        username = device.get('username', '未知用户')

        # 如果没有设置条件，则匹配所有房源
        if not conditions:
            self.logger.info(f"设备 {username} 未设置抢房条件，匹配所有房源")
            return True

        self.logger.info(f"设备 {username} 开始检查房源 {house.id} 是否符合抢房条件...")
        self.logger.info(f"房源详情: 位置={house.position}, 房间号={house.roomno}, 朝向={house.direction}, 面积={house.area}㎡, 租金={house.rent}元, 类型={house.outtype}")

        matched_conditions = 0
        total_conditions = len(conditions)

        for i, condition in enumerate(conditions, 1):
            # 适配两种数据结构：数据库中的condition_type/condition_value 和 API中的type/value
            condition_type = condition.get('type') or condition.get('condition_type')
            condition_value = (condition.get('value') or condition.get('condition_value', '')).strip()

            if not condition_type or not condition_value:
                self.logger.debug(f"设备 {username} 条件{i}: 跳过空条件")
                continue

            self.logger.info(f"设备 {username} 检查条件{i}: {condition_type} = '{condition_value}'")

            if self._evaluate_condition(condition_type, condition_value, house):
                matched_conditions += 1
                self.logger.info(f"设备 {username} 条件{i}: ✅ 匹配成功")
            else:
                self.logger.info(f"设备 {username} 条件{i}: ❌ 匹配失败")
                self.logger.info(f"设备 {username} 房源 {house.id} 不符合抢房条件，原因: {condition_type}='{condition_value}' 不匹配")
                return False

        self.logger.info(f"设备 {username} 条件检查完成: {matched_conditions}/{total_conditions} 条件匹配，房源 {house.id} 符合抢房条件 ✅")
        return True

    def _evaluate_condition(self, condition_type: str, condition_value: str, house: HouseDetail) -> bool:
        """评估单个条件是否匹配"""
        try:
            if condition_type == 'area':
                house_area = float(house.area) if house.area else 0
                return self._evaluate_numeric_condition(condition_value, house_area)

            elif condition_type == 'rent':
                house_rent = float(house.rent) if house.rent else 0
                return self._evaluate_numeric_condition(condition_value, house_rent)

            elif condition_type == 'direction':
                house_direction = (house.direction or '').lower()
                # 使用逗号或竖线作为分隔符，支持多朝向选择
                # 例如: "南,东南" 或 "东|西"
                if ',' in condition_value or '|' in condition_value:
                    accepted_directions = [d.strip().lower() for d in re.split('[,|]', condition_value) if d.strip()]
                    for direction in accepted_directions:
                        if direction in house_direction:
                            return True  # 只要匹配到一个就返回True
                    return False  # 如果所有多选朝向都不匹配
                else:
                    # 保持原来的单值匹配逻辑（精确或包含）
                    single_direction = condition_value.lower()
                    return single_direction == house_direction or single_direction in house_direction

            elif condition_type == 'building':
                # 智能楼号匹配
                return self._match_building(condition_value, house.position or '')

            elif condition_type == 'floor':
                # 智能楼层匹配
                return self._match_floor(condition_value, house.position or '')

            elif condition_type == 'roomno':
                house_roomno = house.roomno or ''
                return condition_value.lower() in house_roomno.lower()

            elif condition_type == 'outtype':
                house_outtype = house.outtype or ''
                return condition_value.lower() in house_outtype.lower()

            return False

        except Exception as e:
            self.logger.warning(f"评估条件时出错: {e}")
            return False

    def _evaluate_numeric_condition(self, condition_value: str, house_value: float) -> bool:
        """评估数值条件"""
        condition_value = condition_value.strip()

        if condition_value.startswith('>='):
            return house_value >= float(condition_value[2:])
        elif condition_value.startswith('<='):
            return house_value <= float(condition_value[2:])
        elif condition_value.startswith('>'):
            return house_value > float(condition_value[1:])
        elif condition_value.startswith('<'):
            return house_value < float(condition_value[1:])
        elif condition_value.startswith('='):
            return house_value == float(condition_value[1:])
        else:
            # 默认等于
            return house_value == float(condition_value)

    def _parse_position(self, position: str) -> dict:
        """解析position字段，提取结构化信息

        支持格式：
        - 1号楼_C单元_15层
        - C座_-_10层
        - A栋_B单元_5楼
        - 2号楼_15层
        """
        import re

        result = {
            'building': '',
            'unit': '',
            'floor': '',
            'raw': position
        }

        if not position or position.strip() == '':
            return result

        # 使用下划线分割
        parts = position.split('_')

        for part in parts:
            part = part.strip()
            if not part or part == '-':
                continue

            # 检查是否是楼号
            if re.search(r'\d+号楼|\d+栋|[A-Z]座|[A-Z]栋', part, re.IGNORECASE):
                result['building'] = part
            # 检查是否是单元
            elif re.search(r'[A-Z]单元|\d+单元', part, re.IGNORECASE):
                result['unit'] = part
            # 检查是否是楼层
            elif re.search(r'\d+层|\d+楼|\d+F', part, re.IGNORECASE):
                result['floor'] = part

        return result

    def _extract_building_info(self, building_text: str) -> dict:
        """提取楼号信息"""
        import re

        result = {
            'number': '',
            'type': '',
            'letter': '',
            'raw': building_text
        }

        if not building_text:
            return result

        building_text = building_text.strip()

        # 匹配数字楼号：1号楼、2栋等
        number_match = re.search(r'(\d+)(号楼|栋)', building_text, re.IGNORECASE)
        if number_match:
            result['number'] = number_match.group(1)
            result['type'] = number_match.group(2)
            return result

        # 匹配字母楼号：A座、B栋等
        letter_match = re.search(r'([A-Z])(座|栋)', building_text, re.IGNORECASE)
        if letter_match:
            result['letter'] = letter_match.group(1).upper()
            result['type'] = letter_match.group(2)
            return result

        return result

    def _extract_floor_info(self, floor_text: str) -> dict:
        """提取楼层信息"""
        import re

        result = {
            'number': '',
            'type': '',
            'raw': floor_text
        }

        if not floor_text:
            return result

        floor_text = floor_text.strip()

        # 匹配楼层：15层、10楼、5F等
        floor_match = re.search(r'(\d+)(层|楼|F)', floor_text, re.IGNORECASE)
        if floor_match:
            result['number'] = floor_match.group(1)
            result['type'] = floor_match.group(2)
            return result

        return result

    def _extract_floor_number(self, floor_text: str) -> int:
        """从楼层文本中提取数字"""
        import re

        if not floor_text:
            return 0

        floor_text = floor_text.strip()

        # 匹配数字
        number_match = re.search(r'(\d+)', floor_text)
        if number_match:
            return int(number_match.group(1))

        return 0

    def _parse_floor_range(self, condition_value: str) -> dict:
        """解析楼层区间条件

        支持格式：
        - 5-15 (5到15层)
        - 8~20 (8到20层)
        - 10..25 (10到25层)
        """
        import re

        result = {
            'is_range': False,
            'min_floor': None,
            'max_floor': None,
            'operator': None,
            'value': None
        }

        condition_value = condition_value.strip()

        # 检查是否是区间条件
        range_patterns = [
            r'(\d+)-(\d+)',      # 5-15
            r'(\d+)~(\d+)',      # 8~20
            r'(\d+)\.\.(\d+)',   # 10..25
            r'(\d+)\s*到\s*(\d+)', # 5到15
        ]

        for pattern in range_patterns:
            match = re.search(pattern, condition_value)
            if match:
                result['is_range'] = True
                result['min_floor'] = int(match.group(1))
                result['max_floor'] = int(match.group(2))
                return result

        # 检查是否是比较操作符
        if condition_value.startswith('>='):
            result['operator'] = '>='
            result['value'] = int(condition_value[2:])
        elif condition_value.startswith('<='):
            result['operator'] = '<='
            result['value'] = int(condition_value[2:])
        elif condition_value.startswith('>'):
            result['operator'] = '>'
            result['value'] = int(condition_value[1:])
        elif condition_value.startswith('<'):
            result['operator'] = '<'
            result['value'] = int(condition_value[1:])
        elif condition_value.startswith('='):
            result['operator'] = '='
            result['value'] = int(condition_value[1:])
        elif condition_value.isdigit():
            result['operator'] = '='
            result['value'] = int(condition_value)

        return result

    def _match_building(self, condition_value: str, house_position: str) -> bool:
        """智能楼号匹配，支持数值比较"""
        try:
            condition_value = condition_value.strip()

            # 解析房源位置信息
            position_info = self._parse_position(house_position)
            house_building = position_info.get('building', '')

            self.logger.info(f"楼号匹配: 条件='{condition_value}', 房源位置='{house_position}', 提取楼号='{house_building}'")

            if not house_building:
                # 如果没有提取到楼号信息，尝试原始匹配
                result = condition_value.lower() in house_position.lower()
                self.logger.info(f"楼号匹配(原始): {result}")
                return result

            # 提取条件中的楼号信息
            condition_building_info = self._extract_building_info(condition_value)
            house_building_info = self._extract_building_info(house_building)

            # 检查是否是数值比较条件（如：>2、<=5等）
            if condition_value.startswith(('>', '<', '>=', '<=')):
                house_building_number = int(house_building_info.get('number', 0)) if house_building_info.get('number') else 0
                if house_building_number > 0:
                    # 解析数值比较条件
                    building_condition = self._parse_floor_range(condition_value)  # 复用楼层的解析逻辑
                    if building_condition.get('operator'):
                        operator = building_condition['operator']
                        value = building_condition['value']

                        if operator == '>':
                            result = house_building_number > value
                        elif operator == '>=':
                            result = house_building_number >= value
                        elif operator == '<':
                            result = house_building_number < value
                        elif operator == '<=':
                            result = house_building_number <= value
                        else:
                            result = False

                        self.logger.info(f"楼号匹配(数值比较): {result} - {house_building_number} {operator} {value}")
                        return result

            # 策略1：精确匹配
            if condition_value.lower() == house_building.lower():
                self.logger.info(f"楼号匹配(精确): True")
                return True

            # 策略2：数字楼号匹配
            if (condition_building_info.get('number') and house_building_info.get('number') and
                condition_building_info['number'] == house_building_info['number']):
                self.logger.info(f"楼号匹配(数字): True - {condition_building_info['number']} == {house_building_info['number']}")
                return True

            # 策略3：字母楼号匹配
            if (condition_building_info.get('letter') and house_building_info.get('letter') and
                condition_building_info['letter'] == house_building_info['letter']):
                self.logger.info(f"楼号匹配(字母): True - {condition_building_info['letter']} == {house_building_info['letter']}")
                return True

            # 策略4：包含匹配（向后兼容）
            if condition_value.lower() in house_building.lower():
                self.logger.info(f"楼号匹配(包含): True")
                return True

            # 策略5：简化数字匹配（如：用户输入"1"匹配"1号楼"）
            if condition_value.isdigit() and house_building_info.get('number') == condition_value:
                self.logger.info(f"楼号匹配(简化数字): True - {condition_value} == {house_building_info['number']}")
                return True

            # 策略6：简化字母匹配（如：用户输入"A"匹配"A座"）
            if (len(condition_value) == 1 and condition_value.isalpha() and
                house_building_info.get('letter') == condition_value.upper()):
                self.logger.info(f"楼号匹配(简化字母): True - {condition_value.upper()} == {house_building_info['letter']}")
                return True

            self.logger.info(f"楼号匹配: False - 所有策略均未匹配")
            return False

        except Exception as e:
            self.logger.warning(f"楼号匹配过程出错: {e}")
            # 发生异常时回退到原始匹配
            return condition_value.lower() in house_position.lower()

    def _match_floor(self, condition_value: str, house_position: str) -> bool:
        """智能楼层匹配，支持数值比较和区间匹配"""
        try:
            condition_value = condition_value.strip()

            # 解析房源位置信息
            position_info = self._parse_position(house_position)
            house_floor = position_info.get('floor', '')

            self.logger.info(f"楼层匹配: 条件='{condition_value}', 房源位置='{house_position}', 提取楼层='{house_floor}'")

            if not house_floor:
                # 如果没有提取到楼层信息，尝试原始匹配
                result = condition_value.lower() in house_position.lower()
                self.logger.info(f"楼层匹配(原始): {result}")
                return result

            # 提取房源楼层数字
            house_floor_number = self._extract_floor_number(house_floor)
            self.logger.info(f"楼层匹配: 房源楼层数字={house_floor_number}")

            # 解析条件（支持数值比较和区间）
            floor_condition = self._parse_floor_range(condition_value)

            # 策略1：区间匹配
            if floor_condition.get('is_range'):
                min_floor = floor_condition['min_floor']
                max_floor = floor_condition['max_floor']
                result = min_floor <= house_floor_number <= max_floor
                self.logger.info(f"楼层匹配(区间): {result} - {house_floor_number} 在 [{min_floor}, {max_floor}] 范围内")
                return result

            # 策略2：数值比较匹配
            elif floor_condition.get('operator'):
                operator = floor_condition['operator']
                value = floor_condition['value']

                if operator == '>':
                    result = house_floor_number > value
                elif operator == '>=':
                    result = house_floor_number >= value
                elif operator == '<':
                    result = house_floor_number < value
                elif operator == '<=':
                    result = house_floor_number <= value
                elif operator == '=':
                    result = house_floor_number == value
                else:
                    result = False

                self.logger.info(f"楼层匹配(数值比较): {result} - {house_floor_number} {operator} {value}")
                return result

            # 以下为原有策略，保持向后兼容
            condition_floor_info = self._extract_floor_info(condition_value)
            house_floor_info = self._extract_floor_info(house_floor)

            # 策略3：精确匹配
            if condition_value.lower() == house_floor.lower():
                self.logger.info(f"楼层匹配(精确): True")
                return True

            # 策略4：数字楼层匹配
            if (condition_floor_info.get('number') and house_floor_info.get('number') and
                condition_floor_info['number'] == house_floor_info['number']):
                self.logger.info(f"楼层匹配(数字): True - {condition_floor_info['number']} == {house_floor_info['number']}")
                return True

            # 策略5：包含匹配（向后兼容）
            if condition_value.lower() in house_floor.lower():
                self.logger.info(f"楼层匹配(包含): True")
                return True

            # 策略6：简化数字匹配（如：用户输入"15"匹配"15层"）
            if condition_value.isdigit() and house_floor_info.get('number') == condition_value:
                self.logger.info(f"楼层匹配(简化数字): True - {condition_value} == {house_floor_info['number']}")
                return True

            # 策略7：楼层同义词匹配（层=楼=F）
            if (condition_floor_info.get('number') and house_floor_info.get('number') and
                condition_floor_info['number'] == house_floor_info['number']):
                # 数字相同，类型可能不同（15层 vs 15楼 vs 15F）
                self.logger.info(f"楼层匹配(同义词): True - {condition_floor_info['number']} == {house_floor_info['number']}")
                return True

            self.logger.info(f"楼层匹配: False - 所有策略均未匹配")
            return False

        except Exception as e:
            self.logger.warning(f"楼层匹配过程出错: {e}")
            # 发生异常时回退到原始匹配
            return condition_value.lower() in house_position.lower()

    def _select_random_house(self, houses: List[HouseDetail]) -> HouseDetail:
        """从符合条件的房源列表中随机选择一个"""
        import random
        return random.choice(houses)

    async def _execute_grab_house(self, device: Dict[str, Any], house: HouseDetail, estate_name: str):
        """执行抢房操作"""
        try:
            # 记录抢房开始时间和完整房源详情
            start_time = datetime.now()
            username = device.get('username', '未知用户')

            self.logger.info(f"========== 开始抢房流程 ==========")
            self.logger.info(f"抢房用户: {username}")
            self.logger.info(f"抢房时间: {start_time.strftime('%Y-%m-%d %H:%M:%S')}")
            self.logger.info(f"目标小区: {estate_name}")
            self.logger.info(f"目标房源ID: {house.id}")

            # 记录完整房源详情
            self.logger.info(f"========== 房源详细信息 ==========")
            self.logger.info(f"房源名称: {house.name or '未知'}")
            self.logger.info(f"房源位置: {house.position or '未知'}")
            self.logger.info(f"房间号: {house.roomno or '未知'}")
            self.logger.info(f"房型: {house.outtype or '未知'}")
            self.logger.info(f"朝向: {house.direction or '未知'}")
            self.logger.info(f"面积: {house.area}㎡")
            self.logger.info(f"月租金: {house.rent}元")
            self.logger.info(f"房源完整信息: {house.name or '未知'} - {house.position or ''} - {house.roomno or ''}")
            self.logger.info(f"=====================================")

            # 使用GrabExecutor执行实际的抢房逻辑
            grab_result = await self._call_grab_api(device, house, estate_name)

            # 记录抢房结果详情
            end_time = datetime.now()
            duration = (end_time - start_time).total_seconds()

            self.logger.info(f"========== 抢房流程结果 ==========")
            self.logger.info(f"抢房结束时间: {end_time.strftime('%Y-%m-%d %H:%M:%S')}")
            self.logger.info(f"抢房总耗时: {duration:.2f}秒")
            self.logger.info(f"抢房结果: {'成功' if grab_result.get('success') else '失败'}")
            self.logger.info(f"结果消息: {grab_result.get('message', '未知')}")
            if grab_result.get('details'):
                self.logger.info(f"结果详情: {grab_result.get('details')}")
            self.logger.info(f"===================================")

            # 发送抢房结果通知给管理员（包含耗时信息）
            await self._send_grab_result_notification(device, house, estate_name, grab_result, duration)

            if grab_result.get('success'):
                self.logger.info(f"✅ 用户 {username} 成功抢到房源 {house.id} (耗时: {duration:.2f}秒)")
            else:
                self.logger.warning(f"❌ 用户 {username} 抢房失败: {grab_result.get('message', '未知错误')} (耗时: {duration:.2f}秒)")

        except Exception as e:
            # 记录异常详情
            error_time = datetime.now()
            error_duration = (error_time - start_time).total_seconds()
            username = device.get('username', '未知用户')

            self.logger.error(f"========== 抢房流程异常 ==========")
            self.logger.error(f"异常时间: {error_time.strftime('%Y-%m-%d %H:%M:%S')}")
            self.logger.error(f"抢房用户: {username}")
            self.logger.error(f"目标房源: {house.id} ({house.name or '未知'})")
            self.logger.error(f"异常信息: {str(e)}")
            self.logger.error(f"异常类型: {type(e).__name__}")
            self.logger.error(f"异常耗时: {error_duration:.2f}秒")
            self.logger.error(f"=================================")

            # 发送异常通知给管理员（包含耗时信息）
            error_result = {
                'success': False,
                'message': f'抢房执行异常: {str(e)}',
                'details': f'用户: {username}, 房源: {house.id} ({house.name or "未知"}), 异常类型: {type(e).__name__}'
            }
            await self._send_grab_result_notification(device, house, estate_name, error_result, error_duration)

    async def _call_grab_api(self, device: Dict[str, Any], house: HouseDetail, estate_name: str) -> Dict[str, Any]:
        """调用实际的抢房API"""
        try:
            # 导入GrabExecutor
            from core.grab.grab_executor import GrabExecutor

            # 获取当前活跃的代理配置
            proxy_config = None
            if self.proxy_manager and self.proxy_manager.current_proxy:
                proxy_ip_port = self.proxy_manager.current_proxy
                proxy_config = {
                    'http': f'http://{proxy_ip_port}',
                    'https': f'http://{proxy_ip_port}'
                }
                self.logger.info(f"抢房流程将使用代理: {proxy_ip_port}")
            else:
                self.logger.warning("未获取到代理配置，抢房流程将直连")

            # 获取缓存管理器（如果可用）
            cache_manager = getattr(self, 'cache_manager', None)

            # 创建抢房执行器，传递代理配置、代理管理器和缓存管理器，默认禁用调试以提升性能
            grab_executor = GrabExecutor(
                logger=self.logger,
                proxy_config=proxy_config,
                enable_debug=False,
                proxy_manager=self.proxy_manager,
                cache_manager=cache_manager
            )

            # 执行抢房
            result = await grab_executor.execute_grab(device, house.id, estate_name)
            return result

        except ImportError:
            self.logger.error("无法导入GrabExecutor，请检查grab_executor.py文件")
            return {
                'success': False,
                'message': '抢房模块不可用',
                'details': '无法导入GrabExecutor'
            }
        except Exception as e:
            self.logger.error(f"调用抢房API时出错: {e}")
            return {
                'success': False,
                'message': f'抢房API调用失败: {str(e)}',
                'details': f'房源ID: {house.id}'
            }

    def _extract_specific_error_message(self, grab_result: Dict[str, Any]) -> str:
        """提取具体的错误信息，优先显示具体原因而非通用错误"""
        try:
            message = grab_result.get('message', '')
            details = grab_result.get('details', '')

            # 定义具体错误模式，按优先级排序
            specific_error_patterns = [
                # 实名认证相关（最高优先级）
                '您的账号暂没有实名认证，或实名认证未通过！',
                '实名认证未通过',
                '暂没有实名认证',

                # 其他具体业务错误
                '房源已被其他用户申请',
                '申请时间已过',
                '申请条件不符',
                '材料审核未通过',
                '余额不足',
                '账号异常',
                '网络连接失败',
                'SSL证书验证失败',
                '服务器错误',
                '连接超时',
            ]

            # 首先在 message 中查找具体错误
            for pattern in specific_error_patterns:
                if pattern in message:
                    self.logger.debug(f"在message中找到具体错误: {pattern}")
                    return pattern

            # 然后在 details 中查找具体错误
            for pattern in specific_error_patterns:
                if pattern in details:
                    self.logger.debug(f"在details中找到具体错误: {pattern}")
                    return pattern

            # 尝试从message中提取冒号后的内容（如："抢房申请提交失败: 具体原因"）
            if ':' in message:
                parts = message.split(':', 1)
                if len(parts) > 1:
                    specific_part = parts[1].strip()
                    if specific_part and len(specific_part) > 3:  # 确保不是空或过短的内容
                        self.logger.debug(f"从冒号分割中提取到具体错误: {specific_part}")
                        return specific_part

            # 如果都没找到，返回原始message，但优化显示
            if message:
                # 移除重复的"抢房失败:"前缀
                if message.startswith('抢房失败: '):
                    clean_message = message[5:].strip()  # 移除"抢房失败: "
                    if clean_message:
                        return clean_message
                return message

            return '未知错误'

        except Exception as e:
            self.logger.warning(f"提取具体错误信息时出现异常: {e}")
            return grab_result.get('message', '未知错误')

    async def _send_grab_result_notification(self, device: Dict[str, Any], house: HouseDetail, estate_name: str, grab_result: Dict[str, Any], duration: float = 0.0):
        """发送抢房结果通知给管理员设备"""
        try:
            # 从配置获取管理员设备ID
            admin_device_ids = self.config.get('grab_notification_devices', [self.config.get('admin_device_id', 'device1')])

            if not admin_device_ids:
                self.logger.warning("未配置抢房通知设备")
                return

            username = device.get('username', '未知用户')

            if grab_result.get('success'):
                title = f"🎉 抢房成功！"
                content = (f"📍 抢房成功通知\n"
                          f"👤 用户: {username}\n"
                          f"🏘️ 小区: {estate_name}\n"
                          f"🏠 房源名称: {house.name or '未知'}\n"
                          f"📍 位置: {house.position or '未知'}\n"
                          f"🏷️ 房间号: {house.roomno or '未知'}\n"
                          f"🏡 房型: {house.outtype or '未知'}\n"
                          f"🧭 朝向: {house.direction or '未知'}\n"
                          f"📐 面积: {house.area}㎡\n"
                          f"💰 月租金: {house.rent}元\n"
                          f"🆔 房源ID: {house.id}\n"
                          f"⏰ 抢房时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n"
                          f"⚡ 抢房耗时: {duration:.2f}秒\n"
                          f"✅ 状态: 抢房成功")
            else:
                title = f"❌ 抢房失败"

                # 提取具体的错误信息
                specific_error = self._extract_specific_error_message(grab_result)

                # 构建详细的错误信息
                error_details = grab_result.get('details', '')

                content = (f" 抢房失败通知\n"
                          f"👤 用户: {username}\n"
                          f"🏘️ 小区: {estate_name}\n"
                          f"🏠 房源名称: {house.name or '未知'}\n"
                          f"📍 位置: {house.position or '未知'}\n"
                          f"🏷️ 房间号: {house.roomno or '未知'}\n"
                          f"🏡 房型: {house.outtype or '未知'}\n"
                          f"🧭 朝向: {house.direction or '未知'}\n"
                          f"📐 面积: {house.area}㎡\n"
                          f"💰 月租金: {house.rent}元\n"
                          f"🆔 房源ID: {house.id}\n"
                          f"❌ 失败原因: {specific_error}\n")

                # 只有当错误详情与失败原因不同时才添加详情
                if error_details and error_details != specific_error and not specific_error in error_details:
                    content += f"📝 详情: {error_details}\n"

                content += (f"⏰ 失败时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n"
                           f"⚡ 抢房耗时: {duration:.2f}秒")

            # 创建一个模拟的MonitorResult用于发送通知
            from core.data.models import MonitorResult
            notification_result = MonitorResult(
                house_name=title,
                old_count=0,
                new_count=1,
                details=None
            )
            notification_result.change_type = "grab_result"  # 自定义类型标识抢房结果
            notification_result._content = content  # 自定义内容

            # 发送通知给所有管理员设备
            await self.notification_manager.send_notification_for_result(
                notification_result,
                admin_device_ids
            )

            self.logger.info(f"抢房结果通知已发送给管理员设备: {', '.join(admin_device_ids)}")

        except Exception as e:
            self.logger.error(f"发送抢房结果通知时出错: {e}")

    async def _build_baseline(self, name: str, estate_id: str):
        """为单个房源建立基线"""
        self.logger.info(f"[{name}] 正在建立房源基线...")
        details = await self.api_client.fetch_house_details_async(estate_id)
        if details:
            house_ids = {house.id for house in details if house.id}
            self.history_manager.set_known_houses(name, house_ids)

            # 保存房源详细信息到历史记录
            self.history_manager.update_house_details_from_list(name, details)

            self.logger.info(f"[{name}] 基线建立成功，当前已知 {len(house_ids)} 个房源（含详细信息）")

    def _identify_new_houses(self, name: str, details: List[HouseDetail]) -> List[HouseDetail]:
        """识别新增房源"""
        known_house_ids = self.history_manager.get_known_houses(name)
        current_house_ids = {house.id for house in details if house.id}

        new_house_ids = current_house_ids - set(known_house_ids)
        if not new_house_ids:
            self.logger.warning(f"[{name}] 检测到数量变化但未识别到具体新增房源ID")
            return []

        self.logger.info(f"[{name}] 严格比对后发现 {len(new_house_ids)} 个真正新增房源")

        # 更新已知房源列表
        self.history_manager.set_known_houses(name, current_house_ids)

        # 同步更新房源详细信息到历史记录
        self.history_manager.update_house_details_from_list(name, details)

        return [house for house in details if house.id in new_house_ids]

    async def _update_baseline_after_decrease(self, name: str, estate_id: str):
        """房源减少后，更新基线"""
        self.logger.info(f"[{name}] 房源数量减少，正在更新基线信息...")
        details = await self.api_client.fetch_house_details_async(estate_id)
        if details:
            current_house_ids = {house.id for house in details if house.id}
            self.history_manager.set_known_houses(name, current_house_ids)

            # 完全重置房源详细信息（先清空再设置，确保数据一致性）
            self.history_manager.reset_house_details(name, details)

            self.logger.info(f"[{name}] 基线更新成功，剩余 {len(current_house_ids)} 个已知房源（含详细信息）")
        else:
            # 如果没有获取到任何详情，清空所有记录
            self.history_manager.set_known_houses(name, set())
            self.history_manager.reset_house_details(name, [])
            self.logger.info(f"[{name}] 基线更新成功，已清空所有房源记录")


class GrabDeviceManager:
    """抢房设备管理器"""

    def __init__(self, grab_device_model, logger):
        self.grab_device_model = grab_device_model
        self.logger = logger

    def get_devices_by_estate(self, estate_name: str) -> List[Dict[str, Any]]:
        """获取指定小区的所有启用的抢房设备"""
        try:
            all_devices = self.grab_device_model.get_all()
            return [
                device for device in all_devices
                if device.get('target_estate') == estate_name and device.get('enabled')
            ]
        except Exception as e:
            self.logger.error(f"获取抢房设备失败: {e}")
            return []