"""默认配置定义 - 统一版本"""

import os
from pathlib import Path

# 基础路径常量
PROJECT_ROOT = Path(__file__).parent.parent
DATABASE_FILE = str(PROJECT_ROOT / "monitoring.db")
LOG_DIR = str(PROJECT_ROOT / "log")

# API配置常量
BASE_URL = "https://www.huhhothome.cn/api/dynamicapi/apiview/viewdata"
COMMON_PARAMS = {
    "application": "jqHj7ddxI1smOEkmKSD",
    "apiview": "houseEstate",
    "_localversion": "",
    "domainId": "LAMDyAh4HSdnug1KdKL"
}

# 默认代理API
DEFAULT_PROXY_API_URL = "http://api1.ydaili.cn/tools/MeasureApi.ashx?action=EAPI&secret=75A9B01A7021F22592E35707C1119B43894970A47AB6E989117D57F2420081E6E8A82FBB158ACD6B&number=1&orderId=SH20230412183940319&format=json&province=510000"

# 监控配置常量
DEFAULT_CHECK_INTERVAL = 60
DEFAULT_MAX_RETRIES = 5

# 日志配置常量
LOG_LEVEL_MAPPING = {
    "DEBUG": 0,
    "INFO": 1,
    "WARNING": 2,
    "ERROR": 3,
    "CRITICAL": 4
}

# 统一默认配置
DEFAULT_CONFIG = {
    "monitor_configs": [],
    "base_url": BASE_URL,
    "common_params": COMMON_PARAMS,
    "check_interval": DEFAULT_CHECK_INTERVAL,
    "max_retries": DEFAULT_MAX_RETRIES,
    "proxy_api_url": DEFAULT_PROXY_API_URL,

    # 数据库配置
    "database": {
        "file": DATABASE_FILE
    },

    # 添加历史数据存储
    "history": {},
    "estate_ids": {},
    "known_houses": {},

    # 添加调度任务配置
    "schedule": {
        "tasks": {
            "monitor_task": {
                "enabled": False,
                "start_time": "09:00",
                "stop_time": "21:00",
                "days": [0, 1, 2, 3, 4, 5, 6]  # 每天
            }
        }
    },

    # API错误监控配置
    "api_error_monitoring": {
        "consecutive_error_threshold": 100,  # 连续错误次数阈值
        "error_duration_threshold": 120,     # 错误持续时间阈值（秒，3分钟）
        "notification_interval": 300,        # 重复通知最小间隔（秒，5分钟）
        "recovery_notification": True        # 是否发送恢复通知
    },

    # 管理员通知器配置
    # 当抢房设备Cookie即将过期并自动发送验证码后，会自动推送通知给管理员设备
    # 管理员可以通过推送中的链接快速填写短信验证码，完成设备的自动重新登录
    "admin_notifier": {
        # 管理员设备ID列表 - 这些设备会接收到抢房设备Cookie过期的推送通知
        # 注意：这里的device_id应该是在"设备管理"中添加的推送设备的ID
        # 例如：bark设备、微信推送设备或PushMe设备的ID
        # 使用方法：
        # 1. 在"设备管理"页面添加您的管理员推送设备（如Bark设备）
        # 2. 记下设备的ID（可以在设备列表中看到）
        # 3. 将该ID添加到下面的device_ids列表中
        # 4. 保存配置后，当抢房设备Cookie即将过期时，会自动发送通知到这些设备
        "device_ids": [
             "device1745334492891"     # 示例：管理员Bark设备ID
        ],
        "enabled": True,                      # 是否启用管理员通知功能

        # 推送通知内容模板配置（可选，一般不需要修改）
        "notification_templates": {
            "cookie_expiring": {
                "title": "🔐 抢房设备Cookie即将过期",
                "include_sms_form_link": True,    # 是否在推送中包含短信验证码填写链接
                "include_device_info": True       # 是否在推送中包含设备详细信息
            },
            "login_success": {
                "title": "✅ 抢房设备Cookie延长成功",
                "include_cookie_info": True       # 是否在推送中包含Cookie有效期信息
            }
        }
    },

    # 设备列表
    "device_list": [],

    # Web配置 - 用户名和密码将在首次启动时设置
    # 注意：不再在代码中存储默认凭据，首次启动时会引导用户设置管理员账户

    # 统一日志配置
    "logging": {
        "level": "INFO",
        "format": "%(asctime)s,%(msecs)03d - %(name)s - %(levelname)s - %(message)s",
        "date_format": "%Y-%m-%d %H:%M:%S",
        "use_colors": True,
        "enable_file_logging": True,
        "file_log_level": "INFO",
        "log_dir": LOG_DIR,
        "log_file_name": "monitor",
        "file_rotation": {
            "when": "midnight",
            "interval": 1,
            "backup_count": 30
        },
        "enable_masking": True
    },

    # 服务管理配置
    "monitor_service": {
        "script": "start_monitor_service.py",
        "host": "127.0.0.1",
        "port": 8089,
        "health_path": "/health",
        "startup_delay": 3,
        "max_startup_wait": 30
    },

    "web_service": {
        "script": "web/app.py",
        "host": "0.0.0.0",
        "port": 50011,
        "health_path": "/",
        "startup_delay": 5,
        "max_startup_wait": 45,
        "depends_on": "monitor"
    },

    # 服务管理器配置
    "service_management": {
        "health_check_timeout": 3,
        "check_interval": 5
    },

    # 监控服务URL配置
    "monitor_service_url": "http://127.0.0.1:8089"
}