"""
Cookie测试器模块
"""

import os
import json
import requests
import time
import random
from datetime import datetime, timedelta
from flask import current_app

# 导入动态请求头生成器
try:
    import sys
    sys.path.append(os.path.join(os.path.dirname(__file__), '..', '..', '..'))
    from core.network.dynamic_header_generator import get_global_header_generator
    DYNAMIC_HEADERS_AVAILABLE = True
except ImportError:
    DYNAMIC_HEADERS_AVAILABLE = False
    print("警告: 动态请求头生成器不可用，将使用固定请求头")


class CookieTester:
    """Cookie有效性测试器 - 使用动态请求头增强反爬能力"""

    def __init__(self):
        self.session = requests.Session()
        self.session.verify = False  # 禁用SSL验证
        self.base_url = "https://www.huhhothome.cn"

        # 获取动态请求头生成器
        if DYNAMIC_HEADERS_AVAILABLE:
            self.header_generator = get_global_header_generator()
            # 使用动态生成的请求头作为基础
            self.headers = self._get_dynamic_headers()
            current_app.logger.info(f"Cookie测试器已启用动态请求头: {self.headers.get('User-Agent', '')[:50]}...")
        else:
            # 如果动态请求头不可用，使用固定请求头
            self.headers = {
                "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/122.0.6261.57 Safari/537.36",
                "Content-Type": "application/json",
                "Accept": "application/json, text/javascript, */*; q=0.01",
                "Sec-Ch-Ua": "\"Not(A:Brand\";v=\"24\", \"Chromium\";v=\"122\"",
                "Sec-Ch-Ua-Mobile": "?0",
                "Sec-Ch-Ua-Platform": "\"Windows\"",
                "Sec-Fetch-Site": "same-origin",
                "Sec-Fetch-Mode": "cors",
                "Sec-Fetch-Dest": "empty",
                "Referer": "https://www.huhhothome.cn/web/index.html"
            }
            current_app.logger.warning("Cookie测试器使用固定请求头（动态生成器不可用）")

        # 添加Cookie测试器特有的请求头
        self.headers.update({
            "Content-Type": "application/json",
            "Accept": "application/json, text/javascript, */*; q=0.01",
            "Referer": "https://www.huhhothome.cn/web/index.html"
        })

    def _get_dynamic_headers(self):
        """获取动态生成的请求头"""
        if DYNAMIC_HEADERS_AVAILABLE:
            return self.header_generator.get_random_headers()
        else:
            return {}

    def _refresh_headers(self):
        """刷新请求头（20%概率刷新，避免过于频繁）"""
        if DYNAMIC_HEADERS_AVAILABLE and random.random() < 0.2:  # 20%概率刷新
            new_headers = self._get_dynamic_headers()
            # 保留Cookie测试器特有的头信息
            special_headers = {
                "Content-Type": self.headers.get("Content-Type"),
                "Accept": self.headers.get("Accept"),
                "Referer": self.headers.get("Referer")
            }

            self.headers = new_headers
            # 重新添加特有头信息
            for key, value in special_headers.items():
                if value:
                    self.headers[key] = value

    def _make_request(self, method, url, **kwargs):
        """统一的请求方法，自动使用动态请求头"""
        # 刷新请求头
        self._refresh_headers()

        # 确保使用最新的请求头
        if 'headers' not in kwargs:
            kwargs['headers'] = self.headers.copy()
        else:
            # 合并用户提供的请求头
            merged_headers = self.headers.copy()
            merged_headers.update(kwargs['headers'])
            kwargs['headers'] = merged_headers

        # 发送请求
        if method.upper() == 'GET':
            return self.session.get(url, **kwargs)
        elif method.upper() == 'POST':
            return self.session.post(url, **kwargs)
        else:
            raise ValueError(f"不支持的请求方法: {method}")

    def test_cookie_validity(self, device_data):
        """测试Cookie有效性"""
        try:
            # 处理不同的参数格式
            if isinstance(device_data, dict):
                # 如果是设备数据字典，提取cookie和access_token
                cookie_str = device_data.get('cookie', '')
                access_token = device_data.get('access_token', '')
            else:
                # 向后兼容：如果传入的是cookies_dict和access_token（旧格式）
                return {
                    'success': False,
                    'message': '参数格式错误',
                    'details': '请提供设备数据字典作为参数'
                }

            if not cookie_str or not access_token:
                return {
                    'success': False,
                    'message': 'Cookie或Access Token为空',
                    'details': '请重新登录获取有效的认证信息'
                }

            # 设置Cookie到session
            try:
                cookie_dict = json.loads(cookie_str)
                for name, value in cookie_dict.items():
                    self.session.cookies.set(name, value)
            except json.JSONDecodeError:
                return {
                    'success': False,
                    'message': 'Cookie格式无效',
                    'details': '无法解析Cookie字符串'
                }

            # 设置access token到请求头
            test_headers = self.headers.copy()
            test_headers["Membertoken"] = access_token

            # 测试用户信息接口
            url = f"{self.base_url}/api/dynamicapi/user/detail"
            params = {
                "application": "jqHj7ddxI1smOEkmKSD"
            }

            response = self._make_request('GET', url, headers=test_headers, params=params)
            response.raise_for_status()

            result = response.json()
            if result.get("errcode") == 0.0:
                user_data = result.get("data", {})
                user_info = user_data.get("itemmap", {})

                return {
                    'success': True,
                    'message': 'Cookie有效',
                    'details': {
                        'username': user_info.get('name', '未知'),
                        'phone': user_info.get('phone', '未知'),
                        'test_time': datetime.now().strftime('%Y-%m-%d %H:%M:%S')
                    }
                }
            else:
                return {
                    'success': False,
                    'message': f'API返回错误: {result.get("errmsg", "未知错误")}',
                    'details': f'错误码: {result.get("errcode")}'
                }

        except requests.RequestException as e:
            return {
                'success': False,
                'message': f'网络请求失败: {str(e)}',
                'details': '可能是网络连接问题或Cookie已失效'
            }
        except Exception as e:
            return {
                'success': False,
                'message': f'测试过程出错: {str(e)}',
                'details': '请检查Cookie格式和Access Token是否正确'
            }


def _check_profile_exists(username: str) -> bool:
    """检查用户profile目录是否存在"""
    try:
        profile_path = os.path.join('ZDQF', 'profiles', username)
        return os.path.exists(profile_path) and os.path.isdir(profile_path)
    except:
        return False


def _get_profile_data(username: str) -> dict:
    """获取用户profile数据"""
    try:
        profile_path = os.path.join('ZDQF', 'profiles', username)
        config_file = os.path.join(profile_path, 'config.json')

        if os.path.exists(config_file):
            with open(config_file, 'r', encoding='utf-8') as f:
                return json.load(f)
        else:
            return {}
    except Exception as e:
        current_app.logger.error(f"读取profile数据失败: {str(e)}")
        return {}


def _save_profile_data(username: str, profile_data: dict) -> bool:
    """保存用户profile数据"""
    try:
        profile_path = os.path.join('ZDQF', 'profiles', username)

        # 确保目录存在
        os.makedirs(profile_path, exist_ok=True)

        config_file = os.path.join(profile_path, 'config.json')

        with open(config_file, 'w', encoding='utf-8') as f:
            json.dump(profile_data, f, ensure_ascii=False, indent=2)

        return True
    except Exception as e:
        current_app.logger.error(f"保存profile数据失败: {str(e)}")
        return False