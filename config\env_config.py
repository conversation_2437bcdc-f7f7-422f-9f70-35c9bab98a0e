"""
环境配置管理模块
集中管理所有服务的配置参数，支持从环境变量读取
"""

import os
from typing import Dict, Any


def get_env_config() -> Dict[str, Any]:
    """
    获取环境配置，优先从环境变量读取，否则使用默认值

    Returns:
        Dict[str, Any]: 包含所有配置的字典
    """
    return {
        # 监控服务配置
        'monitor_service': {
            'host': os.getenv('MONITOR_SERVICE_HOST', '127.0.0.1'),
            'port': int(os.getenv('MONITOR_SERVICE_PORT', '8089')),
            'script': os.getenv('MONITOR_SERVICE_SCRIPT', 'start_monitor_service.py'),
            'startup_delay': int(os.getenv('MONITOR_SERVICE_STARTUP_DELAY', '3')),
            'max_startup_wait': int(os.getenv('MONITOR_SERVICE_MAX_STARTUP_WAIT', '30')),
            'health_path': os.getenv('MONITOR_SERVICE_HEALTH_PATH', '/health')
        },

        # Web控制台配置
        'web_service': {
            'host': os.getenv('WEB_SERVICE_HOST', '0.0.0.0'),
            'port': int(os.getenv('WEB_SERVICE_PORT', '50011')),
            'script': os.getenv('WEB_SERVICE_SCRIPT', 'web_control_new.py'),
            'startup_delay': int(os.getenv('WEB_SERVICE_STARTUP_DELAY', '2')),
            'max_startup_wait': int(os.getenv('WEB_SERVICE_MAX_STARTUP_WAIT', '20')),
            'health_path': os.getenv('WEB_SERVICE_HEALTH_PATH', '/')
        },

        # 日志配置
        'logging': {
            'level': os.getenv('LOG_LEVEL', 'INFO'),
            'format': os.getenv('LOG_FORMAT', '%(asctime)s,%(msecs)03d - %(name)s - %(levelname)s - %(message)s'),
            'date_format': os.getenv('LOG_DATE_FORMAT', '%Y-%m-%d %H:%M:%S'),
            'use_colors': os.getenv('LOG_USE_COLORS', 'true').lower() == 'true',
            'enable_file_logging': os.getenv('LOG_ENABLE_FILE', 'true').lower() == 'true',
            'file_log_level': os.getenv('LOG_FILE_LEVEL', 'INFO'),
            'log_dir': os.getenv('LOG_DIR', 'log'),
            'log_file_name': os.getenv('LOG_FILE_NAME', 'monitor'),
            'file_rotation': {
                'when': os.getenv('LOG_ROTATION_WHEN', 'midnight'),
                'interval': int(os.getenv('LOG_ROTATION_INTERVAL', '1')),
                'backup_count': int(os.getenv('LOG_ROTATION_BACKUP_COUNT', '30'))
            },
            'enable_masking': os.getenv('LOG_ENABLE_MASKING', 'true').lower() == 'true'
        },

        # 服务管理配置
        'service_management': {
            'check_interval': int(os.getenv('SERVICE_CHECK_INTERVAL', '5')),
            'health_check_timeout': int(os.getenv('HEALTH_CHECK_TIMEOUT', '3'))
        },

        # 监控服务API URL（用于Web控制台）
        'monitor_service_url': os.getenv('MONITOR_SERVICE_URL', 'http://127.0.0.1:8089'),

        # 数据库配置
        'database': {
            'file': os.getenv('DATABASE_FILE', 'monitoring.db')
        }
    }


# 全局配置实例
ENV_CONFIG = get_env_config()