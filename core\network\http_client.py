"""HTTP客户端 - 统一的HTTP请求管理"""

import asyncio
import ssl
import random
import time
from typing import Dict, Any, Optional, Union
import aiohttp
import requests
import re

from .proxy_manager import ProxyManager
from .dynamic_header_generator import get_global_header_generator
from .optimized_connector import get_housing_monitor_connector, HousingMonitorConnector
from ..utils.unified_logging import Logger
from ..utils.helpers import random_delay, retry_on_exception

# 导入统一异常处理框架
from core.exceptions import handle_exception, NetworkException, ProxyException


class OptimizedHttpClient:
    """优化的HTTP客户端 - 集成自适应连接池和性能监控"""

    def __init__(self, proxy_manager: ProxyManager, logger: Optional[Logger] = None):
        self.proxy_manager = proxy_manager
        self.logger = logger or Logger("OptimizedHttpClient")

        # 获取全局动态请求头生成器
        self.header_generator = get_global_header_generator()

        # 同步会话
        self.sync_session = None

        # 使用专为住房监控优化的连接器
        self.housing_connector: HousingMonitorConnector = get_housing_monitor_connector()

        # 默认的补充头信息（会与动态生成的头合并）
        self.default_headers = {
            'Referer': 'https://www.huhhothome.cn/',
        }

        # 性能统计
        self.request_stats = {
            'total_requests': 0,
            'successful_requests': 0,
            'failed_requests': 0,
            'avg_response_time': 0.0
        }

        # 连接池刷新状态管理
        self._refresh_lock = asyncio.Lock()
        self._is_refreshing = False
        self._last_refresh_time = 0

        # 代理更新防重机制 - 5秒内只允许一次代理更新请求
        self._last_proxy_update_request_time = 0
        self._proxy_update_cooldown = 5.0  # 5秒冷却时间

        self.logger.info("OptimizedHttpClient初始化完成，已启用自适应连接池和代理更新防重机制")

    def _should_trigger_proxy_update(self) -> bool:
        """检查是否应该触发代理更新 - 5秒内只允许一次"""
        import time
        current_time = time.time()
        if current_time - self._last_proxy_update_request_time >= self._proxy_update_cooldown:
            self._last_proxy_update_request_time = current_time
            return True
        else:
            remaining_time = self._proxy_update_cooldown - (current_time - self._last_proxy_update_request_time)
            self.logger.debug(f"代理更新请求被限制，需等待 {remaining_time:.1f} 秒")
            return False

    def _get_sync_session(self) -> requests.Session:
        """获取同步会话"""
        try:
            if self.sync_session is None:
                self.sync_session = requests.Session()
                self.sync_session.verify = False  # 禁用SSL验证
            return self.sync_session
        except Exception as e:
            raise handle_exception(e, context="创建同步HTTP会话失败")

    async def _get_async_session(self, force_recreate: bool = False, retry_count: int = 0) -> aiohttp.ClientSession:
        """获取优化的异步会话，支持重试机制"""
        max_retries = 3
        try:
            # 使用住房监控专用连接器
            session = await self.housing_connector.get_session(ssl_context=False)

            if force_recreate:
                self.logger.debug("请求强制重新创建会话（由自适应连接器管理）")

            # 检查会话状态
            if session.closed:
                self.logger.warning(f"检测到会话已关闭，尝试重新获取 (重试次数: {retry_count})")
                if retry_count < max_retries:
                    # 等待一小段时间后重试
                    await asyncio.sleep(0.1 * (retry_count + 1))
                    return await self._get_async_session(force_recreate=True, retry_count=retry_count + 1)
                else:
                    raise Exception("会话获取重试次数已达上限")

            # 额外的连接器状态检查
            if hasattr(session, '_connector') and session._connector and session._connector.closed:
                self.logger.warning(f"检测到连接器已关闭，尝试重新获取 (重试次数: {retry_count})")
                if retry_count < max_retries:
                    await asyncio.sleep(0.1 * (retry_count + 1))
                    return await self._get_async_session(force_recreate=True, retry_count=retry_count + 1)
                else:
                    raise Exception("连接器获取重试次数已达上限")

            return session
        except Exception as e:
            if retry_count < max_retries:
                self.logger.warning(f"获取会话失败，尝试重试 {retry_count + 1}/{max_retries}: {str(e)}")
                await asyncio.sleep(0.2 * (retry_count + 1))
                return await self._get_async_session(force_recreate=True, retry_count=retry_count + 1)
            else:
                raise handle_exception(e, context="获取异步HTTP会话失败（重试次数已达上限）")

    def _prepare_headers(self, additional_headers: Optional[Dict] = None) -> Dict[str, str]:
        """准备请求头 - 使用动态生成器"""
        try:
            # 获取动态生成的请求头
            headers = self.header_generator.get_random_headers()

            # 添加默认补充头信息
            for key, value in self.default_headers.items():
                if key not in headers:
                    headers[key] = value

            # 添加额外的头信息
            if additional_headers:
                headers.update(additional_headers)

            return headers
        except Exception as e:
            # 如果头信息生成失败，使用基础头信息
            self.logger.warning(f"请求头生成失败，使用基础头信息: {e}")
            basic_headers = {
                'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36',
                'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,*/*;q=0.8'
            }
            if additional_headers:
                basic_headers.update(additional_headers)
            return basic_headers

    async def get_async(self, url: str, use_proxy: bool = True, **kwargs) -> aiohttp.ClientResponse:
        """异步GET请求，集成性能监控和自适应连接池"""
        return await self._execute_async_request('GET', url, use_proxy, **kwargs)

    async def _execute_async_request(self, method: str, url: str, use_proxy: bool = True, max_retries: int = 2, **kwargs) -> aiohttp.ClientResponse:
        """执行异步请求的通用方法，支持会话重试"""
        start_time = time.perf_counter()
        success = False
        connection_error = False
        retry_count = 0

        while retry_count <= max_retries:
            try:
                # 检查代理是否正在刷新，如果是则等待
                if self.proxy_manager and self.proxy_manager._refresh_lock.locked():
                    self.logger.debug("代理正在刷新，请求将等待...")
                    await self.proxy_manager._refresh_lock.acquire() # 等待锁
                    self.proxy_manager._refresh_lock.release() # 立刻释放，仅用于等待
                    self.logger.debug("代理刷新完成，请求继续执行。")

                self.request_stats['total_requests'] += 1

                session = await self._get_async_session()

                # 添加代理（可选）
                if use_proxy and 'proxy' not in kwargs:
                    try:
                        proxy_url = self.proxy_manager.get_proxy_url()
                        if proxy_url:
                            kwargs['proxy'] = proxy_url
                    except Exception as e:
                        # 代理获取失败不影响请求，记录警告
                        self.logger.warning(f"获取代理失败，使用直连: {e}")

                # 添加头信息
                if 'headers' not in kwargs:
                    kwargs['headers'] = self._prepare_headers()
                else:
                    headers = self._prepare_headers()
                    headers.update(kwargs['headers'])
                    kwargs['headers'] = headers

                # 设置默认超时 - 增加超时时间以适应代理连接
                if 'timeout' not in kwargs:
                    kwargs['timeout'] = aiohttp.ClientTimeout(total=15, connect=8)  # 总超时15秒，连接超时8秒

                # 添加随机延迟
                await random_delay()

                proxy_status = "使用代理" if use_proxy and kwargs.get('proxy') else "直连"
                self.logger.debug(f"发送异步{method}请求到: {url} ({proxy_status})")

                # 执行请求
                if method.upper() == 'GET':
                    response = await session.get(url, **kwargs)
                else:
                    response = await session.post(url, **kwargs)

                success = True
                self.request_stats['successful_requests'] += 1
                return response

            except (RuntimeError, aiohttp.ClientConnectionError) as e:
                error_msg = str(e).lower()
                if 'session is closed' in error_msg or 'connector is closed' in error_msg:
                    self.logger.warning(f"检测到会话/连接器关闭: {e}, 尝试重试 ({retry_count + 1}/{max_retries + 1})")
                    connection_error = True
                    if retry_count < max_retries:
                        retry_count += 1
                        await asyncio.sleep(0.5 * retry_count)  # 递增等待时间
                        continue
                    else:
                        raise handle_exception(
                            NetworkException(f"会话关闭: {str(e)}", url=url),
                            context=f"异步{method}请求会话关闭: {url}"
                        )
                else:
                    # 其他RuntimeError或ClientConnectionError
                    connection_error = True
                    raise handle_exception(
                        NetworkException(f"连接失败: {str(e)}", url=url),
                        context=f"异步{method}请求连接失败: {url}"
                    )
            except aiohttp.ClientProxyConnectionError as e:
                connection_error = True
                # 检查是否是407认证错误
                error_msg = str(e).lower()
                if '407' in error_msg and 'proxy authentication required' in error_msg:
                    self.logger.error(f"检测到407代理认证错误: {e}")
                    # 提取代理URL并通知代理管理器
                    proxy_url = kwargs.get('proxy', '')
                    if proxy_url and self.proxy_manager:
                        # 提取IP:PORT格式
                        match = re.search(r'//([^/]+)', proxy_url)
                        if match:
                            failed_proxy = match.group(1)
                            self.logger.warning(f"代理 {failed_proxy} 需要认证，请求切换新代理")
                            # 检查是否应该触发代理更新（防重机制）
                            if self._should_trigger_proxy_update():
                                # 异步切换代理（不等待结果，避免阻塞当前请求）
                                asyncio.create_task(self.proxy_manager.handle_407_error(failed_proxy))
                            else:
                                self.logger.debug("代理更新请求被限制，跳过此次407错误处理")

                raise handle_exception(
                    ProxyException(f"代理连接失败: {str(e)}", proxy_url=kwargs.get('proxy')),
                    context=f"异步{method}请求代理连接失败: {url}"
                )
            except aiohttp.ClientHttpProxyError as e:
                connection_error = True
                # 专门处理ClientHttpProxyError（包含407错误）
                error_msg = str(e).lower()
                self.logger.error(f"检测到ClientHttpProxyError: {e}")
                if '407' in error_msg and 'proxy authentication required' in error_msg:
                    self.logger.error(f"检测到407代理认证错误: {e}")
                    # 提取代理URL并通知代理管理器
                    proxy_url = kwargs.get('proxy', '')
                    if proxy_url and self.proxy_manager:
                        # 提取IP:PORT格式
                        match = re.search(r'//([^/]+)', proxy_url)
                        if match:
                            failed_proxy = match.group(1)
                            self.logger.warning(f"代理 {failed_proxy} 需要认证，请求切换新代理")
                            # 检查是否应该触发代理更新（防重机制）
                            if self._should_trigger_proxy_update():
                                # 异步切换代理（不等待结果，避免阻塞当前请求）
                                asyncio.create_task(self.proxy_manager.handle_407_error(failed_proxy))
                            else:
                                self.logger.debug("代理更新请求被限制，跳过此次407错误处理")

                raise handle_exception(
                    ProxyException(f"代理HTTP错误: {str(e)}", proxy_url=kwargs.get('proxy')),
                    context=f"异步{method}请求代理HTTP错误: {url}"
                )
            except asyncio.TimeoutError as e:
                # 处理asyncio超时异常
                timeout_type = "请求超时"
                self.logger.warning(f"检测到{timeout_type}: {e}")

                # 智能重试机制：如果是代理超时且有代理刷新正在进行，则等待并重试
                if kwargs.get('proxy') and self.proxy_manager:
                    proxy_url = kwargs.get('proxy', '')
                    match = re.search(r'//([^/]+)', proxy_url)
                    if match:
                        failed_proxy = match.group(1)
                        self.logger.warning(f"代理 {failed_proxy} 请求超时，请求切换新代理")

                        # 检查是否有代理刷新正在进行
                        if self.proxy_manager._refresh_lock.locked():
                            self.logger.info(f"检测到代理刷新正在进行，等待完成后重试请求...")
                            # 等待代理刷新完成
                            await self.proxy_manager._refresh_lock.acquire()
                            self.proxy_manager._refresh_lock.release()
                            self.logger.info(f"代理刷新完成，开始重试请求...")

                            # 重试请求一次（更新代理URL）
                            if self.proxy_manager.current_proxy:
                                new_proxy_url = self.proxy_manager.get_proxy_url()
                                if new_proxy_url:
                                    kwargs['proxy'] = new_proxy_url
                                    self.logger.debug(f"使用新代理重试: {new_proxy_url}")
                                    # 重新执行请求，但不增加retry_count，这是超时后的智能重试
                                    continue
                        else:
                            # 没有代理刷新在进行，触发代理更新
                            if self._should_trigger_proxy_update():
                                asyncio.create_task(self.proxy_manager.update_proxy_async(force=True))
                            else:
                                self.logger.debug("代理更新请求被限制，跳过此次超时错误处理")

                raise handle_exception(
                    NetworkException(f"{timeout_type}: {str(e)}", url=url),
                    context=f"异步{method}{timeout_type}: {url}"
                )
            except aiohttp.ServerTimeoutError as e:
                # 处理aiohttp服务器超时异常
                timeout_type = "服务器超时"
                self.logger.warning(f"检测到{timeout_type}: {e}")

                raise handle_exception(
                    NetworkException(f"{timeout_type}: {str(e)}", url=url),
                    context=f"异步{method}{timeout_type}: {url}"
                )
            except Exception as e:
                if not success:
                    self.request_stats['failed_requests'] += 1
                raise handle_exception(e, context=f"异步{method}请求失败: {url}")
            finally:
                # 记录性能指标
                response_time = time.perf_counter() - start_time
                self.housing_connector.record_performance(response_time, success, connection_error)

                # 更新平均响应时间
                self._update_avg_response_time(response_time)

            # 如果代码执行到这里，说明需要重试
            break

    async def post_async(self, url: str, use_proxy: bool = True, **kwargs) -> aiohttp.ClientResponse:
        """异步POST请求，集成性能监控和自适应连接池"""
        return await self._execute_async_request('POST', url, use_proxy, **kwargs)

    def get_sync(self, url: str, use_proxy: bool = True, **kwargs) -> requests.Response:
        """同步GET请求，集成统一异常处理"""
        try:
            session = self._get_sync_session()

            # 添加代理（可选）
            if use_proxy and 'proxies' not in kwargs:
                try:
                    proxy_dict = self.proxy_manager.get_proxy_dict()
                    if proxy_dict:
                        kwargs['proxies'] = proxy_dict
                except Exception as e:
                    self.logger.warning(f"获取代理失败，使用直连: {e}")

            # 添加头信息
            if 'headers' not in kwargs:
                kwargs['headers'] = self._prepare_headers()
            else:
                headers = self._prepare_headers()
                headers.update(kwargs['headers'])
                kwargs['headers'] = headers

            # 设置默认超时
            if 'timeout' not in kwargs:
                kwargs['timeout'] = 10

            proxy_status = "使用代理" if use_proxy and kwargs.get('proxies') else "直连"
            self.logger.debug(f"发送同步GET请求到: {url} ({proxy_status})")

            response = session.get(url, **kwargs)
            return response

        except requests.exceptions.ProxyError as e:
            raise handle_exception(
                ProxyException(f"代理错误: {str(e)}", proxy_url=str(kwargs.get('proxies', ''))),
                context=f"同步GET请求代理错误: {url}"
            )
        except requests.exceptions.ConnectionError as e:
            raise handle_exception(
                NetworkException(f"连接错误: {str(e)}", url=url),
                context=f"同步GET请求连接错误: {url}"
            )
        except requests.exceptions.Timeout as e:
            raise handle_exception(
                NetworkException(f"请求超时: {str(e)}", url=url),
                context=f"同步GET请求超时: {url}"
            )
        except requests.exceptions.RequestException as e:
            raise handle_exception(
                NetworkException(f"请求异常: {str(e)}", url=url),
                context=f"同步GET请求异常: {url}"
            )
        except Exception as e:
            raise handle_exception(e, context=f"同步GET请求失败: {url}")

    def post_sync(self, url: str, use_proxy: bool = True, **kwargs) -> requests.Response:
        """同步POST请求，集成统一异常处理"""
        try:
            session = self._get_sync_session()

            # 添加代理（可选）
            if use_proxy and 'proxies' not in kwargs:
                try:
                    proxy_dict = self.proxy_manager.get_proxy_dict()
                    if proxy_dict:
                        kwargs['proxies'] = proxy_dict
                except Exception as e:
                    self.logger.warning(f"获取代理失败，使用直连: {e}")

            # 添加头信息
            if 'headers' not in kwargs:
                kwargs['headers'] = self._prepare_headers()
            else:
                headers = self._prepare_headers()
                headers.update(kwargs['headers'])
                kwargs['headers'] = headers

            # 设置默认超时
            if 'timeout' not in kwargs:
                kwargs['timeout'] = 10

            proxy_status = "使用代理" if use_proxy and kwargs.get('proxies') else "直连"
            self.logger.debug(f"发送同步POST请求到: {url} ({proxy_status})")

            response = session.post(url, **kwargs)
            return response

        except requests.exceptions.ProxyError as e:
            raise handle_exception(
                ProxyException(f"代理错误: {str(e)}", proxy_url=str(kwargs.get('proxies', ''))),
                context=f"同步POST请求代理错误: {url}"
            )
        except requests.exceptions.ConnectionError as e:
            raise handle_exception(
                NetworkException(f"连接错误: {str(e)}", url=url),
                context=f"同步POST请求连接错误: {url}"
            )
        except requests.exceptions.Timeout as e:
            raise handle_exception(
                NetworkException(f"请求超时: {str(e)}", url=url),
                context=f"同步POST请求超时: {url}"
            )
        except requests.exceptions.RequestException as e:
            raise handle_exception(
                NetworkException(f"请求异常: {str(e)}", url=url),
                context=f"同步POST请求异常: {url}"
            )
        except Exception as e:
            raise handle_exception(e, context=f"同步POST请求失败: {url}")

    def _update_avg_response_time(self, response_time: float):
        """更新平均响应时间"""
        current_avg = self.request_stats['avg_response_time']
        total_requests = self.request_stats['total_requests']

        if total_requests == 1:
            self.request_stats['avg_response_time'] = response_time
        else:
            # 滑动平均
            self.request_stats['avg_response_time'] = (current_avg * 0.9) + (response_time * 0.1)

    async def close_all(self):
        """优雅地关闭所有会话"""
        try:
            # 关闭住房监控连接器
            await self.housing_connector.close()
            self.logger.info("住房监控连接器已关闭")

            if self.sync_session:
                try:
                    self.sync_session.close()
                    self.logger.info("同步HTTP会话已成功关闭")
                except Exception as e:
                    self.logger.error(f"关闭同步会话时出错", context=e)
            self.sync_session = None
        except Exception as e:
            # 关闭会话的错误不应该抛出，只记录日志
            self.logger.warning(f"关闭HTTP会话时出现问题: {e}")

    async def force_refresh_connections(self):
        """强化的连接刷新，确保代理切换立即生效，支持并发控制"""
        # 使用锁防止并发刷新
        if self._is_refreshing:
            self.logger.debug("连接池正在刷新中，跳过本次刷新请求")
            return

        current_time = time.time()
        # 防止频繁刷新（最小间隔1秒）
        if current_time - self._last_refresh_time < 1.0:
            self.logger.debug("连接池刷新间隔太短，跳过本次刷新请求")
            return

        async with self._refresh_lock:
            if self._is_refreshing:
                self.logger.debug("连接池正在刷新中，跳过本次刷新请求")
                return

            self._is_refreshing = True
            self._last_refresh_time = current_time

            try:
                self.logger.info("开始强化连接池刷新...")

                # 导入连接器工厂和相关函数
                from .optimized_connector import ConnectorFactory

                # 第一步：获取连接器工厂实例
                connector_factory = ConnectorFactory()

                # 第二步：强制清理所有连接器和缓存
                await connector_factory.force_clear_all_connections()
                self.logger.debug("连接器工厂已完成强制清理")

                # 第三步：重置当前实例的连接器引用
                self.housing_connector = connector_factory.get_housing_monitor_connector()
                self.logger.debug("已获取全新的住房监控连接器实例")

                # 第四步：关闭同步会话（如果存在）
                if self.sync_session:
                    try:
                        self.sync_session.close()
                        self.sync_session = None
                        self.logger.debug("同步HTTP会话已重置")
                    except Exception as e:
                        self.logger.warning(f"关闭同步会话时出现异常: {e}")

                # 第五步：使用新代理预热连接
                try:
                    # 获取当前代理URL（如果有）
                    proxy_url = None
                    if self.proxy_manager and self.proxy_manager.current_proxy:
                        proxy_url = f"http://{self.proxy_manager.current_proxy}"

                    await connector_factory.warmup_connections_with_proxy(proxy_url)
                    self.logger.debug("新代理连接预热完成")
                except Exception as e:
                    self.logger.warning(f"连接预热失败，但不影响正常使用: {e}")

                self.logger.info("强化连接池刷新完成，代理切换已生效")
            except Exception as e:
                self.logger.error(f"强化连接池刷新时出错: {str(e)}")
                # 如果强化刷新失败，尝试基础刷新
                try:
                    await self._get_async_session(force_recreate=True)
                    self.logger.info("基础连接池刷新完成")
                except Exception as e2:
                    self.logger.error(f"基础连接池刷新也失败: {str(e2)}")
            finally:
                self._is_refreshing = False

    async def warmup_connections(self):
        """预热连接池"""
        try:
            self.logger.info("开始预热HTTP连接池...")
            # 住房监控连接器会自动预热
            await self._get_async_session()
            self.logger.info("HTTP连接池预热完成")
        except Exception as e:
            self.logger.error(f"预热连接池时出错: {str(e)}")

    def get_performance_stats(self) -> Dict[str, Any]:
        """获取性能统计信息"""
        stats = self.request_stats.copy()

        # 计算成功率
        if stats['total_requests'] > 0:
            stats['success_rate'] = stats['successful_requests'] / stats['total_requests']
        else:
            stats['success_rate'] = 0.0

        # 获取连接器性能指标
        housing_metrics = self.housing_connector.adaptive_manager.metrics.get_metrics()
        stats.update({
            'connector_success_rate': housing_metrics.get('success_rate', 0.0),
            'connector_avg_response_time': housing_metrics.get('avg_response_time', 0.0),
            'connector_error_rate': housing_metrics.get('connection_error_rate', 0.0),
            'current_profile': self.housing_connector.adaptive_manager.current_profile
        })

        return stats

    def log_performance_stats(self):
        """记录性能统计到日志"""
        stats = self.get_performance_stats()
        self.logger.info(f"HTTP客户端性能统计: "
                        f"总请求:{stats['total_requests']}, "
                        f"成功率:{stats['success_rate']:.2%}, "
                        f"平均响应时间:{stats['avg_response_time']:.3f}s, "
                        f"当前配置:{stats['current_profile']}")

    def get_current_user_agent(self) -> str:
        """获取当前User-Agent"""
        try:
            return self.header_generator.get_current_user_agent()
        except Exception as e:
            self.logger.warning(f"获取User-Agent失败: {e}")
            return "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36"

    def force_refresh_headers(self):
        """强制刷新请求头配置"""
        try:
            self.header_generator.force_refresh()
            self.logger.info("已强制刷新动态请求头配置")
        except Exception as e:
            self.logger.warning(f"刷新请求头配置失败: {e}")

    def get_header_info(self) -> Dict[str, any]:
        """获取当前请求头信息"""
        try:
            return self.header_generator.get_refresh_info()
        except Exception as e:
            self.logger.warning(f"获取请求头信息失败: {e}")
            return {"error": "获取失败"}

    async def __aenter__(self):
        """异步上下文管理器入口"""
        return self

    async def __aexit__(self, exc_type, exc_val, exc_tb):
        """异步上下文管理器退出"""
        await self.close_all()


# 保持向后兼容性
class HttpClient(OptimizedHttpClient):
    """HTTP客户端 - 向后兼容的别名"""

    def __init__(self, proxy_manager: ProxyManager, logger: Optional[Logger] = None):
        super().__init__(proxy_manager, logger)
        # 为了向后兼容，保留一些原有属性
        self.pool_monitor = None

    def get_connection_pool_stats(self) -> dict:
        """获取连接池统计信息 - 向后兼容"""
        return self.get_performance_stats()

    def log_connection_pool_stats(self):
        """记录连接池统计到日志 - 向后兼容"""
        self.log_performance_stats()

    def is_connection_pool_healthy(self) -> bool:
        """检查连接池健康状态 - 向后兼容"""
        stats = self.get_performance_stats()
        success_rate = stats.get('success_rate', 0.0)
        return success_rate > 0.8  # 成功率大于80%认为健康

    async def rebuild_connector_if_needed(self) -> bool:
        """根据连接池健康状态重建连接器 - 向后兼容"""
        if not self.is_connection_pool_healthy():
            self.logger.warning("检测到连接池不健康，触发自适应调整...")
            await self.force_refresh_connections()
            return True
        return True