import requests
from requests.exceptions import RequestException
import base64
import json
from json import JSONDecodeError
import time
import io
import os
import random
import sys
from pathlib import Path
import mimetypes # 导入 mimetypes
from datetime import datetime, timedelta
from concurrent.futures import ThreadPoolExecutor, as_completed

# 导入日志记录器
from core.utils.unified_logging import Logger

# 导入动态请求头生成器
sys.path.append(os.path.join(os.path.dirname(__file__), '..'))
from core.network.dynamic_header_generator import get_global_header_generator

# 尝试导入图像处理相关库（保留最基础的依赖）
try:
    from PIL import Image
    PIL_AVAILABLE = True
except ImportError:
    PIL_AVAILABLE = False
    # 在没有logger实例的情况下，这里只能用print
    print("警告: PIL (Pillow) 库未安装，图像处理功能将受限")

class HuhhothomeLogin:
    """简化版登录客户端 - 仅保留核心功能供其他模块调用"""

    def __init__(self, proxy_config=None, logger=None, proxy_manager=None):
        self.logger = logger or Logger("HuhhothomeLogin")
        self.proxy_manager = proxy_manager  # 集成代理管理器
        self.session = requests.Session()
        # 禁用SSL证书验证以解决证书过期问题
        self.session.verify = False

        # 获取全局动态请求头生成器
        self.header_generator = get_global_header_generator()

        # 配置代理（如果提供）
        if proxy_config:
            proxies = {}
            # 支持标准代理格式 {'http': 'xxx', 'https': 'xxx'}
            if proxy_config.get('http'):
                proxies['http'] = proxy_config['http']
            if proxy_config.get('https'):
                proxies['https'] = proxy_config['https']
            # 兼容旧格式 {'http_proxy': 'xxx', 'https_proxy': 'xxx'}
            if proxy_config.get('http_proxy'):
                proxies['http'] = proxy_config['http_proxy']
            if proxy_config.get('https_proxy'):
                proxies['https'] = proxy_config['https_proxy']
            if proxies:
                self.session.proxies.update(proxies)
                self.logger.info(f"登录模块已配置代理: {proxies}")

        self.base_url = "https://www.huhhothome.cn"
        self.application = "jqHj7ddxI1smOEkmKSD"
        self.domainid = "LAMDyAh4HSdnug1KdKL"

        # 基础请求头 - 使用动态生成的请求头作为基础
        self.headers = self._get_dynamic_headers()

        # 添加登录模块特有的请求头
        self.headers.update({
            "Content-Type": "application/json",
            "Accept": "application/json, text/javascript, */*; q=0.01",
            "Referer": "https://www.huhhothome.cn/web/index.html",
            "Requesttype": "-"
        })

        self.access_token = ""
        self.user_info = {}

        self.logger.debug(f"登录模块初始化完成，使用动态请求头: {self.headers.get('User-Agent', '')[:50]}...")

    def _get_dynamic_headers(self):
        """获取动态生成的请求头"""
        return self.header_generator.get_random_headers()

    def _refresh_headers(self):
        """刷新请求头（20%概率刷新，避免过于频繁）"""
        if random.random() < 0.2:  # 20%概率刷新
            new_headers = self._get_dynamic_headers()
            # 保留登录模块特有的头信息
            special_headers = {
                "Content-Type": self.headers.get("Content-Type"),
                "Accept": self.headers.get("Accept"),
                "Referer": self.headers.get("Referer"),
                "Requesttype": self.headers.get("Requesttype"),
                "Membertoken": self.headers.get("Membertoken")
            }

            self.headers = new_headers
            # 重新添加特有头信息
            for key, value in special_headers.items():
                if value:
                    self.headers[key] = value

    def _make_request(self, method, url, **kwargs):
        """统一的请求方法，自动使用动态请求头，支持407错误自动重试"""
        max_retries = 3
        original_kwargs = kwargs.copy()

        for attempt in range(max_retries):
            try:
                # 刷新请求头
                self._refresh_headers()

                # 确保使用最新的请求头
                if 'headers' not in kwargs:
                    kwargs['headers'] = self.headers.copy()
                else:
                    # 合并用户提供的请求头
                    merged_headers = self.headers.copy()
                    merged_headers.update(kwargs['headers'])
                    kwargs['headers'] = merged_headers

                # 如果是文件上传，移除Content-Type，让requests库自动处理
                if 'files' in kwargs and 'Content-Type' in kwargs['headers']:
                    del kwargs['headers']['Content-Type']

                # 更新代理配置（如果有代理管理器）
                if self.proxy_manager and attempt > 0:  # 第一次失败后更新代理
                    if self.proxy_manager.current_proxy:
                        new_proxy_dict = self.proxy_manager.get_proxy_dict()
                        if new_proxy_dict:
                            self.session.proxies.update(new_proxy_dict)
                            self.logger.info(f"尝试 {attempt + 1}: 使用新代理 {self.proxy_manager.current_proxy}")

                # 发送请求
                if method.upper() == 'GET':
                    response = self.session.get(url, **kwargs)
                elif method.upper() == 'POST':
                    response = self.session.post(url, **kwargs)
                else:
                    self.logger.error(f"不支持的请求方法: {method}")
                    raise ValueError(f"不支持的请求方法: {method}")

                return response

            except RequestException as e:
                error_msg = str(e)
                self.logger.warning(f"网络请求失败 (尝试 {attempt + 1}/{max_retries}): {method} {url} | {error_msg[:100]}")

                # 检测407错误或其他代理相关错误
                is_407_error = any(keyword in error_msg.lower() for keyword in [
                    '407', 'proxy authentication required', 'tunnel connection failed: 407'
                ])

                is_network_exception = 'networkexception' in error_msg.lower() or type(e).__name__ == 'NetworkException'

                is_proxy_error = any(keyword in error_msg.lower() for keyword in [
                    'proxy', 'connection refused', 'timeout', 'max retries exceeded'
                ])

                # 如果是407错误或NetworkException且有代理管理器，立即切换代理
                if (is_407_error or is_network_exception) and self.proxy_manager:
                    if is_407_error:
                        self.logger.warning(f" 检测到407错误，代理IP失效，立即切换新代理")
                        error_type = "407_auth_required"
                    elif is_network_exception:
                        self.logger.warning(f" 检测到NetworkException，代理IP网络失效，立即切换新代理")
                        error_type = "network_exception"

                    try:
                        # 异步调用代理切换（在同步环境中）
                        import asyncio
                        try:
                            # 尝试在已存在的事件循环中运行
                            loop = asyncio.get_event_loop()
                            if loop.is_running():
                                # 在已运行的循环中创建任务
                                asyncio.create_task(
                                    self.proxy_manager.handle_proxy_failure(
                                        self.proxy_manager.current_proxy or "unknown",
                                        error_type
                                    )
                                )
                                # 短暂等待代理切换
                                import time
                                time.sleep(0.5)
                            else:
                                # 创建新的事件循环运行
                                asyncio.run(
                                    self.proxy_manager.handle_proxy_failure(
                                        self.proxy_manager.current_proxy or "unknown",
                                        error_type
                                    )
                                )
                        except RuntimeError:
                            # 备用方案：同步切换代理
                            success = self.proxy_manager.update_proxy_sync()
                            self.logger.info(f"同步代理切换: {'成功' if success else '失败'}")

                    except Exception as proxy_switch_error:
                        self.logger.warning(f"代理切换失败: {proxy_switch_error}")

                # 如果是其他代理错误且有代理管理器，也尝试切换
                elif is_proxy_error and self.proxy_manager and attempt == 0:
                    self.logger.info(f"检测到代理相关错误，尝试切换代理后重试")
                    try:
                        success = self.proxy_manager.update_proxy_sync()
                        if success:
                            # 重置kwargs以使用新代理
                            kwargs = original_kwargs.copy()
                            continue
                    except Exception as proxy_switch_error:
                        self.logger.warning(f"代理切换失败: {proxy_switch_error}")

                # 最后一次尝试失败，抛出异常
                if attempt == max_retries - 1:
                    self.logger.error(f"网络请求最终失败: {method} {url}", context=e)
                    raise

                # 等待后重试
                import time
                time.sleep(0.5 * (attempt + 1))  # 递增等待时间

                # 重置kwargs准备下次重试
                kwargs = original_kwargs.copy()

    def get_user_info(self):
        """获取用户信息"""
        url = f"{self.base_url}/api/dynamicapi/user/detail"
        params = {
            "application": self.application
        }

        try:
            response = self._make_request('GET', url, params=params)
            response.raise_for_status()

            result = response.json()
            if result["errcode"] == 0.0:
                self.user_info = result["data"]
                return self.user_info
            else:
                self.logger.warning(f"获取用户信息失败: {result.get('errmsg', '未知错误')}")
                return None
        except (RequestException, JSONDecodeError) as e:
            self.logger.error("获取用户信息请求失败", context=e)
            return None

    def _get_enhanced_content_type(self, file_path):
        """增强的文件类型检测，包含文件头验证和自动修复"""
        file_name = os.path.basename(file_path).lower()

        # 优先使用文件扩展名映射（更准确）
        extension_mapping = {
            '.jpg': 'image/jpeg',
            '.jpeg': 'image/jpeg',
            '.png': 'image/png',
            '.gif': 'image/gif',
            '.bmp': 'image/bmp',
            '.pdf': 'application/pdf',
            '.doc': 'application/msword',
            '.docx': 'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
            '.txt': 'text/plain'
        }

        # 检查文件头magic number以验证真实格式
        actual_format = self._detect_file_format_by_header(file_path)

        # 根据扩展名确定期望的类型
        expected_mime = None
        for ext, mime_type in extension_mapping.items():
            if file_name.endswith(ext):
                expected_mime = mime_type
                self.logger.debug(f"根据扩展名确定期望MIME类型: {ext} -> {mime_type}")
                break

        # 如果检测到文件头与扩展名不符，记录警告并尝试修复
        if actual_format and expected_mime:
            if actual_format != expected_mime.split('/')[1]:  # 比较格式部分
                self.logger.warning(f"文件格式不一致：扩展名期望{expected_mime}，但文件头显示{actual_format}")

                # 如果是图片文件，尝试修复
                if expected_mime.startswith('image/') and actual_format in ['jpeg', 'png', 'gif', 'bmp']:
                    repaired_path = self._repair_image_file(file_path, expected_mime.split('/')[1])
                    if repaired_path and repaired_path != file_path:
                        self.logger.info(f"文件已修复并保存为: {repaired_path}")
                        # 递归调用，使用修复后的文件
                        return self._get_enhanced_content_type(repaired_path)

        # 如果扩展名匹配，返回对应的MIME类型
        if expected_mime:
            return expected_mime

        # 回退到标准库检测
        content_type, _ = mimetypes.guess_type(file_path)
        if content_type:
            self.logger.debug(f"标准库检测MIME类型: {content_type}")
            return content_type

        # 最后的默认值
        self.logger.debug(f"使用默认MIME类型: application/octet-stream")
        return 'application/octet-stream'

    def _detect_file_format_by_header(self, file_path):
        """通过文件头检测实际文件格式"""
        try:
            with open(file_path, 'rb') as f:
                header = f.read(12)  # 读取前12字节通常足够识别格式

            # 常见文件格式的magic number
            if header.startswith(b'\xff\xd8\xff'):
                return 'jpeg'
            elif header.startswith(b'\x89PNG\r\n\x1a\n'):
                return 'png'
            elif header.startswith(b'GIF87a') or header.startswith(b'GIF89a'):
                return 'gif'
            elif header.startswith(b'BM'):
                return 'bmp'
            elif header.startswith(b'%PDF'):
                return 'pdf'
            elif header.startswith(b'\xd0\xcf\x11\xe0'):
                return 'doc'  # Office文档
            else:
                self.logger.debug(f"未识别的文件头: {header[:8].hex()}")
                return None

        except Exception as e:
            self.logger.debug(f"文件头检测失败: {e}")
            return None

    def _repair_image_file(self, file_path, target_format):
        """尝试修复图片文件格式"""
        if not PIL_AVAILABLE:
            self.logger.warning("PIL不可用，无法修复图片文件")
            return None

        try:
            # 使用PIL重新保存图片以修复格式问题
            with Image.open(file_path) as img:
                # 确保图片处于RGB模式（对JPEG必需）
                if target_format.lower() == 'jpeg' and img.mode in ('RGBA', 'LA'):
                    # JPEG不支持透明度，转换为RGB
                    rgb_img = Image.new('RGB', img.size, (255, 255, 255))
                    if img.mode == 'RGBA':
                        rgb_img.paste(img, mask=img.split()[-1])  # 使用alpha通道作为mask
                    else:
                        rgb_img.paste(img)
                    img = rgb_img
                elif target_format.lower() == 'jpeg' and img.mode != 'RGB':
                    img = img.convert('RGB')

                # 生成修复后的文件路径
                base_name = os.path.splitext(file_path)[0]
                if target_format.lower() == 'jpeg':
                    repaired_path = f"{base_name}_repaired.jpg"
                else:
                    repaired_path = f"{base_name}_repaired.{target_format.lower()}"

                # 保存修复后的文件
                save_kwargs = {}
                if target_format.lower() == 'jpeg':
                    save_kwargs['quality'] = 95
                    save_kwargs['optimize'] = True

                img.save(repaired_path, format=target_format.upper(), **save_kwargs)

                self.logger.info(f"图片文件已修复: {file_path} -> {repaired_path}")
                return repaired_path

        except Exception as e:
            self.logger.error(f"图片文件修复失败: {e}")
            return None

    def upload_file(self, local_file_path):
        """上传单个文件到服务器（增强版本，支持格式检测和修复）"""
        if not self.access_token:
            self.logger.error("无法上传文件，请先登录。")
            return None

        if not os.path.exists(local_file_path):
            self.logger.error(f"本地文件不存在 -> {local_file_path}")
            return None

        url = f"{self.base_url}/api/imageUploadServlet"

        # 获取动态请求头并移除Content-Type，让requests自动处理
        upload_headers = self._get_dynamic_headers()
        # 移除Content-Type，让requests自动处理
        upload_headers.pop("Content-Type", None)
        # 根据抓包信息，这个接口需要不同的Accept头
        upload_headers["Accept"] = "application/json; charset=utf-8"

        # 检查文件大小和格式
        file_size = os.path.getsize(local_file_path)
        if file_size == 0:
            self.logger.error(f"文件为空，无法上传 -> {local_file_path}")
            return None
        elif file_size > 50 * 1024 * 1024:  # 50MB限制
            self.logger.warning(f"文件过大 ({file_size/1024/1024:.1f}MB)，可能上传失败 -> {local_file_path}")

        file_name = os.path.basename(local_file_path)

        # 增强的文件类型检测（包含文件头验证和自动修复）
        content_type = self._get_enhanced_content_type(local_file_path)

        # 如果文件被修复了，更新文件路径和名称
        current_file_path = local_file_path
        if local_file_path != local_file_path and os.path.exists(local_file_path + "_repaired"):
            # 检查是否生成了修复文件
            potential_repaired = local_file_path.replace('.jpg', '_repaired.jpg').replace('.jpeg', '_repaired.jpg')
            if os.path.exists(potential_repaired):
                current_file_path = potential_repaired
                file_name = os.path.basename(current_file_path)
                self.logger.info(f"使用修复后的文件进行上传: {file_name}")

        # 文件验证信息记录
        self.logger.debug(f"文件上传详情: 文件={file_name}, 大小={file_size}字节, MIME类型={content_type}")

        # 如果是图片文件，进行额外验证
        if content_type.startswith('image/') and PIL_AVAILABLE:
            image_info = self._validate_image_file(current_file_path)
            if image_info:
                self.logger.debug(f"图片验证: 格式={image_info['format']}, 尺寸={image_info['size']}, 模式={image_info['mode']}")
            else:
                self.logger.warning(f"图片文件验证失败，但仍将尝试上传")

        try:
            with open(current_file_path, 'rb') as f:
                files = {
                    'File': (file_name, f, content_type)
                }

                self.logger.info(f"正在上传文件: {file_name} ({file_size/1024:.1f}KB)...")
                response = self._make_request('POST', url, headers=upload_headers, files=files, timeout=30)
                response.raise_for_status()

                # 增强的响应处理
                try:
                    result = response.json()
                except JSONDecodeError as json_err:
                    self.logger.error(f"  -> 上传失败: 服务器响应不是有效JSON -> {file_name}")
                    self.logger.debug(f"     响应状态码: {response.status_code}")
                    self.logger.debug(f"     响应内容: {response.text[:300]}")
                    return None

                if result.get("errcode") == 0:
                    file_data = result.get("data")[0] # 响应是个数组
                    self.logger.info(f"  -> 上传成功: {file_data.get('name')}")

                    # 如果使用了修复文件，清理临时文件
                    if current_file_path != local_file_path and current_file_path.endswith('_repaired.jpg'):
                        try:
                            os.remove(current_file_path)
                            self.logger.debug(f"已清理修复临时文件: {current_file_path}")
                        except:
                            pass

                    return file_data # 返回 {'path': '...', 'name': '...'}
                else:
                    error_msg = result.get('errmsg', '未知错误')
                    self.logger.error(f"  -> 上传失败: {error_msg}")

                    # 增强的错误诊断
                    self.logger.debug(f"     完整服务器响应: {result}")
                    self.logger.debug(f"     文件信息: 名称={file_name}, 大小={file_size}, MIME={content_type}")

                    # 特定错误的建议和自动修复尝试
                    if "格式" in error_msg or "png" in error_msg.lower() or "jpg" in error_msg.lower():
                        self.logger.warning(f"     建议: 文件格式问题，尝试转换为PNG格式或检查文件完整性")

                        # 如果是格式错误且还没有尝试过修复，尝试转换为PNG
                        if current_file_path == local_file_path and content_type.startswith('image/'):
                            self.logger.info(f"     尝试将文件转换为PNG格式并重新上传...")
                            png_path = self._convert_image_to_png(local_file_path)
                            if png_path:
                                return self.upload_file(png_path)  # 递归调用上传PNG版本

                    return None
        except FileNotFoundError:
            self.logger.error(f"  -> 上传失败: 文件不存在 -> {current_file_path}")
            return None
        except PermissionError:
            self.logger.error(f"  -> 上传失败: 无权限读取文件 -> {current_file_path}")
            return None
        except requests.exceptions.Timeout:
            self.logger.error(f"  -> 上传失败: 请求超时 (30秒) -> {file_name}")
            return None
        except requests.exceptions.ConnectionError as e:
            self.logger.error(f"  -> 上传失败: 网络连接错误 -> {file_name}")
            self.logger.debug(f"     连接错误详情: {str(e)}")
            return None
        except (RequestException, Exception) as e:
            self.logger.error(f"  -> 上传失败: {type(e).__name__} -> {file_name}")
            self.logger.debug(f"     错误详情: {str(e)}")
            return None

    def _validate_image_file(self, file_path):
        """验证图片文件的有效性"""
        if not PIL_AVAILABLE:
            return None

        try:
            with Image.open(file_path) as img:
                return {
                    'format': img.format,
                    'size': img.size,
                    'mode': img.mode
                }
        except Exception as e:
            self.logger.debug(f"图片验证失败: {e}")
            return None

    def _convert_image_to_png(self, file_path):
        """将图片转换为PNG格式"""
        if not PIL_AVAILABLE:
            self.logger.warning("PIL不可用，无法转换图片格式")
            return None

        try:
            base_name = os.path.splitext(file_path)[0]
            png_path = f"{base_name}_converted.png"

            with Image.open(file_path) as img:
                # 确保图片处于RGBA模式以支持透明度
                if img.mode != 'RGBA':
                    img = img.convert('RGBA')

                img.save(png_path, format='PNG', optimize=True)

            self.logger.info(f"图片已转换为PNG: {file_path} -> {png_path}")
            return png_path

        except Exception as e:
            self.logger.error(f"图片转换失败: {e}")
            return None

    def get_idcard_info(self, server_path, card_side="front"):
        """调用AI接口，从上传的身份证图片中识别信息"""
        if not self.access_token:
            self.logger.error("无法识别身份证，请先登录。")
            return None

        url = f"{self.base_url}/api/dynamicapi/ai/viewidcard"
        params = {"application": self.application}
        data = {
            "cardSide": card_side,
            "path": server_path
        }

        self.logger.info(f"  -> 正在调用AI识别 ({card_side})...")
        try:
            response = self._make_request('POST', url, params=params, json=data)
            response.raise_for_status()
            result = response.json()
            if result.get("errcode") == 0.0:
                self.logger.info(f"  -> AI识别成功!")
                return result.get("data", {})
            else:
                self.logger.warning(f"  -> AI识别失败: {result.get('errmsg', '未知错误')}")
                return None
        except (RequestException, JSONDecodeError) as e:
            self.logger.error(f"  -> AI识别请求失败", context=e)
            return None

    def select_house(self, house_id):
        """选择房源，获取房源详细信息"""
        if not self.access_token:
            self.logger.error("请先登录")
            return None

        url = f"{self.base_url}/api/dynamicapi/usernot/detail"
        params = {
            "application": self.application,
            "id": house_id
        }

        self.logger.info(f"正在选择房源, ID: {house_id}...")
        try:
            # 根据您提供的日志，这是一个POST请求，但没有正文内容
            response = self._make_request('POST', url, params=params)
            response.raise_for_status()

            result = response.json()
            if result.get("errcode") == 0.0:
                house_data = result.get("data", {})
                house_name = house_data.get("houseestatename", "未知名称")
                position = house_data.get("position", "")
                self.logger.info(f"房源选择成功!")
                self.logger.info(f"房源名称: {house_name}")
                self.logger.info(f"具体位置: {position}")
                return house_data
            else:
                self.logger.error(f"选择房源失败: {result.get('errmsg', '未知错误')}")
                return None
        except (RequestException, JSONDecodeError) as e:
            self.logger.error(f"选择房源请求失败", context=e)
            return None

    def submit_application(self, application_data, house_id=None):
        """提交租房申请"""
        if not self.access_token:
            self.logger.error("请先登录")
            return False

        url = f"{self.base_url}/api/dynamicapi/houseapprove/approve"
        params = {
            "application": self.application
        }

        self.logger.info("正在提交申请...")
        try:
            # 手动序列化JSON数据
            json_data = json.dumps(application_data, ensure_ascii=False)

            # 创建一个新的headers副本，确保Content-Type正确设置
            headers = self._get_dynamic_headers()
            headers["Content-Type"] = "application/json"

            # 动态构造 Referer 请求头以匹配真实浏览器行为
            if house_id:
                # 对中文字符进行URL编码以避免编码错误
                import urllib.parse
                encoded_param = urllib.parse.quote("否", safe='')
                referer = f"https://www.huhhothome.cn/web/personal.html?applypage=true&houseId={house_id}&onlypersonnel={encoded_param}"
                headers["Referer"] = referer
                self.logger.debug(f"使用动态Referer: {referer}")
            else:
                # 保持向后兼容性，使用默认Referer
                headers["Referer"] = "https://www.huhhothome.cn/web/index.html"
                self.logger.debug("使用默认Referer（未提供house_id）")

            # 使用data参数而不是json参数，手动传递序列化后的JSON字符串
            response = self._make_request(
                'POST',
                url,
                headers=headers,
                params=params,
                data=json_data.encode('utf-8')
            )
            response.raise_for_status()

            # 尝试解析响应
            try:
                result = response.json()
                if result.get("errcode") == 0.0:
                    success_message = result.get('errmsg', '申请提交成功')
                    self.logger.info(f"申请提交成功: {success_message}")
                    return {
                        'success': True,
                        'message': success_message
                    }
                else:
                    error_message = result.get('errmsg', '未知错误')
                    self.logger.error(f"提交申请失败: {error_message}")
                    return {
                        'success': False,
                        'message': error_message
                    }
            except JSONDecodeError as json_err:
                error_msg = "服务器响应格式错误"
                self.logger.error(f"解析响应JSON失败", context=json_err)
                self.logger.error(f"响应状态码: {response.status_code}")
                self.logger.error(f"响应内容: {response.text[:200]}...")  # 只打印前200个字符
                return {
                    'success': False,
                    'message': error_msg
                }
        except RequestException as e:
            error_msg = f"网络请求失败: {str(e)}"
            self.logger.error(f"提交申请请求失败", context=e)
            return {
                'success': False,
                'message': error_msg
            }

    def auto_fill_config_from_user_info(self, config_data):
        """从用户信息自动填充配置文件中的占位符"""
        if not self.user_info:
            self.logger.warning("  -> 用户信息未获取，无法进行自动填充")
            return config_data

        self.logger.info("  -> 开始自动填充配置文件...")

        # 显示用户信息用于调试
        user_itemmap = self.user_info.get("itemmap", {})
        self.logger.debug(f"  -> 用户信息详情:")
        self.logger.debug(f"     - 真实姓名: {user_itemmap.get('name', '未知')}")
        self.logger.debug(f"     - 手机号: {user_itemmap.get('phone', '未知')}")
        self.logger.debug(f"     - 工作单位: {user_itemmap.get('workname', '未知')}")
        self.logger.debug(f"     - 工作地址: {user_itemmap.get('addr', '未知')}")
        self.logger.debug(f"     - 性别: {user_itemmap.get('sex', '未知')}")

        # 显示地区信息用于调试
        user_country = user_itemmap.get("country", "")
        user_township = user_itemmap.get("township", "")
        user_village = user_itemmap.get("village", "")
        self.logger.debug(f"     - 地区代码: country={user_country}, township={user_township}, village={user_village}")

        # 优先处理地区代码：如果用户信息中有完整的地区代码，直接使用（覆盖配置文件中的固定值）
        region_filled_count = 0
        if user_country and user_township and user_village:
            if "application_data" in config_data and "itemmap" in config_data["application_data"]:
                config_data["application_data"]["itemmap"]["country"] = user_country
                config_data["application_data"]["itemmap"]["township"] = user_township
                config_data["application_data"]["itemmap"]["village"] = user_village

                self.logger.info(f"  -> ✅ 使用用户个人信息中的地区代码:")
                self.logger.info(f"     - country: {user_country}")
                self.logger.info(f"     - township: {user_township}")
                self.logger.info(f"     - village: {user_village}")
                region_filled_count = 3
        else:
            self.logger.info("  ->   用户信息中未找到完整的地区代码，保持配置文件默认值")

        # 扩展的字段映射，包含更多个人信息（地区代码已在上面单独处理）
        auto_fill_mappings = {
            # 基本信息
            "login_phone": user_itemmap.get("phone", ""),
            "application_data.itemmap.company": user_itemmap.get("workname", ""),
            "application_data.itemmap.addr": user_itemmap.get("addr", ""),

            # 个人基本信息
            "application_data.itemmap.name": user_itemmap.get("name", ""),
            "application_data.itemmap.phone": user_itemmap.get("phone", ""),
            "application_data.itemmap.sex": user_itemmap.get("sex", ""),
            "application_data.itemmap.cardno": user_itemmap.get("cardno", ""),
            "application_data.itemmap.cardaddr": user_itemmap.get("cardaddr", ""),

            # 注意：地区代码(country/township/village)已在上面单独处理，不在此映射中
        }

        # 执行自动填充
        filled_count = 0
        for config_path, user_value in auto_fill_mappings.items():
            if not user_value:  # 如果用户信息中没有这个值，跳过
                continue

            # 解析配置路径并设置值
            if config_path == "login_phone":
                current_login_phone = config_data.get("login_phone", "")
                # 精确匹配占位符，而不是包含匹配
                placeholder_values = ["申请人手机号", "", None]
                if current_login_phone in placeholder_values:
                    config_data["login_phone"] = user_value
                    self.logger.debug(f"    - 自动填充登录手机号: '{current_login_phone}' -> '{user_value}'")
                    filled_count += 1
                else:
                    self.logger.debug(f"    - 跳过登录手机号填充: 当前值 '{current_login_phone}' 不是占位符")

            elif config_path.startswith("application_data.itemmap."):
                field_name = config_path.split(".")[-1]

                # 跳过地区代码字段，因为它们已经在前面单独处理了
                if field_name in ["country", "township", "village"]:
                    self.logger.debug(f"    - 跳过{field_name}字段: 已在地区代码处理中完成")
                    continue

                if "application_data" in config_data and "itemmap" in config_data["application_data"]:
                    current_value = config_data["application_data"]["itemmap"].get(field_name, "")

                    # 精确的占位符列表，只匹配完整的占位符值（移除了地区代码相关字段）
                    exact_placeholders = {
                        "company": ["公司名称", "工作单位", "单位名称", "申请人的公司"],
                        "addr": ["公司地址", "工作地址", "单位地址", "地址", "申请人的地址"],
                        "name": ["姓名", "申请人姓名", "用户姓名"],
                        "phone": ["手机号", "电话号码", "联系电话", "申请人手机号"],
                        "sex": ["性别"],
                        "cardno": ["身份证号", "身份证", "证件号"],
                        "cardaddr": ["身份证地址", "户籍地址", "证件地址"]
                    }

                    placeholders = exact_placeholders.get(field_name, [])
                    # 使用精确匹配而不是包含匹配
                    is_placeholder = (not current_value or
                                    current_value in ["", None] or
                                    str(current_value).strip() in placeholders)

                    if is_placeholder:
                        config_data["application_data"]["itemmap"][field_name] = user_value
                        self.logger.debug(f"    - 自动填充{field_name}: '{current_value}' -> '{user_value}'")
                        filled_count += 1
                    else:
                        self.logger.debug(f"    - 跳过{field_name}填充: 当前值 '{current_value}' 不是占位符")

        # 计算总填充数量（包括地区代码）
        total_filled_count = filled_count + region_filled_count

        if total_filled_count > 0:
            self.logger.info(f"  -> 自动填充完成，共填充了 {total_filled_count} 个字段")
            if region_filled_count > 0:
                self.logger.info(f"     其中地区代码字段: {region_filled_count} 个")
            if filled_count > 0:
                self.logger.info(f"     其他字段: {filled_count} 个")
        else:
            self.logger.info("  -> 没有需要自动填充的字段")

        return config_data


def run_application_for_profile(login_client, profile_dir, house_id, cache_manager=None):
    """
    为指定的用户配置档案执行完整的房源申请流程。
    支持预上传缓存以大幅提升抢房速度。

    Args:
        login_client: 登录客户端实例
        profile_dir: 用户配置目录
        house_id: 房源ID
        cache_manager: 文件上传缓存管理器（可选）
    """
    # 从 login_client 获取 logger
    logger = login_client.logger

    # 记录开始时间（精确到毫秒）
    start_time = datetime.now()
    logger.info(f"\n{'='*20} 开始为 '{os.path.basename(profile_dir)}' 申请房源 {'='*20}")
    logger.info(f"开始时间: {start_time.strftime('%Y-%m-%d %H:%M:%S.%f')[:-3]}")

    # 1. 读取配置文件
    config_path = os.path.join(profile_dir, "config.json")
    try:
        with open(config_path, 'r', encoding='utf-8') as f:
            config = json.load(f)
        logger.info("  -> 成功读取配置文件。")
    except (IOError, JSONDecodeError) as e:
        logger.error(f"  -> 读取配置文件 {config_path} 失败", context=e)
        return False

    # 1.1 显示当前配置文件状态（填充前）
    logger.debug(f"  -> 配置文件填充前状态:")
    if "application_data" in config and "itemmap" in config["application_data"]:
        itemmap = config["application_data"]["itemmap"]
        logger.debug(f"     - 姓名: {itemmap.get('name', '未设置')}")
        logger.debug(f"     - 公司: {itemmap.get('company', '未设置')}")
        logger.debug(f"     - 地址: {itemmap.get('addr', '未设置')}")
        logger.debug(f"     - 性别: {itemmap.get('sex', '未设置')}")
    logger.debug(f"     - 登录手机号: {config.get('login_phone', '未设置')}")

    # 1.1 自动填充配置文件
    config = login_client.auto_fill_config_from_user_info(config)

    # 1.1.1 显示填充后的配置文件状态
    logger.debug(f"  -> 配置文件填充后状态:")
    if "application_data" in config and "itemmap" in config["application_data"]:
        itemmap = config["application_data"]["itemmap"]
        logger.debug(f"     - 姓名: {itemmap.get('name', '未设置')}")
        logger.debug(f"     - 公司: {itemmap.get('company', '未设置')}")
        logger.debug(f"     - 地址: {itemmap.get('addr', '未设置')}")
        logger.debug(f"     - 性别: {itemmap.get('sex', '未设置')}")
    logger.debug(f"     - 登录手机号: {config.get('login_phone', '未设置')}")

    # 1.1 获取并验证 application_data 结构
    application_data = config.get("application_data")
    if not application_data or not isinstance(application_data, dict):
        logger.error(f"  -> 配置文件 {config_path} 中缺少 'application_data' 对象。")
        return False

    itemmap = application_data.get("itemmap", {})

    # 1.2 验证必填字段
    required_fields = {
        "group": "所属群体",
        "sex": "性别",
        "marriage": "婚姻状况",
        "education": "学历",
        "company": "工作单位",
        "addr": "地址"
    }

    missing_fields = []
    for field, desc in required_fields.items():
        if not itemmap.get(field):
            missing_fields.append(f"{desc}({field})")

    if missing_fields:
        logger.error(f"  -> 配置文件 'application_data.itemmap' 中缺少以下必填字段:")
        for field in missing_fields:
            logger.error(f"     - {field}")
        logger.error(f"\n  请在配置文件 {config_path} 中添加这些字段并重试。")
        return False

    # 2. 选择房源
    house_info = login_client.select_house(house_id)
    if not house_info:
        logger.error("  -> 选择房源失败，终止申请流程。")
        return False

    # 3. 准备并填充申请数据 (优化版：支持预上传缓存)
    try:
        user_id = os.path.basename(profile_dir)

        # === 阶段1: 检查缓存或收集需要上传的文件 ===
        if cache_manager:
            logger.info(f"  -> 🚀 使用预上传缓存模式，大幅提升抢房速度...")
            upload_results = {}

            # 收集所有需要的文件并检查缓存
            files_to_upload = []
            file_mapping = {}  # 映射: 实际文件路径 -> 配置文件名
            family_members_from_config = config.get("application_data", {}).get("familys", [])
            attachment_keys = ["work", "nohouse", "educationattachment", "approvefile", "household", "rewardattmts", "achievementattmts", "personnel"]

            # 收集家庭成员身份证文件
            for member in family_members_from_config:
                for card_key in ["card0", "card1"]:
                    if card_key in member and isinstance(member[card_key], list):
                        for i, filename in enumerate(member[card_key]):
                            actual_filename = find_flexible_file(profile_dir, filename, logger)
                            actual_file_path = os.path.join(profile_dir, actual_filename)
                            if os.path.exists(actual_file_path):
                                files_to_upload.append(actual_file_path)
                                file_mapping[actual_file_path] = filename

            # 收集附件文件
            itemmap_from_config = config.get("application_data", {}).get("itemmap", {})
            for key in attachment_keys:
                if key in itemmap_from_config and isinstance(itemmap_from_config[key], list):
                    for i, filename in enumerate(itemmap_from_config[key]):
                        actual_filename = find_flexible_file(profile_dir, filename, logger)
                        actual_file_path = os.path.join(profile_dir, actual_filename)
                        if os.path.exists(actual_file_path):
                            files_to_upload.append(actual_file_path)
                            file_mapping[actual_file_path] = filename

            # 去重
            files_to_upload = sorted(list(set(files_to_upload)))

            # 从缓存获取上传结果
            cache_hit_count = 0
            cache_miss_count = 0

            for file_path in files_to_upload:
                cached_result = cache_manager.get_cached_upload(user_id, file_path)
                if cached_result:
                    upload_results[file_path] = cached_result
                    cache_hit_count += 1
                    logger.debug(f"    - 缓存命中: {os.path.basename(file_path)} -> {cached_result['name']}")
                else:
                    cache_miss_count += 1
                    logger.warning(f"    - 缓存未命中，需要实时上传: {os.path.basename(file_path)}")
                    # 实时上传
                    file_info = login_client.upload_file(file_path)
                    if file_info:
                        upload_results[file_path] = file_info
                        # 缓存新的上传结果
                        cache_manager.cache_upload_result(user_id, file_path, file_info)
                        logger.info(f"    - 实时上传成功: {os.path.basename(file_path)} -> {file_info['name']}")
                    else:
                        upload_results[file_path] = None
                        logger.error(f"    - 实时上传失败: {os.path.basename(file_path)}")

            logger.info(f"  -> 📊 缓存统计: 命中{cache_hit_count}个, 未命中{cache_miss_count}个, 总计{len(files_to_upload)}个文件")

            # 缓存模式下，文件已经处理完毕，跳过后续的文件收集和上传阶段
            files_to_upload = []  # 清空列表，避免重复上传

        else:
            # === 传统模式: 收集所有需要上传的文件（智能文件匹配） ===
            logger.info(f"  -> 🔍 使用传统模式，开始智能文件收集...")
            files_to_upload = []
            file_mapping = {}  # 映射: 实际文件路径 -> 配置文件名
            family_members_from_config = config.get("application_data", {}).get("familys", [])
            attachment_keys = ["work", "nohouse", "educationattachment", "approvefile", "household", "rewardattmts", "achievementattmts", "personnel"]

            # 收集家庭成员的身份证文件（使用灵活匹配）
            for member in family_members_from_config:
                for card_key in ["card0", "card1"]:
                    if card_key in member and isinstance(member[card_key], list):
                        for i, filename in enumerate(member[card_key]):
                            # 使用灵活文件查找
                            actual_filename = find_flexible_file(profile_dir, filename, logger)
                            actual_file_path = os.path.join(profile_dir, actual_filename)

                            if os.path.exists(actual_file_path):
                                files_to_upload.append(actual_file_path)
                                file_mapping[actual_file_path] = filename
                                # 更新配置中的文件名为实际找到的文件名
                                member[card_key][i] = actual_filename
                                if actual_filename != filename:
                                    logger.info(f"    - 身份证文件匹配: {filename} -> {actual_filename}")
                            else:
                                logger.warning(f"    - 身份证文件未找到: {filename}")

            # 收集其他附件（使用灵活匹配）
            itemmap_from_config = config.get("application_data", {}).get("itemmap", {})
            for key in attachment_keys:
                if key in itemmap_from_config and isinstance(itemmap_from_config[key], list):
                    for i, filename in enumerate(itemmap_from_config[key]):
                        # 使用灵活文件查找
                        actual_filename = find_flexible_file(profile_dir, filename, logger)
                        actual_file_path = os.path.join(profile_dir, actual_filename)

                        if os.path.exists(actual_file_path):
                            files_to_upload.append(actual_file_path)
                            file_mapping[actual_file_path] = filename
                            # 更新配置中的文件名为实际找到的文件名
                            itemmap_from_config[key][i] = actual_filename
                            if actual_filename != filename:
                                logger.info(f"    - 附件文件匹配: {filename} -> {actual_filename} (类型: {key})")
                        else:
                            logger.warning(f"    - 附件文件未找到: {filename} (类型: {key})")

            # 去重
            files_to_upload = sorted(list(set(files_to_upload)))

        # === 阶段2: 并发上传文件（仅在有文件需要上传时执行） ===
        if not files_to_upload:
            logger.info("  -> 信息: 未发现需要上传的本地文件。")
            # 注意：缓存模式下upload_results已经包含缓存结果，不要重置
            if not cache_manager:
                upload_results = {}  # 仅在传统模式下重置为空
        else:
            logger.info(f"\n  -> 📁 智能文件匹配完成，发现 {len(files_to_upload)} 个待上传文件:")
            for file_path in files_to_upload:
                filename = os.path.basename(file_path)
                original_name = file_mapping.get(file_path, filename)
                if original_name != filename:
                    logger.info(f"     ✅ {original_name} -> {filename} (自动匹配)")
                else:
                    logger.info(f"     📄 {filename} (精确匹配)")
            logger.info(f"  -> 开始并发上传...")

            # 并发上传文件
            upload_results = {} # key: local_file_path, value: server_file_info
            upload_start_time = datetime.now()

            with ThreadPoolExecutor(max_workers=10) as executor:
                future_to_path = {executor.submit(login_client.upload_file, path): path for path in files_to_upload}
                for future in as_completed(future_to_path):
                    local_path = future_to_path[future]
                    try:
                        file_info = future.result()
                        if file_info:
                            upload_results[local_path] = file_info
                            logger.debug(f"    - 并发上传成功: {os.path.basename(local_path)} -> {file_info.get('path')}")
                        else:
                            upload_results[local_path] = None
                            logger.warning(f"    - 并发上传失败: {os.path.basename(local_path)} (无返回信息)")
                    except Exception as exc:
                        upload_results[local_path] = None
                        logger.error(f"    - 并发上传异常: {os.path.basename(local_path)}", context=exc)

            upload_duration = (datetime.now() - upload_start_time).total_seconds()
            successful_uploads = sum(1 for r in upload_results.values() if r)
            logger.info(f"  -> 并发上传完成 (耗时: {upload_duration:.2f}s)。成功 {successful_uploads}/{len(files_to_upload)} 个。")

            # === 阶段2.1: 重试失败的上传 ===
            failed_files = [path for path, result in upload_results.items() if not result]
            if failed_files:
                logger.warning(f"\n  -> 检测到 {len(failed_files)} 个文件上传失败，开始重试...")
                retry_start_time = datetime.now()
                retry_count = 0
                max_retries = 2  # 最多重试2次

                for retry_round in range(1, max_retries + 1):
                    if not failed_files:
                        break

                    logger.info(f"  -> 第 {retry_round} 轮重试，处理 {len(failed_files)} 个失败文件...")
                    current_round_failed = []

                    for file_path in failed_files:
                        try:
                            logger.debug(f"    - 重试上传: {os.path.basename(file_path)}")
                            file_info = login_client.upload_file(file_path)
                            if file_info:
                                upload_results[file_path] = file_info
                                logger.info(f"    - 重试成功: {os.path.basename(file_path)} -> {file_info.get('path')}")
                                retry_count += 1
                            else:
                                current_round_failed.append(file_path)
                                logger.warning(f"    - 重试仍失败: {os.path.basename(file_path)}")
                        except Exception as exc:
                            current_round_failed.append(file_path)
                            logger.error(f"    - 重试异常: {os.path.basename(file_path)}", context=exc)

                    failed_files = current_round_failed
                    if failed_files:
                        # 在重试轮次之间稍作等待
                        import time
                        time.sleep(1)

                retry_duration = (datetime.now() - retry_start_time).total_seconds()
                final_successful = sum(1 for r in upload_results.values() if r)
                logger.info(f"  -> 重试完成 (耗时: {retry_duration:.2f}s)。重试成功 {retry_count} 个，最终成功 {final_successful}/{len(files_to_upload)} 个。")

                if failed_files:
                    logger.warning(f"  -> 仍有 {len(failed_files)} 个文件重试后依然失败:")
                    for file_path in failed_files:
                        logger.warning(f"     - {os.path.basename(file_path)}")
            else:
                logger.info(f"  -> 所有文件均上传成功，无需重试。")

        # === 阶段3: 使用已上传的结果，处理申请数据 ===
        logger.info("\n  -> 开始处理申请数据...")
        application_data["itemmap"]["selecthouse"] = house_id

        # 处理家庭成员信息
        family_members = application_data.get("familys", [])
        original_family_members = list(family_members) # 创建一个副本用于迭代
        valid_family_members = [] # 用于存储通过所有验证的成员

        logger.debug(f"\n  -> 📊 家庭成员处理统计:")
        logger.debug(f"     配置文件中的家庭成员总数: {len(original_family_members)}")

        # 预检查：统计是否真正配置了身份证文件
        total_id_groups = 0
        members_with_id_config = 0  # 配置了身份证文件的成员数
        members_with_actual_files = 0  # 实际有身份证文件的成员数

        for i, member in enumerate(original_family_members):
            member_name = member.get('name', f'成员{i+1}')
            has_front_config = bool(member.get('card0', []))
            has_back_config = bool(member.get('card1', []))

            # 检查是否配置了身份证文件路径
            if has_front_config or has_back_config:
                members_with_id_config += 1

                # 检查文件是否真实存在
                has_actual_files = False
                for card_key in ["card0", "card1"]:
                    if card_key in member and isinstance(member[card_key], list):
                        for filename in member[card_key]:
                            file_path = os.path.join(profile_dir, filename)
                            if os.path.exists(file_path):
                                has_actual_files = True
                                break
                    if has_actual_files:
                        break

                if has_actual_files:
                    members_with_actual_files += 1
                    total_id_groups += 1

            logger.debug(f"     成员{i+1} ({member_name}): 配置身份证={has_front_config or has_back_config}, 文件存在={'是' if has_actual_files else '否'}")

        # 根据配置情况确定处理策略
        family_member_optional = (members_with_id_config == 0)  # 如果没有配置任何身份证文件，则家庭成员是可选的

        logger.debug(f"     配置了身份证的成员数: {members_with_id_config}")
        logger.debug(f"     实际有身份证文件的成员数: {members_with_actual_files}")
        logger.info(f"     家庭成员信息状态: {'可选（未配置身份证文件）' if family_member_optional else '必需（已配置身份证文件）'}")
        logger.debug(f"     预期能处理的成员数: {total_id_groups}")

        if family_member_optional:
            logger.info(f"     ✅ 检测到用户选择不提供家庭成员信息，这是合法的配置")
        else:
            logger.info(f"     🔍 检测到用户配置了家庭成员身份证文件，将进行验证处理")

        logger.info(f"     开始逐个处理验证...\n")

        for member in original_family_members:
            # 验证1: 检查关键占位符（先检查是否有身份证图片，如果有则可以通过AI识别填充）
            has_id_files = False
            for card_key in ["card0", "card1"]:
                if card_key in member and isinstance(member[card_key], list):
                    for filename in member[card_key]:
                        file_path = os.path.join(profile_dir, filename)
                        if file_path in upload_results and upload_results[file_path]:
                            has_id_files = True
                            break
                if has_id_files:
                    break

            # 如果没有身份证文件，则严格检查占位符
            if not has_id_files:
                is_valid = True
                # 精确的占位符列表，只匹配完整的占位符值
                exact_placeholders = {
                    "name": ["姓名", "父亲的姓名", "母亲的姓名", "配偶姓名", "家庭成员姓名"],
                    "cardno": ["身份证", "身份证号", "父亲的身份证号", "母亲的身份证号", "配偶身份证号"],
                    "phone": ["手机号", "电话", "父亲的手机号", "母亲的手机号", "配偶手机号"]
                }
                for field, placeholders in exact_placeholders.items():
                    value = str(member.get(field, "")).strip()
                    if value in placeholders:  # 精确匹配而非包含匹配
                        logger.warning(f"  -> 跳过家庭成员: '{member.get('name', '未知')}' - 字段 '{field}' 值为占位符 '{value}' 且无身份证图片")
                        is_valid = False
                        break # 找到一个占位符就够了

                if not is_valid:
                    continue # 跳过这个无效的成员
            else:
                logger.debug(f"  -> 处理家庭成员: '{member.get('name', '未知')}' - 检测到身份证图片，将尝试AI识别填充")

            # 验证2: 上传并检查身份证图片，同时进行AI识别
            has_id_image = False
            ai_recognized_info = {}  # 存储AI识别的信息

            for card_key in ["card0", "card1"]:
                uploaded_cards = []
                if card_key in member and isinstance(member[card_key], list):
                    for filename in member[card_key]:
                        file_path = os.path.join(profile_dir, filename)
                        file_info = upload_results.get(file_path) # 从并发上传结果中获取

                        if file_info:
                            logger.debug(f"  -> 使用已上传文件 {member.get('name', '')} 的 '{filename}'...")
                            uploaded_cards.append({
                                "path": file_info["path"],
                                "name": file_info["name"]
                            })
                            has_id_image = True # 只要有一张成功上传就标记

                            # 调用AI识别接口
                            card_side = "front" if card_key == "card0" else "back"
                            ai_result = login_client.get_idcard_info(file_info["path"], card_side)
                            if ai_result:
                                # 智能合并AI识别结果，保护正面识别的关键字段
                                if card_side == "front":
                                    # 正面识别结果直接更新，并标记关键字段
                                    ai_recognized_info.update(ai_result)
                                    # 记录正面识别的关键字段，防止被背面覆盖
                                    front_words_result = ai_result.get("words_result", {})
                                    if front_words_result:
                                        ai_recognized_info["_front_priority_fields"] = {
                                            "姓名": front_words_result.get("姓名"),
                                            "公民身份号码": front_words_result.get("公民身份号码"),
                                            "性别": front_words_result.get("性别"),
                                            "民族": front_words_result.get("民族")
                                        }
                                        logger.debug(f"       保护正面识别的关键字段: {list(ai_recognized_info['_front_priority_fields'].keys())}")
                                else:
                                    # 背面识别结果只更新非关键字段，避免覆盖正面结果
                                    back_result = ai_result.copy()
                                    back_words_result = back_result.get("words_result", {})

                                    # 如果正面已识别关键字段，背面不覆盖
                                    if "_front_priority_fields" in ai_recognized_info:
                                        logger.debug(f"       检测到正面关键字段保护，背面识别不覆盖关键信息")
                                        front_priority = ai_recognized_info["_front_priority_fields"]
                                        for field_name in ["姓名", "公民身份号码", "性别", "民族"]:
                                            if field_name in front_priority and front_priority[field_name]:
                                                if field_name in back_words_result:
                                                    logger.debug(f"         跳过背面字段 {field_name}，保持正面识别结果")
                                                    del back_words_result[field_name]

                                    # 更新背面结果
                                    ai_recognized_info.update(back_result)
                                logger.info(f"  -> AI识别成功，获取到 {card_side} 身份证信息:")

                                # 详细打印AI识别结果的完整数据结构
                                logger.debug(f"       完整AI结果数据结构:")
                                logger.debug(f"       {json.dumps(ai_result, ensure_ascii=False, indent=8)}")

                                # 打印words_result的具体内容
                                words_result = ai_result.get("words_result", {})
                                if words_result:
                                    logger.debug(f"       words_result内容:")
                                    for field_name, field_data in words_result.items():
                                        if isinstance(field_data, dict) and "words" in field_data:
                                            logger.debug(f"         {field_name}: {field_data['words']}")
                                        else:
                                            logger.debug(f"         {field_name}: {field_data}")
                                else:
                                    logger.warning(f"       警告: words_result为空或不存在")

                                for key, value in ai_result.items():
                                    logger.debug(f"       {key}: {value}")
                            else:
                                logger.warning(f"  -> AI识别失败或无结果 ({card_side})")
                        else:
                            logger.warning(f"  -> 未找到文件 '{filename}' 的上传结果或上传失败。")
                member[card_key] = uploaded_cards

            # 如果AI识别成功，自动填充成员信息
            if ai_recognized_info:
                logger.info(f"  -> 正在使用AI识别结果自动填充 {member.get('name', '')} 的信息...")

                # 显示最终合并后的AI识别数据状态
                logger.debug(f"  -> 最终AI识别数据状态检查:")
                words_result = ai_recognized_info.get("words_result", {})
                if words_result:
                    logger.debug(f"     当前words_result包含字段: {list(words_result.keys())}")
                    for field_name, field_data in words_result.items():
                        if isinstance(field_data, dict) and "words" in field_data:
                            logger.debug(f"     {field_name}: '{field_data['words']}'")
                        else:
                            logger.debug(f"     {field_name}: {field_data}")
                else:
                    logger.warning(f"      words_result为空或不存在")

                # 显示保护的正面字段信息
                if "_front_priority_fields" in ai_recognized_info:
                    front_fields = ai_recognized_info["_front_priority_fields"]
                    logger.debug(f"     正面优先保护字段状态:")
                    for field_name, field_data in front_fields.items():
                        if field_data and isinstance(field_data, dict) and "words" in field_data:
                            logger.debug(f"       {field_name}: '{field_data['words']}' (来自正面)")
                        elif field_data:
                            logger.debug(f"       {field_name}: {field_data} (来自正面)")
                        else:
                            logger.debug(f"       {field_name}: 空值")

                # 解析AI识别结果，处理 words_result 结构
                def extract_ai_field_value(ai_data, chinese_field_name):
                    """从AI识别结果中提取字段值，优先使用正面识别结果"""
                    logger.debug(f"    - 正在提取字段 '{chinese_field_name}' 从AI数据")

                    # 优先从正面保护字段中提取
                    front_priority_fields = ai_data.get("_front_priority_fields", {})
                    if chinese_field_name in front_priority_fields and front_priority_fields[chinese_field_name]:
                        front_field_data = front_priority_fields[chinese_field_name]
                        logger.debug(f"    - 优先使用正面保护字段 '{chinese_field_name}'")
                        logger.debug(f"    - 正面字段数据: {front_field_data}")

                        if isinstance(front_field_data, dict) and "words" in front_field_data:
                            extracted_value = front_field_data["words"]
                            logger.debug(f"    - 从正面dict.words提取值: '{extracted_value}'")
                            return extracted_value
                        elif isinstance(front_field_data, str):
                            logger.debug(f"    - 直接使用正面字符串值: '{front_field_data}'")
                            return front_field_data
                        else:
                            logger.debug(f"    - 正面数据格式转换: '{str(front_field_data)}'")
                            return str(front_field_data)

                    # fallback到普通words_result
                    words_result = ai_data.get("words_result", {})
                    logger.debug(f"    - words_result是否存在: {bool(words_result)}")

                    if chinese_field_name in words_result:
                        field_data = words_result[chinese_field_name]
                        logger.debug(f"    - 找到字段 '{chinese_field_name}', 数据类型: {type(field_data)}")
                        logger.debug(f"    - 字段数据内容: {field_data}")

                        if isinstance(field_data, dict) and "words" in field_data:
                            extracted_value = field_data["words"]
                            logger.debug(f"    - 从dict.words提取值: '{extracted_value}'")
                            return extracted_value
                        elif isinstance(field_data, str):
                            logger.debug(f"    - 直接使用字符串值: '{field_data}'")
                            return field_data
                        else:
                            logger.debug(f"    - 未知数据格式，尝试转换为字符串: '{str(field_data)}'")
                            return str(field_data)
                    else:
                        logger.debug(f"    - 字段 '{chinese_field_name}' 在words_result中不存在")
                        available_fields = list(words_result.keys())
                        logger.debug(f"    - 可用字段列表: {available_fields}")

                    return ""

                # 中文字段到英文字段的映射（仅包含申请必需字段）
                chinese_to_english_mappings = {
                    "姓名": "name",
                    "公民身份号码": "cardno",
                    "身份证号": "cardno",
                    "身份证号码": "cardno",
                    "性别": "sex",
                    "民族": "nation",
                    # 增加更多可能的字段名变体
                    "证件号码": "cardno",
                    "证件号": "cardno",
                    "身份证": "cardno"
                }

                # 提取AI识别的字段值
                extracted_values = {}
                for chinese_field, english_field in chinese_to_english_mappings.items():
                    value = extract_ai_field_value(ai_recognized_info, chinese_field)
                    if value:
                        extracted_values[english_field] = value
                        logger.debug(f"    - AI提取: {chinese_field} -> {english_field} = '{value}'")

                # AI识别字段映射，仅包含申请必需的字段
                ai_mappings = {
                    "name": extracted_values.get("name", "") or ai_recognized_info.get("name", ""),
                    "cardno": extracted_values.get("cardno", "") or ai_recognized_info.get("cardno", "") or ai_recognized_info.get("idno", ""),
                    "sex": extracted_values.get("sex", "") or ai_recognized_info.get("sex", "") or ai_recognized_info.get("gender", ""),
                    "nation": extracted_values.get("nation", "") or ai_recognized_info.get("nation", "") or ai_recognized_info.get("nationality", ""),
                }

                logger.debug(f"    - AI字段映射结果: {ai_mappings}")

                # 检查是否所有映射都为空
                non_empty_mappings = {k: v for k, v in ai_mappings.items() if v}
                if not non_empty_mappings:
                    logger.warning(f"    - 警告：所有AI字段映射均为空值")
                    logger.debug(f"    - 原始AI识别数据键: {list(ai_recognized_info.keys())}")
                    if "words_result" in ai_recognized_info:
                        logger.debug(f"    - words_result键: {list(ai_recognized_info['words_result'].keys())}")
                else:
                    logger.debug(f"    - ✅ 成功映射的字段: {non_empty_mappings}")

                # 定义精确的占位符列表，用于判断是否需要填充
                exact_placeholders = {
                    "name": ["姓名", "父亲的姓名", "母亲的姓名", "配偶姓名", "家庭成员姓名"],
                    "cardno": ["身份证", "身份证号", "证件号", "父亲的身份证号", "母亲的身份证号", "配偶身份证号"],
                    "sex": ["性别"],
                    "nation": ["民族"]
                }

                filled_fields = []
                for field, ai_value in ai_mappings.items():
                    if ai_value:
                        current_value = str(member.get(field, "")).strip()
                        placeholders = exact_placeholders.get(field, [])

                        # 判断是否需要填充：值为空或精确匹配占位符
                        needs_fill = (not current_value or
                                    current_value == "" or
                                    current_value in placeholders)

                        logger.debug(f"    - 检查字段 {field}:")
                        logger.debug(f"      当前值: '{current_value}'")
                        logger.debug(f"      AI识别值: '{ai_value}'")
                        logger.debug(f"      占位符列表: {placeholders}")
                        logger.debug(f"      当前值是否在占位符中: {current_value in placeholders}")
                        logger.debug(f"      需要填充: {needs_fill}")

                        if needs_fill:
                            logger.debug(f"      执行填充: '{current_value}' -> '{ai_value}'")
                            member[field] = ai_value
                            filled_fields.append(f"{field}={ai_value}")

                            # 特殊处理：如果姓名被识别出来，更新member的显示名
                            if field == "name" and ai_value:
                                logger.debug(f"    - 更新成员姓名: '{current_value}' -> '{ai_value}'")
                        else:
                            logger.debug(f"      跳过填充: 当前值不是占位符或已有有效值")

                if filled_fields:
                    logger.info(f"    - AI自动填充成功: {', '.join(filled_fields)}")
                else:
                    logger.info(f"    - 成员信息已完整，无需AI填充")

                # 详细显示填充后的成员数据
                logger.debug(f"    - 填充完成后的成员数据验证:")
                logger.debug(f"      姓名: '{member.get('name', '')}'")
                logger.debug(f"      身份证号: '{member.get('cardno', '')}'")
                logger.debug(f"      性别: '{member.get('sex', '')}'")
                logger.debug(f"      民族: '{member.get('nation', '')}'")

                # 验证关键字段是否已正确填充
                name_value = str(member.get('name', '')).strip()
                cardno_value = str(member.get('cardno', '')).strip()

                name_placeholders = ["姓名", "父亲的姓名", "母亲的姓名", "配偶姓名", "家庭成员姓名", "请填写姓名"]
                cardno_placeholders = ["身份证", "身份证号", "父亲的身份证号", "母亲的身份证号", "配偶身份证号", "身份证号码", "请填写身份证号"]

                name_is_placeholder = not name_value or name_value in name_placeholders
                cardno_is_placeholder = not cardno_value or cardno_value in cardno_placeholders

                logger.debug(f"    - 最终验证结果:")
                logger.debug(f"      姓名是否仍为占位符: {name_is_placeholder}")
                logger.debug(f"      身份证号是否仍为占位符: {cardno_is_placeholder}")

                if name_is_placeholder or cardno_is_placeholder:
                    logger.warning(f"    - 警告：AI填充后仍有关键字段为占位符值")
                else:
                    logger.debug(f"    - ✅ AI填充验证通过：关键信息已正确填充")

                # 显示AI填充完成后的完整成员对象状态
                logger.debug(f"    - AI填充完成后的完整成员对象:")
                for key, value in member.items():
                    if key not in ["card0", "card1"]:  # 跳过文件数组，避免输出过长
                        logger.debug(f"      {key}: '{value}'")

            # 验证3: 确保至少有一张身份证图片被上传
            if not has_id_image:
                logger.warning(f"  -> 排除家庭成员: {member.get('name', '')} (无有效身份证图片文件)")
                continue

            # 最终验证：检查关键信息是否完整
            logger.debug(f"  -> 开始最终验证成员 '{member.get('name', '未知')}'...")
            required_member_fields = ["name", "cardno"]
            missing_fields = []
            for field in required_member_fields:
                value = str(member.get(field, "")).strip()
                # 精确的占位符检查
                exact_placeholders = {
                    "name": ["姓名", "父亲的姓名", "母亲的姓名", "配偶姓名", "家庭成员姓名", "请填写姓名"],
                    "cardno": ["身份证", "身份证号", "父亲的身份证号", "母亲的身份证号", "配偶身份证号", "身份证号码", "请填写身份证号"]
                }
                placeholders = exact_placeholders.get(field, [])

                logger.debug(f"    - 最终验证字段 {field}:")
                logger.debug(f"      当前值: '{value}'")
                logger.debug(f"      值长度: {len(value)}")
                logger.debug(f"      占位符列表: {placeholders}")
                logger.debug(f"      值为空: {not value}")
                logger.debug(f"      值等于空字符串: {value == ''}")
                logger.debug(f"      值在占位符中: {value in placeholders}")

                # 检查值是否为空或完全匹配占位符
                # 优化判断逻辑：只有当值确实为空或完全匹配占位符时才认为无效
                is_empty = not value or value == ""
                is_placeholder = value in placeholders
                is_invalid = is_empty or is_placeholder

                logger.debug(f"      是否为空: {is_empty}")
                logger.debug(f"      是否为占位符: {is_placeholder}")
                logger.debug(f"      最终判定无效: {is_invalid}")

                # 特殊处理：如果值看起来像真实数据（长度合理且不在占位符列表中），则认为有效
                if not is_empty and not is_placeholder:
                    if field == "name" and len(value) >= 2 and len(value) <= 10:
                        logger.debug(f"      检测到疑似真实姓名，长度合理: {len(value)} 字符")
                        is_invalid = False
                    elif field == "cardno" and len(value) == 18:
                        # 身份证号验证：前17位是数字，最后一位是数字或X
                        if value[:17].isdigit() and (value[17].upper() == 'X'):
                            logger.debug(f"      检测到疑似真实身份证号，格式正确: 18位标准格式")
                            is_invalid = False
                        else:
                            logger.debug(f"      身份证号格式不标准，但非占位符，暂时认为有效")
                            # 如果不是标准格式但也不是占位符，可能是特殊情况，暂时认为有效
                            if not is_placeholder:
                                is_invalid = False

                if is_invalid:
                    missing_fields.append(field)
                    logger.warning(f"    - ❌ 字段 {field} 验证失败: 值为 '{value}' (空值或占位符)")
                else:
                    logger.debug(f"    - ✅ 字段 {field} 验证通过: 值为 '{value}'")

            if missing_fields:
                logger.error(f"  -> ❌ 排除家庭成员: {member.get('name', '未知')} (关键信息缺失: {', '.join(missing_fields)})")
                logger.debug(f"     成员完整数据: {member}")
                continue
            else:
                logger.info(f"  -> ✅ 成员 {member.get('name', '未知')} 通过最终验证")

            # 如果所有验证都通过，则将该成员添加到有效列表中
            member_name = member.get('name', '未知')
            member_relation = member.get('relationship', '未知关系')
            logger.info(f"  -> ✓ 保留家庭成员: {member_name} ({member_relation}) - 信息完整")
            valid_family_members.append(member)

        # 更新 familys 数组为过滤后的有效成员
        application_data["familys"] = valid_family_members

        # 显示最终的成员处理结果统计
        logger.info(f"\n  -> 📊 家庭成员处理结果统计:")
        logger.info(f"     原始成员数: {len(original_family_members)}")
        logger.debug(f"     预期处理数: {total_id_groups}")
        logger.info(f"     最终保留数: {len(valid_family_members)}")

        if len(valid_family_members) < total_id_groups:
            logger.warning(f"      警告: 有 {total_id_groups - len(valid_family_members)} 个成员在验证过程中被过滤")

        # 根据家庭成员是否可选来调整最终验证逻辑
        if len(valid_family_members) == 0:
            if family_member_optional:
                logger.info(f"     ✅ 家庭成员信息为空：用户选择不提供家庭成员信息（合法配置）")
            else:
                logger.error(f"     ❌ 严重问题: 用户配置了家庭成员身份证文件，但所有成员都验证失败")
                logger.error(f"        建议检查身份证文件是否正确，或考虑从配置文件中移除家庭成员配置")
        else:
            logger.info(f"     ✅ 最终将提交 {len(valid_family_members)} 个家庭成员的信息")
            for i, member in enumerate(valid_family_members):
                member_name = member.get('name', '未知')
                member_cardno = member.get('cardno', '未知')
                logger.info(f"       成员{i+1}: {member_name} (身份证: {member_cardno})")
        logger.info("")

        # 如果用户配置了身份证文件但最终没有有效成员，提供额外的帮助信息
        if not family_member_optional and len(valid_family_members) == 0:
            logger.warning(f"  -> 💡 建议解决方案:")
            logger.warning(f"     1. 检查身份证图片文件是否存在且格式正确")
            logger.warning(f"     2. 确保AI识别服务正常工作")
            logger.warning(f"     3. 或者从config.json中完全移除familys配置以不提供家庭成员信息")
            logger.warning("")

        # 处理其他附件
        attachment_keys = ["work", "nohouse", "educationattachment", "approvefile", "household", "rewardattmts", "achievementattmts", "personnel"]

        # 定义必需的附件类型
        required_attachments = ["work", "educationattachment"]  # 工作证明和学历证明是必需的
        missing_required_attachments = []

        for key in attachment_keys:
            if key in application_data["itemmap"] and isinstance(application_data["itemmap"][key], list):
                uploaded_attachments = []
                failed_uploads = []

                # 列表里存的是文件名
                for filename in application_data["itemmap"][key]:
                    file_path = os.path.join(profile_dir, filename)
                    file_info = upload_results.get(file_path) # 从并发上传结果中获取
                    if file_info:
                        logger.debug(f"  -> 使用已上传附件 '{filename}'...")
                        uploaded_attachments.append({
                            "path": file_info["path"],
                            "name": file_info["name"]
                        })
                    else:
                        failed_uploads.append(filename)
                        logger.warning(f"  -> 未找到附件 '{filename}' 的上传结果或上传失败。")

                # 用上传后的信息替换原始文件名列表
                application_data["itemmap"][key] = uploaded_attachments

                # 检查必需附件的上传情况
                if key in required_attachments:
                    if not uploaded_attachments:  # 必需附件完全没有上传成功
                        missing_required_attachments.append({
                            "type": key,
                            "failed_files": failed_uploads,
                            "configured_files": application_data["itemmap"].get(key, [])
                        })
                        logger.error(f"  -> ❌ 关键附件 '{key}' 上传失败！配置的文件: {failed_uploads}")
                    elif failed_uploads:  # 部分文件上传失败
                        logger.warning(f"  ->  附件 '{key}' 部分文件上传失败: {failed_uploads}")
                    else:
                        logger.info(f"  -> ✅ 附件 '{key}' 全部上传成功，共 {len(uploaded_attachments)} 个文件")

        # 如果有必需附件上传失败，终止申请流程
        if missing_required_attachments:
            logger.error(f"\n  -> ❌ 申请流程终止：以下必需附件上传失败:")
            for missing in missing_required_attachments:
                attachment_type = missing["type"]
                failed_files = missing["failed_files"]
                logger.error(f"     - {attachment_type}: {failed_files}")

            logger.error(f"\n  -> 💡 建议解决方案:")
            logger.error(f"     1. 检查文件是否存在且格式正确")
            logger.error(f"     2. 确认网络连接稳定")
            logger.error(f"     3. 检查登录状态是否有效")
            logger.error(f"     4. 重新运行抢房程序")

            # 显示详细的上传结果统计
            logger.error(f"\n  -> 📊 详细上传结果统计:")
            total_files = len(files_to_upload)
            successful_files = sum(1 for r in upload_results.values() if r)
            failed_files = total_files - successful_files
            logger.error(f"     总文件数: {total_files}")
            logger.error(f"     上传成功: {successful_files}")
            logger.error(f"     上传失败: {failed_files}")

            if failed_files > 0:
                logger.error(f"     失败文件列表:")
                for file_path, result in upload_results.items():
                    if not result:
                        logger.error(f"       - {os.path.basename(file_path)} ({file_path})")

            return False  # 提前终止函数

        logger.info("\n  -> 申请数据准备完成，准备提交...")
        # 打印部分申请数据用于确认
        logger.debug(f"     - 房源ID: {application_data['itemmap']['selecthouse']}")
        logger.debug(f"     - 所属群体: {application_data['itemmap']['group']}")
        logger.debug(f"     - 家庭成员数: {len(application_data.get('familys', []))}")

        # 快速验证JSON序列化（优化性能，禁用调试文件保存）
        try:
            # 仅验证序列化可行性，不保存文件
            json.dumps(application_data, ensure_ascii=False)
            logger.info("\n  -> 申请数据验证通过")
        except TypeError as json_error:
            logger.error(f"\n  -> 申请数据序列化失败", context=json_error)
            logger.error("  -> 申请数据结构包含无效数据，无法提交")
            return False

    except Exception as e:
        logger.error(f"  -> 构建申请数据时出错", context=e)
        import traceback
        traceback.print_exc()
        return False

    # 4. 提交申请
    logger.info("\n  -> 提交最终申请...")
    submit_result = login_client.submit_application(application_data, house_id)

    # 记录结束时间（精确到毫秒）
    end_time = datetime.now()
    duration = end_time - start_time

    # 处理新的返回格式
    if isinstance(submit_result, dict):
        submit_success = submit_result.get('success', False)
        submit_message = submit_result.get('message', '无详细信息')
    else:
        # 向后兼容：如果返回的是布尔值
        submit_success = bool(submit_result)
        submit_message = '申请提交成功' if submit_success else '申请提交失败'

    if submit_success:
        logger.info(f"\n{'='*20} 为 '{os.path.basename(profile_dir)}' 申请房源成功! {'='*20}")
        logger.info(f"成功信息: {submit_message}")
    else:
        logger.error(f"\n{'='*20} 为 '{os.path.basename(profile_dir)}' 申请房源失败。 {'='*20}")
        logger.error(f"失败原因: {submit_message}")

    logger.info(f"结束时间: {end_time.strftime('%Y-%m-%d %H:%M:%S.%f')[:-3]}")
    logger.info(f"总耗时: {duration.total_seconds():.3f} 秒")

    # 返回详细结果给调用方，以便监控服务正确处理抢房结果
    return {
        'success': submit_success,
        'message': submit_message,
        'details': f'用户: {os.path.basename(profile_dir)}, 总耗时: {duration.total_seconds():.3f}秒'
    }

def find_flexible_file(profile_dir, configured_filename, logger=None):
    """
    灵活查找文件：如果配置的文件不存在，尝试查找同名但不同扩展名的文件

    Args:
        profile_dir: 用户配置目录
        configured_filename: 配置文件中指定的文件名
        logger: 日志记录器

    Returns:
        找到的实际文件名，如果没找到则返回原文件名
    """
    if not logger:
        logger = Logger("FileHelper")

    # 原始文件路径
    original_path = os.path.join(profile_dir, configured_filename)

    # 如果原始文件存在，直接返回
    if os.path.exists(original_path):
        logger.debug(f"找到原始文件: {configured_filename}")
        return configured_filename

    # 如果原始文件不存在，尝试查找同名但不同扩展名的文件
    base_name = os.path.splitext(configured_filename)[0]
    original_ext = os.path.splitext(configured_filename)[1].lower()

    # 常见的文件扩展名列表，按优先级排序
    if original_ext in ['.jpg', '.jpeg', '.png', '.gif', '.bmp']:
        # 图片文件的候选扩展名
        candidate_extensions = ['.jpg', '.jpeg', '.png', '.gif', '.bmp', '.webp']
    elif original_ext in ['.pdf', '.doc', '.docx', '.txt']:
        # 文档文件的候选扩展名
        candidate_extensions = ['.pdf', '.doc', '.docx', '.txt', '.jpg', '.jpeg', '.png']
    else:
        # 通用候选扩展名
        candidate_extensions = ['.jpg', '.jpeg', '.png', '.pdf', '.doc', '.docx', '.txt', '.gif', '.bmp']

    # 移除原始扩展名，避免重复检查
    if original_ext in candidate_extensions:
        candidate_extensions.remove(original_ext)

    logger.debug(f"原始文件 {configured_filename} 不存在，开始查找同名替代文件...")
    logger.debug(f"基础文件名: {base_name}")
    logger.debug(f"候选扩展名: {candidate_extensions}")

    # 按优先级查找候选文件
    for ext in candidate_extensions:
        candidate_filename = base_name + ext
        candidate_path = os.path.join(profile_dir, candidate_filename)

        if os.path.exists(candidate_path):
            logger.info(f"✅ 找到替代文件: {configured_filename} -> {candidate_filename}")
            logger.info(f"   原始配置: {configured_filename}")
            logger.info(f"   实际文件: {candidate_filename}")
            return candidate_filename

    # 如果都没找到，记录详细信息并返回原文件名
    logger.warning(f"❌ 未找到任何匹配文件:")
    logger.warning(f"   配置文件名: {configured_filename}")
    logger.warning(f"   基础名称: {base_name}")
    logger.warning(f"   查找目录: {profile_dir}")

    # 列出目录中实际存在的相关文件，帮助用户调试
    try:
        existing_files = [f for f in os.listdir(profile_dir) if os.path.isfile(os.path.join(profile_dir, f))]
        related_files = [f for f in existing_files if base_name.lower() in f.lower()]

        if related_files:
            logger.warning(f"   目录中发现相关文件: {related_files}")
        else:
            logger.warning(f"   目录中未发现任何相关文件")
            logger.debug(f"   目录中所有文件: {existing_files}")
    except Exception as e:
        logger.debug(f"   列出目录文件时出错: {e}")

    return configured_filename  # 返回原文件名，让后续逻辑处理文件不存在的情况