# 生产环境配置文件
# 用于生产环境的优化配置

# 日志配置 - 生产环境使用较少的日志输出
logging:
  level: "INFO"
  use_colors: false
  enable_file_logging: true
  file_log_level: "INFO"
  enable_masking: true  # 生产环境需要数据脱敏
  file_rotation:
    when: "midnight"
    interval: 1
    backup_count: 30

# 服务配置 - 生产环境标准端口
monitor_service:
  host: "127.0.0.1"
  port: 8089
  startup_delay: 3
  max_startup_wait: 30

web_service:
  host: "0.0.0.0"
  port: 50011
  startup_delay: 5
  max_startup_wait: 45

# 生产环境特定功能
production:
  debug_mode: false
  performance_monitoring: true
  security_enhanced: true

# API错误监控 - 生产环境默认值
api_error_monitoring:
  consecutive_error_threshold: 100
  error_duration_threshold: 120
  notification_interval: 300
  recovery_notification: true

# 服务管理器配置 - 生产环境优化
service_management:
  health_check_timeout: 100
  check_interval: 10