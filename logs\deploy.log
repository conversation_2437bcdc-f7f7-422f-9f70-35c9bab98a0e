2025-07-30 11:02:11,177 - __main__ - INFO - 🚀 开始部署预上传缓存系统
2025-07-30 11:02:11,178 - __main__ - INFO - ============================================================
2025-07-30 11:02:11,178 - __main__ - INFO -
==================================================
2025-07-30 11:02:11,178 - __main__ - INFO - 执行步骤: 检查系统要求
2025-07-30 11:02:11,178 - __main__ - INFO - ==================================================
2025-07-30 11:02:11,178 - __main__ - INFO - === 检查系统要求 ===
2025-07-30 11:02:11,178 - __main__ - INFO - ✅ Python版本: 3.10.10
2025-07-30 11:02:11,179 - __main__ - INFO - ✅ 目录存在: cache
2025-07-30 11:02:11,179 - __main__ - INFO - ✅ 目录存在: services
2025-07-30 11:02:11,179 - __main__ - INFO - ✅ 目录存在: ZDQF
2025-07-30 11:02:11,179 - __main__ - INFO - ✅ 目录存在: ZDQF/profiles
2025-07-30 11:02:11,179 - __main__ - INFO - ✅ 目录存在: core/grab
2025-07-30 11:02:11,179 - __main__ - INFO - ✅ 目录存在: monitor_service/core
2025-07-30 11:02:11,179 - __main__ - INFO - ✅ 文件存在: cache/upload_cache.py
2025-07-30 11:02:11,179 - __main__ - INFO - ✅ 文件存在: services/preupload_service.py
2025-07-30 11:02:11,179 - __main__ - INFO - ✅ 文件存在: ZDQF/login.py
2025-07-30 11:02:11,179 - __main__ - INFO - ✅ 文件存在: core/grab/grab_executor.py
2025-07-30 11:02:11,180 - __main__ - INFO - ✅ 文件存在: startup_preupload.py
2025-07-30 11:02:11,180 - __main__ - INFO - ✅ 系统要求检查通过
2025-07-30 11:02:11,180 - __main__ - INFO - ✅ 步骤完成: 检查系统要求
2025-07-30 11:02:11,180 - __main__ - INFO -
==================================================
2025-07-30 11:02:11,180 - __main__ - INFO - 执行步骤: 创建缓存目录
2025-07-30 11:02:11,180 - __main__ - INFO - ==================================================
2025-07-30 11:02:11,180 - __main__ - INFO - === 创建缓存目录 ===
2025-07-30 11:02:11,180 - __main__ - INFO - ✅ 缓存目录创建: cache/uploads
2025-07-30 11:02:11,180 - __main__ - INFO - ✅ 缓存目录创建: logs
2025-07-30 11:02:11,181 - __main__ - INFO - ✅ 缓存目录创建: backup/cache
2025-07-30 11:02:11,181 - __main__ - ERROR - ❌ 步骤失败: 创建缓存目录
2025-07-30 11:02:11,181 - __main__ - INFO -
==================================================
2025-07-30 11:02:11,181 - __main__ - INFO - 执行步骤: 备份现有系统
2025-07-30 11:02:11,181 - __main__ - INFO - ==================================================
2025-07-30 11:02:11,181 - __main__ - INFO - === 备份现有系统 ===
2025-07-30 11:02:11,182 - __main__ - INFO - ✅ 备份文件: ZDQF/login.py -> backup\preupload_backup_20250730_110211\ZDQF\login.py
2025-07-30 11:02:11,183 - __main__ - INFO - ✅ 备份文件: core/grab/grab_executor.py -> backup\preupload_backup_20250730_110211\core\grab\grab_executor.py
2025-07-30 11:02:11,184 - __main__ - INFO - ✅ 备份文件: monitor_service/core/monitor.py -> backup\preupload_backup_20250730_110211\monitor_service\core\monitor.py
2025-07-30 11:02:11,187 - __main__ - INFO - ✅ 备份文件: core/monitor/house_monitor.py -> backup\preupload_backup_20250730_110211\core\monitor\house_monitor.py
2025-07-30 11:02:11,187 - __main__ - INFO - ✅ 系统备份完成: backup\preupload_backup_20250730_110211
2025-07-30 11:02:11,187 - __main__ - INFO - ✅ 步骤完成: 备份现有系统
2025-07-30 11:02:11,187 - __main__ - INFO -
==================================================
2025-07-30 11:02:11,187 - __main__ - INFO - 执行步骤: 验证用户配置
2025-07-30 11:02:11,187 - __main__ - INFO - ==================================================
2025-07-30 11:02:11,187 - __main__ - INFO - === 验证用户配置 ===
2025-07-30 11:02:11,191 - __main__ - INFO - 📊 用户配置统计: 23/23 有效
2025-07-30 11:02:11,191 - __main__ - INFO - ✅ 用户配置验证通过
2025-07-30 11:02:11,191 - __main__ - INFO - ✅ 步骤完成: 验证用户配置
2025-07-30 11:02:11,191 - __main__ - INFO -
==================================================
2025-07-30 11:02:11,192 - __main__ - INFO - 执行步骤: 创建启动脚本
2025-07-30 11:02:11,192 - __main__ - INFO - ==================================================
2025-07-30 11:02:11,192 - __main__ - INFO - === 创建启动脚本 ===
2025-07-30 11:02:11,192 - __main__ - INFO - ✅ Windows启动脚本创建: start_preupload.bat
2025-07-30 11:02:11,193 - __main__ - INFO - ✅ Linux/Mac启动脚本创建: start_preupload.sh
2025-07-30 11:02:11,193 - __main__ - ERROR - ❌ 步骤失败: 创建启动脚本
2025-07-30 11:02:11,193 - __main__ - INFO -
==================================================
2025-07-30 11:02:11,193 - __main__ - INFO - 执行步骤: 创建定时任务模板
2025-07-30 11:02:11,193 - __main__ - INFO - ==================================================
2025-07-30 11:02:11,193 - __main__ - INFO - === 创建定时任务模板 ===
2025-07-30 11:02:11,194 - __main__ - INFO - ✅ Windows定时任务模板: templates/windows_scheduled_task.xml
2025-07-30 11:02:11,194 - __main__ - INFO - ✅ Linux定时任务模板: templates/crontab_template.txt
2025-07-30 11:02:11,194 - __main__ - ERROR - ❌ 步骤失败: 创建定时任务模板
2025-07-30 11:02:11,195 - __main__ - INFO -
==================================================
2025-07-30 11:02:11,195 - __main__ - INFO - 执行步骤: 创建监控脚本
2025-07-30 11:02:11,195 - __main__ - INFO - ==================================================
2025-07-30 11:02:11,195 - __main__ - INFO - === 创建监控脚本 ===
2025-07-30 11:02:11,195 - __main__ - INFO - ✅ 监控脚本创建: monitor_preupload.py
2025-07-30 11:02:11,195 - __main__ - ERROR - ❌ 步骤失败: 创建监控脚本
2025-07-30 11:02:11,195 - __main__ - INFO -
==================================================
2025-07-30 11:02:11,196 - __main__ - INFO - 执行步骤: 创建部署总结
2025-07-30 11:02:11,196 - __main__ - INFO - ==================================================
2025-07-30 11:02:11,196 - __main__ - INFO - === 创建部署总结 ===
2025-07-30 11:02:11,196 - __main__ - INFO - ✅ 部署总结创建: DEPLOYMENT_SUMMARY.md
2025-07-30 11:02:11,196 - __main__ - ERROR - ❌ 步骤失败: 创建部署总结
2025-07-30 11:02:11,196 - __main__ - INFO -
============================================================
2025-07-30 11:02:11,197 - __main__ - INFO - 部署完成: 3/8 步骤成功
2025-07-30 11:02:11,197 - __main__ - INFO - 总耗时: 0.02秒
2025-07-30 11:02:11,197 - __main__ - INFO - ============================================================
2025-07-30 11:02:11,197 - __main__ - WARNING -  部署不完整，有 5 个步骤失败
2025-07-30 11:02:11,197 - __main__ - WARNING - 请检查日志并手动完成失败的步骤
