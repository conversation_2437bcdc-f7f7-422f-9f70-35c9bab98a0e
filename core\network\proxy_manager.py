"""代理管理器 - 从原代码中提取和改进的代理IP管理功能"""

import asyncio
import random
import time
from typing import Optional, Tuple, List
import requests
import aiohttp
import socket

from ..utils.unified_logging import Logger
from ..utils.helpers import is_valid_ip_port, retry_on_exception


class ProxyManager:
    """代理IP管理器"""

    def __init__(self, api_url: str, logger: Optional[Logger] = None):
        self.api_url = api_url
        self.logger = logger or Logger("ProxyManager")
        self.current_proxy = None
        self.last_fetch_time = 0
        self.remaining_count = 0
        self.proxy_lock = asyncio.Lock()
        self._refresh_lock = asyncio.Lock()
        self.proxy_cooldown = 10  # 代理冷却时间设置为10秒，确保10秒内只获取一个代理IP
        self.proxy_updating = False

        # 简化的代理验证配置
        self.validation_enabled = True  # 保持启用，但仅做格式检查
        self.validation_timeout = 5  # 降低超时时间

        # 代理格式配置
        self.proxy_formats = [
            lambda ip_port: {"http": f"http://{ip_port}", "https": f"http://{ip_port}"},  # 标准格式
            lambda ip_port: {"http": ip_port, "https": ip_port}  # 简化格式
        ]

        # 自动更新相关配置
        self.auto_update_interval = 300  # 自动更新间隔（秒）
        self.low_count_threshold = 10  # 代理数量不足阈值
        self.proxy_expire_time = 240  # 代理有效期（秒）

        # 简化的异常刷新机制
        self.last_exception_refresh_time = 0  # 最后一次异常刷新时间
        self.exception_refresh_cooldown = 5  # 异常刷新冷却时间设置为5秒
        self.proxy_related_exceptions = {
            # 代理连接相关异常
            'ClientProxyConnectionError',
            'ClientHttpProxyError',  # 专门处理407错误
            'ProxyConnectionError',
            'ConnectTimeout',
            'ReadTimeout',
            'ConnectError',
            # 超时相关异常
            'TimeoutError',
            'asyncio.TimeoutError',
            'aiohttp.ServerTimeoutError',
            'requests.exceptions.Timeout',
            # 连接拒绝相关异常
            'ConnectionRefusedError',
            'ConnectionError',
            'ClientConnectorError',
            # Session相关异常
            'RuntimeError',
            'ClientOSError',
            'ClientSSLError',
            'ServerConnectionError',
            # 新增：Windows网络错误异常
            'NetworkException',  # 我们自定义的网络异常
            'WinError',  # Windows错误基类
        }

        # HTTP客户端引用（用于连接池刷新）
        self.http_client = None

        # 验证性能统计
        self.validation_stats = {
            'total_validations': 0,
            'successful_validations': 0,
            'failed_validations': 0,
            'average_validation_time': 0.0,
            'last_validation_time': 0
        }

        # 验证结果缓存机制
        self.validation_cache = {}  # 格式: {ip_port: {'result': bool, 'timestamp': float, 'ttl': 300}}
        self.validation_cache_ttl = 300  # 缓存有效期5分钟
        self.validation_cache_max_size = 50  # 最大缓存数量

        # 请求去重和智能切换机制 - 增强为5秒内严格限制
        self.update_request_times = []  # 记录最近的更新请求时间
        self.update_request_window = 5.0  # 去重时间窗口设置为5秒
        self.max_requests_in_window = 1  # 5秒时间窗口内最大允许的请求数为1次
        self.last_update_attempt_time = 0  # 最后一次尝试更新的时间
        self.update_request_count = 0  # 总更新请求计数
        self.ignored_request_count = 0  # 被忽略的请求计数
        self.adaptive_cooldown_enabled = True  # 是否启用自适应冷却时间

        self.logger.info("代理管理器初始化完成（严格5秒内单次获取限制）")

    def set_http_client(self, http_client):
        """设置HTTP客户端引用"""
        self.http_client = http_client
        self.logger.debug("HTTP客户端引用已设置")

    @retry_on_exception(max_retries=3, exceptions=(requests.RequestException,))
    def _fetch_proxy_from_api(self) -> Tuple[Optional[str], int]:
        """从API获取新的代理IP"""
        try:
            self.logger.debug(f"正在从API获取代理: {self.api_url}")
            response = requests.get(self.api_url, timeout=10, verify=False)
            response.raise_for_status()
            proxy_data = response.json()

            if proxy_data.get("status") == "success":
                data_list = proxy_data.get("data", [])
                if data_list:
                    ip_port = data_list[0].get("IP")
                    remaining_proxies = proxy_data.get("number", 0)

                    if ip_port and is_valid_ip_port(ip_port):
                        self.logger.debug(f"从API获取到新代理: {ip_port}，剩余数量: {remaining_proxies}")
                        return ip_port, remaining_proxies
                    else:
                        self.logger.warning(f"API返回的代理格式无效: {ip_port}")
                        return None, remaining_proxies

            self.logger.warning(f"API返回状态不是success: {proxy_data.get('status')}")
            return None, 0

        except requests.RequestException as e:
            self.logger.error(f"获取代理IP时网络错误: {str(e)}")
            raise
        except Exception as e:
            self.logger.error(f"获取代理IP时出现未预期错误: {str(e)}")
            return None, 0

    def _validate_proxy(self, ip_port: str) -> bool:
        """简化的代理验证 - 仅进行基础格式检查"""
        if not self.validation_enabled:
            self.logger.debug("代理验证已禁用，跳过验证")
            return True

        # 仅进行基础的IP:PORT格式验证
        if not is_valid_ip_port(ip_port):
            self.logger.warning(f"代理格式无效: {ip_port}")
            return False

        self.logger.info(f"代理格式验证通过: {ip_port}")
        return True

    async def _validate_proxy_async(self, ip_port: str) -> bool:
        """简化的异步代理验证 - 仅进行基础检查，移除复杂的HTTP验证"""
        if not self.validation_enabled:
            self.logger.debug("代理验证已禁用，跳过验证")
            return True

        # 仅进行基础的IP:PORT格式验证
        if not is_valid_ip_port(ip_port):
            self.logger.warning(f"代理格式无效: {ip_port}")
            return False

        self.logger.info(f"代理格式验证通过: {ip_port}，将在实际使用中验证可用性")
        return True

    def _update_validation_stats(self, success: bool, validation_time: float):
        """更新验证统计信息"""
        if success:
            self.validation_stats['successful_validations'] += 1
        else:
            self.validation_stats['failed_validations'] += 1

        # 更新平均验证时间
        total_validations = self.validation_stats['total_validations']
        current_avg = self.validation_stats['average_validation_time']
        new_avg = (current_avg * (total_validations - 1) + validation_time) / total_validations
        self.validation_stats['average_validation_time'] = new_avg
        self.validation_stats['last_validation_time'] = validation_time

    def _get_validation_from_cache(self, ip_port: str) -> Optional[bool]:
        """从缓存获取验证结果"""
        if ip_port not in self.validation_cache:
            return None

        cache_entry = self.validation_cache[ip_port]
        current_time = time.time()

        # 检查缓存是否过期
        if current_time - cache_entry['timestamp'] > self.validation_cache_ttl:
            del self.validation_cache[ip_port]
            self.logger.debug(f"代理 {ip_port} 验证缓存已过期，已清除")
            return None

        self.logger.debug(f"代理 {ip_port} 验证结果来自缓存: {cache_entry['result']}")
        return cache_entry['result']

    def _save_validation_to_cache(self, ip_port: str, result: bool):
        """保存验证结果到缓存"""
        # 如果缓存已满，删除最旧的条目
        if len(self.validation_cache) >= self.validation_cache_max_size:
            oldest_key = min(self.validation_cache.keys(),
                           key=lambda k: self.validation_cache[k]['timestamp'])
            del self.validation_cache[oldest_key]
            self.logger.debug(f"验证缓存已满，删除最旧缓存: {oldest_key}")

        self.validation_cache[ip_port] = {
            'result': result,
            'timestamp': time.time()
        }
        self.logger.debug(f"代理 {ip_port} 验证结果已缓存: {result}")

    def _clean_validation_cache(self):
        """清理过期的验证缓存"""
        current_time = time.time()
        expired_keys = [
            ip_port for ip_port, cache_entry in self.validation_cache.items()
            if current_time - cache_entry['timestamp'] > self.validation_cache_ttl
        ]

        for key in expired_keys:
            del self.validation_cache[key]

        if expired_keys:
            self.logger.debug(f"清理了 {len(expired_keys)} 个过期的验证缓存条目")

    def get_validation_stats(self) -> dict:
        """获取代理验证统计信息"""
        stats = self.validation_stats.copy()
        if stats['total_validations'] > 0:
            stats['success_rate'] = stats['successful_validations'] / stats['total_validations']
            stats['failure_rate'] = stats['failed_validations'] / stats['total_validations']
        else:
            stats['success_rate'] = 0.0
            stats['failure_rate'] = 0.0

        # 添加缓存统计信息
        stats['cache_size'] = len(self.validation_cache)
        stats['cache_max_size'] = self.validation_cache_max_size
        stats['cache_hit_ratio'] = f"{len(self.validation_cache)}/{self.validation_cache_max_size}"

        return stats

    def _should_skip_proxy_update(self) -> bool:
        """判断是否应该跳过此次代理更新请求"""
        current_time = time.time()

        # 如果当前没有代理，允许首次获取（绕过防重机制）
        if not self.current_proxy:
            self.logger.debug("当前无可用代理，允许首次获取")
            return False

        # 清理过期的请求时间记录
        self.update_request_times = [
            t for t in self.update_request_times
            if current_time - t <= self.update_request_window
        ]

        # 检查时间窗口内的请求数量
        if len(self.update_request_times) >= self.max_requests_in_window:
            return True

        # 检查距离上次请求是否太近 - 严格5秒间隔
        if (self.last_update_attempt_time > 0 and
            current_time - self.last_update_attempt_time < 5.0):  # 最小间隔5秒
            return True

        return False

    def _record_proxy_update_request(self):
        """记录代理更新请求"""
        current_time = time.time()
        self.update_request_times.append(current_time)
        self.last_update_attempt_time = current_time
        self.update_request_count += 1

        # 保持请求时间列表的大小在合理范围内
        if len(self.update_request_times) > 10:
            self.update_request_times = self.update_request_times[-5:]

    def _get_adaptive_cooldown(self) -> float:
        """获取自适应冷却时间"""
        if not self.adaptive_cooldown_enabled:
            return self.proxy_cooldown

        # 根据最近的请求频率动态调整冷却时间
        current_time = time.time()
        recent_requests = [
            t for t in self.update_request_times
            if current_time - t <= 10.0  # 最近10秒内的请求
        ]

        if len(recent_requests) >= 3:
            # 如果最近10秒内有3次或更多请求，增加冷却时间
            return min(self.proxy_cooldown * 2, 5.0)
        elif len(recent_requests) >= 2:
            # 如果最近10秒内有2次请求，稍微增加冷却时间
            return self.proxy_cooldown * 1.5
        else:
            return self.proxy_cooldown

    def update_proxy_sync(self) -> bool:
        """同步更新代理IP"""
        try:
            self.logger.info("开始同步更新代理IP...")
            new_proxy, remaining_count = self._fetch_proxy_from_api()

            if new_proxy:
                # 验证代理有效性
                if self._validate_proxy(new_proxy):
                    old_proxy = self.current_proxy
                    self.current_proxy = new_proxy
                    self.remaining_count = remaining_count
                    self.last_fetch_time = time.time()
                    self.logger.info(f"同步更新代理IP成功: {old_proxy} -> {self.current_proxy}，剩余代理IP数量: {self.remaining_count}")
                    return True
                else:
                    self.logger.error(f"新代理 {new_proxy} 验证失败")
                    return False
            else:
                self.logger.error("未能从API获取到有效的代理IP")
                return False

        except Exception as e:
            self.logger.error(f"同步更新代理IP失败: {str(e)}")
            return False

    async def update_proxy_async(self, force: bool = False) -> bool:
        """智能的异步更新代理IP - 全局锁确保原子性"""
        # 尝试获取锁，如果锁已被占用，说明已有刷新任务在执行，直接返回True表示"刷新任务已在处理"
        if self._refresh_lock.locked():
            self.logger.debug("代理刷新任务已在进行中，跳过重复请求。")
            # 等待现有任务完成
            await self._refresh_lock.acquire()
            self._refresh_lock.release()
            return True

        async with self._refresh_lock:
            # 在锁内再次检查是否需要执行
            if not force and self._should_skip_proxy_update():
                self.logger.debug("忽略重复的代理更新请求。")
                return False

            self.logger.info("获取到代理刷新锁，开始执行更新...")
            self._record_proxy_update_request()

            try:
                # 获取新代理
                new_proxy, remaining_count = await self._fetch_proxy_async()
                if not new_proxy:
                    self.logger.error("未能从API获取到有效的代理IP。")
                    return False

                # 验证新代理
                if not await self._validate_proxy_async(new_proxy):
                    self.logger.error(f"新代理 {new_proxy} 验证失败。")
                    return False

                old_proxy = self.current_proxy
                self.current_proxy = new_proxy
                self.remaining_count = remaining_count
                self.last_fetch_time = time.time()
                self.logger.info(f"异步更新代理IP成功: {old_proxy} -> {self.current_proxy}，剩余代理IP数量: {self.remaining_count}")

                # 刷新连接池
                if self.http_client:
                    await self.http_client.force_refresh_connections()

                return True

            except Exception as e:
                self.logger.error(f"异步更新代理IP过程中发生异常: {e}")
                return False
            finally:
                self.logger.info("释放代理刷新锁。")
                # 锁将在 async with 块结束时自动释放

    async def _fetch_proxy_async(self) -> Tuple[Optional[str], int]:
        """异步获取代理IP"""
        try:
            loop = asyncio.get_running_loop()
            return await loop.run_in_executor(None, self._fetch_proxy_from_api)
        except Exception as e:
            self.logger.error(f"异步获取代理IP失败: {str(e)}")
            return None, 0

    async def force_refresh_proxy(self) -> bool:
        """
        报告当前代理出错，并强制刷新代理。
        此方法会绕过冷却时间，并在调用时立即将当前代理置为无效。
        """
        if self.current_proxy:
            self.logger.warning(f"当前代理 {self.current_proxy} 报告失败，强制刷新")
            self.current_proxy = None
        else:
            self.logger.warning("无可用代理，请求强制刷新")

        return await self.update_proxy_async(force=True)

    def should_auto_update(self) -> bool:
        """检查是否应该自动更新代理"""
        current_time = time.time()

        # 检查时间间隔
        if current_time - self.last_fetch_time > self.auto_update_interval:
            self.logger.debug(f"代理使用时间已超过 {self.auto_update_interval} 秒，需要更新")
            return True

        # 检查剩余代理数量
        if self.remaining_count < self.low_count_threshold:
            self.logger.debug(f"剩余代理数量 {self.remaining_count} 低于阈值 {self.low_count_threshold}，需要更新")
            return True

        return False

    def get_proxy(self) -> Optional[str]:
        """获取当前代理IP"""
        return self.current_proxy

    def get_proxy_dict(self) -> Optional[dict]:
        """获取代理字典格式，用于requests"""
        if not self.current_proxy:
            return None

        return {
            "http": f"http://{self.current_proxy}",
            "https": f"http://{self.current_proxy}"
        }

    def get_proxy_url(self) -> Optional[str]:
        """获取代理URL格式，用于aiohttp"""
        if not self.current_proxy:
            return None

        return f"http://{self.current_proxy}"

    def is_proxy_expired(self) -> bool:
        """检查代理是否已过期（基于时间和使用次数）"""
        if not self.current_proxy:
            return True

        # 检查时间过期
        current_time = time.time()
        time_since_fetch = current_time - self.last_fetch_time

        if time_since_fetch > self.proxy_expire_time:
            self.logger.debug(f"代理已超时过期，使用时间: {time_since_fetch:.1f}秒")
            return True

        # 检查剩余代理数量（如果为0可能表示当前代理已用完）
        if self.remaining_count == 0:
            self.logger.debug("代理已用完，需要更新")
            return True

        return False

    def get_remaining_count(self) -> int:
        """获取剩余代理数量"""
        return self.remaining_count

    def get_update_request_stats(self) -> dict:
        """获取代理更新请求统计信息"""
        current_time = time.time()

        # 清理过期的请求时间记录
        recent_requests = [
            t for t in self.update_request_times
            if current_time - t <= self.update_request_window
        ]

        return {
            "total_requests": self.update_request_count,
            "ignored_requests": self.ignored_request_count,
            "recent_requests_count": len(recent_requests),
            "request_window_seconds": self.update_request_window,
            "max_requests_in_window": self.max_requests_in_window,
            "last_update_attempt_time": self.last_update_attempt_time,
            "adaptive_cooldown_enabled": self.adaptive_cooldown_enabled,
            "current_effective_cooldown": self._get_adaptive_cooldown(),
            "ignore_ratio": self.ignored_request_count / max(1, self.update_request_count)
        }

    def get_proxy_status(self) -> dict:
        """获取代理状态信息 - 增强版本，包含详细性能监控"""
        current_time = time.time()

        # 基础状态信息
        status = {
            "current_proxy": self.current_proxy,
            "remaining_count": self.remaining_count,
            "last_fetch_time": self.last_fetch_time,
            "time_since_fetch": current_time - self.last_fetch_time if self.last_fetch_time > 0 else 0,
            "is_expired": self.is_proxy_expired(),
            "should_auto_update": self.should_auto_update(),
            "is_updating": self.proxy_updating,
            "validation_enabled": self.validation_enabled,
            "validation_stats": self.get_validation_stats()
        }

        # 添加性能监控信息
        status.update({
            "validation_timeout": self.validation_timeout,
            "proxy_cooldown": self.proxy_cooldown,
            "exception_refresh_cooldown": self.exception_refresh_cooldown,
            "last_exception_refresh_time": self.last_exception_refresh_time,
            "time_since_last_exception_refresh": current_time - self.last_exception_refresh_time if self.last_exception_refresh_time > 0 else 0,
            "auto_update_interval": self.auto_update_interval,
            "low_count_threshold": self.low_count_threshold,
            "proxy_expire_time": self.proxy_expire_time,
        })

        # 添加防重机制统计信息
        status["update_request_stats"] = self.get_update_request_stats()

        # 添加状态评估
        if self.current_proxy:
            proxy_age = current_time - self.last_fetch_time if self.last_fetch_time > 0 else 0
            status["proxy_health"] = {
                "age_seconds": proxy_age,
                "age_percentage": min(100, (proxy_age / self.proxy_expire_time) * 100) if self.proxy_expire_time > 0 else 0,
                "is_fresh": proxy_age < 60,  # 小于1分钟算新鲜
                "is_aging": 60 <= proxy_age < 180,  # 1-3分钟算老化
                "is_old": proxy_age >= 180,  # 超过3分钟算老旧
            }
        else:
            status["proxy_health"] = {"status": "no_proxy"}

        return status

    def update_config(self, config: dict):
        """更新配置"""
        old_api_url = self.api_url

        # 更新API URL
        if 'proxy_api_url' in config:
            self.api_url = config['proxy_api_url']

        # 更新其他配置
        if 'proxy_validation_enabled' in config:
            self.validation_enabled = config['proxy_validation_enabled']

        if 'proxy_validation_timeout' in config:
            self.validation_timeout = config['proxy_validation_timeout']

        if 'proxy_auto_update_interval' in config:
            self.auto_update_interval = config['proxy_auto_update_interval']

        if 'proxy_low_count_threshold' in config:
            self.low_count_threshold = config['proxy_low_count_threshold']

        if 'proxy_expire_time' in config:
            self.proxy_expire_time = config['proxy_expire_time']

        # 如果API URL发生变化，记录日志
        if old_api_url != self.api_url:
            self.logger.info(f"代理API URL已更新: {old_api_url} -> {self.api_url}")

        self.logger.debug("代理管理器配置已更新")

    def enable_validation(self, enable: bool = True):
        """启用或禁用代理验证"""
        self.validation_enabled = enable
        self.logger.info(f"代理验证已{'启用' if enable else '禁用'}")

    async def health_check(self) -> bool:
        """健康检查，测试当前代理是否可用"""
        if not self.current_proxy:
            self.logger.warning("没有可用的代理进行健康检查")
            return False

        try:
            # 使用当前代理进行健康检查
            is_healthy = await self._validate_proxy_async(self.current_proxy)
            if not is_healthy:
                self.logger.warning(f"代理健康检查失败: {self.current_proxy}")
                # 自动刷新代理
                await self.force_refresh_proxy()
            return is_healthy
        except Exception as e:
            self.logger.error(f"代理健康检查出错: {str(e)}")
            return False

    def __str__(self) -> str:
        """字符串表示"""
        return f"ProxyManager(current_proxy={self.current_proxy}, remaining={self.remaining_count})"

    def __repr__(self) -> str:
        """详细字符串表示"""
        return (f"ProxyManager(api_url='{self.api_url}', "
                f"current_proxy='{self.current_proxy}', "
                f"remaining_count={self.remaining_count}, "
                f"validation_enabled={self.validation_enabled})")

    async def force_refresh_proxy_on_exception(self, exception_type: str, exception_msg: str = "") -> bool:
        """
        基于异常类型的智能代理刷新。
        只有当异常类型是代理相关且距离上次异常刷新超过冷却时间时才会刷新。

        Args:
            exception_type: 异常类型名称
            exception_msg: 异常消息（可选，用于更详细的判断）

        Returns:
            bool: 是否成功刷新了代理
        """
        current_time = time.time()

        # 检查是否是代理相关异常
        if not self._is_proxy_related_exception(exception_type, exception_msg):
            self.logger.debug(f"异常 {exception_type} 不是代理相关异常，跳过代理刷新")
            return False

        # 检查冷却时间
        if current_time - self.last_exception_refresh_time < self.exception_refresh_cooldown:
            remaining_cooldown = self.exception_refresh_cooldown - (current_time - self.last_exception_refresh_time)
            self.logger.debug(f"异常触发的代理刷新仍在冷却中，剩余 {remaining_cooldown:.1f} 秒")
            return False

        # 记录本次异常刷新时间
        self.last_exception_refresh_time = current_time

        self.logger.warning(f"检测到代理相关异常 {exception_type}，立即刷新代理IP")

        # 强制刷新代理
        success = await self.update_proxy_async(force=True)

        if success:
            self.logger.info(f"因异常 {exception_type} 触发的代理刷新成功")
        else:
            self.logger.error(f"因异常 {exception_type} 触发的代理刷新失败")

        return success

    def _is_proxy_related_exception(self, exception_type: str, exception_msg: str = "") -> bool:
        """
        判断异常是否与代理相关（增强版本，专门优化407错误检测）

        Args:
            exception_type: 异常类型名称
            exception_msg: 异常消息

        Returns:
            bool: 是否是代理相关异常
        """
        # 检查异常类型
        if exception_type in self.proxy_related_exceptions:
            return True

        # 检查异常消息中的关键词（增强版本）
        if exception_msg:
            exception_msg_lower = exception_msg.lower()

            # 407错误 - 最高优先级检测，代理IP失效的明确信号
            if any(keyword in exception_msg_lower for keyword in [
                '407', 'proxy authentication required', '代理认证',
                'tunnel connection failed: 407', 'authentication required'
            ]):
                self.logger.debug(f"检测到407错误关键词，确认为代理IP失效: {exception_msg[:100]}")
                return True

            # 其他代理相关关键词
            proxy_keywords = [
                'proxy', '代理', 'connection refused', '连接被拒绝',
                'remote computer refused', '远程计算机拒绝',
                'connect to host', 'ssl:default', 'timeout',
                'connect call failed', '连接调用失败', 'cannot connect to host',
                'proxy connection', '代理连接', 'connection error',
                'session is closed', 'transport is closing', 'ssl transport',
                'bad file descriptor', 'connection lost', 'application data after close notify',
                # Windows网络错误关键词
                '[winerror 1225]', '[winerror 64]', 'winerror 1225', 'winerror 64',
                '指定的网络名不再可用', '远程计算机拒绝网络连接',
                'clientproxyconnectionerror', 'proxyconnectionerror',
                # 更多网络失败关键词
                'max retries exceeded', 'httpsconnectionpool', 'unable to connect to proxy',
                'proxy error', 'proxyerror', 'failed to establish connection',
                # NetworkException相关关键词
                'networkexception', 'network exception', '网络异常', '网络错误', '网络连接失败'
            ]

            for keyword in proxy_keywords:
                if keyword in exception_msg_lower:
                    return True

        return False

    def get_error_type_from_exception(self, exception_type: str, exception_msg: str = "") -> str:
        """
        从异常信息中识别具体的错误类型

        Args:
            exception_type: 异常类型名称
            exception_msg: 异常消息

        Returns:
            str: 错误类型标识
        """
        # 检查异常类型名称
        if exception_type == "NetworkException":
            return "network_exception"

        if not exception_msg:
            return "unknown"

        exception_msg_lower = exception_msg.lower()

        # 407错误 - 代理IP失效
        if any(keyword in exception_msg_lower for keyword in [
            '407', 'proxy authentication required', 'tunnel connection failed: 407'
        ]):
            return "407_auth_required"

        # NetworkException - 网络异常导致的代理失效
        if any(keyword in exception_msg_lower for keyword in [
            'networkexception', 'network exception', '网络异常', '网络错误'
        ]):
            return "network_exception"

        # 连接超时
        if any(keyword in exception_msg_lower for keyword in [
            'timeout', 'timed out', 'read timeout', 'connect timeout'
        ]):
            return "timeout"

        # 连接被拒绝
        if any(keyword in exception_msg_lower for keyword in [
            'connection refused', 'refused', 'remote computer refused'
        ]):
            return "connection_refused"

        # 连接错误
        if any(keyword in exception_msg_lower for keyword in [
            'connection error', 'connection failed', 'cannot connect'
        ]):
            return "connection_error"

        # 代理错误
        if any(keyword in exception_msg_lower for keyword in [
            'proxy error', 'proxyerror', 'proxy connection'
        ]):
            return "proxy_error"

        return "unknown"

    def get_proxy_refresh_status(self) -> dict:
        """获取代理刷新状态信息"""
        current_time = time.time()
        return {
            "last_exception_refresh_time": self.last_exception_refresh_time,
            "exception_refresh_cooldown": self.exception_refresh_cooldown,
            "time_since_last_exception_refresh": current_time - self.last_exception_refresh_time if self.last_exception_refresh_time > 0 else 0,
            "can_refresh_on_exception": current_time - self.last_exception_refresh_time >= self.exception_refresh_cooldown,
            "proxy_related_exceptions": list(self.proxy_related_exceptions)
        }

    def get_comprehensive_status(self) -> dict:
        """获取全面的代理管理器状态信息 - 用于调试和监控"""
        return {
            "proxy_status": self.get_proxy_status(),
            "refresh_status": self.get_proxy_refresh_status(),
            "validation_config": {
                "enabled": self.validation_enabled,
                "timeout": self.validation_timeout,
                "cache_ttl": self.validation_cache_ttl,
                "cache_max_size": self.validation_cache_max_size
            },
            "performance_config": {
                "cooldown": self.proxy_cooldown,
                "auto_update_interval": self.auto_update_interval,
                "low_count_threshold": self.low_count_threshold,
                "proxy_expire_time": self.proxy_expire_time,
                "exception_refresh_cooldown": self.exception_refresh_cooldown
            },
            "quality_score": self.get_proxy_quality_score()
        }

    async def smart_error_recovery(self) -> bool:
        """智能错误恢复机制"""
        try:
            self.logger.info("开始智能错误恢复...")

            # 获取当前状态
            status = self.get_proxy_status()
            validation_stats = status.get('validation_stats', {})

            # 分析错误情况
            success_rate = validation_stats.get('success_rate', 0.0)
            consecutive_failures = validation_stats.get('failed_validations', 0)

            recovery_actions = []

            # 根据错误情况决定恢复策略
            if success_rate < 0.3 and consecutive_failures > 5:
                # 严重错误：激进恢复
                recovery_actions.extend([
                    "force_proxy_refresh",
                    "clear_validation_cache",
                    "reduce_validation_timeout",
                    "aggressive_connection_refresh"
                ])
                self.logger.warning("检测到严重代理问题，启动激进恢复模式")

            elif success_rate < 0.6 and consecutive_failures > 3:
                # 中等错误：标准恢复
                recovery_actions.extend([
                    "force_proxy_refresh",
                    "clear_validation_cache",
                    "moderate_connection_refresh"
                ])
                self.logger.info("检测到中等代理问题，启动标准恢复模式")

            elif consecutive_failures > 2:
                # 轻微错误：温和恢复
                recovery_actions.extend([
                    "clear_validation_cache",
                    "basic_connection_refresh"
                ])
                self.logger.info("检测到轻微代理问题，启动温和恢复模式")

            # 执行恢复动作
            recovery_success = True
            for action in recovery_actions:
                try:
                    if action == "force_proxy_refresh":
                        success = await self.update_proxy_async(force=True)
                        if success:
                            self.logger.info("✅ 强制代理刷新成功")
                        else:
                            self.logger.warning("❌ 强制代理刷新失败")
                            recovery_success = False

                    elif action == "clear_validation_cache":
                        old_size = len(self.validation_cache)
                        self.validation_cache.clear()
                        self.logger.info(f"✅ 清空验证缓存，移除 {old_size} 个条目")

                    elif action == "reduce_validation_timeout":
                        old_timeout = self.validation_timeout
                        self.validation_timeout = max(3, self.validation_timeout - 1)
                        self.logger.info(f"✅ 降低验证超时：{old_timeout}s -> {self.validation_timeout}s")

                    elif action == "aggressive_connection_refresh":
                        if self.http_client:
                            for i in range(3):  # 多次刷新
                                await self.http_client.force_refresh_connections()
                                await asyncio.sleep(0.2)
                            self.logger.info("✅ 激进连接池刷新完成")

                    elif action == "moderate_connection_refresh":
                        if self.http_client:
                            for i in range(2):  # 中等刷新
                                await self.http_client.force_refresh_connections()
                                await asyncio.sleep(0.1)
                            self.logger.info("✅ 标准连接池刷新完成")

                    elif action == "basic_connection_refresh":
                        if self.http_client:
                            await self.http_client.force_refresh_connections()
                            self.logger.info("✅ 基本连接池刷新完成")

                except Exception as e:
                    self.logger.error(f"❌ 执行恢复动作 {action} 失败: {str(e)}")
                    recovery_success = False

            # 重置统计信息（部分）
            if recovery_success:
                # 只重置失败计数，保留总体统计
                self.validation_stats['failed_validations'] = 0
                self.logger.info("✅ 智能错误恢复完成，系统已重新优化")
                return True
            else:
                self.logger.error("❌ 智能错误恢复部分失败")
                return False

        except Exception as e:
            self.logger.error(f"智能错误恢复过程异常: {str(e)}")
            return False

    def get_proxy_quality_score(self) -> float:
        """计算当前代理质量评分 (0.0-1.0)"""
        try:
            stats = self.validation_stats
            if stats['total_validations'] == 0:
                return 0.5  # 无数据时返回中等分数

            # 基础成功率评分 (0-40分)
            success_rate = stats.get('success_rate', 0.0)
            success_score = success_rate * 40

            # 响应时间评分 (0-30分)
            avg_time = stats.get('average_validation_time', 10.0)
            if avg_time <= 2.0:
                time_score = 30
            elif avg_time <= 4.0:
                time_score = 20
            elif avg_time <= 6.0:
                time_score = 10
            else:
                time_score = 0

            # 稳定性评分 (0-30分)
            consecutive_failures = stats.get('failed_validations', 0)
            if consecutive_failures == 0:
                stability_score = 30
            elif consecutive_failures <= 2:
                stability_score = 20
            elif consecutive_failures <= 5:
                stability_score = 10
            else:
                stability_score = 0

            total_score = (success_score + time_score + stability_score) / 100.0
            return min(1.0, max(0.0, total_score))

        except Exception as e:
            self.logger.debug(f"计算代理质量评分异常: {str(e)}")
            return 0.5

    async def auto_optimize_if_degraded(self) -> bool:
        """如果代理质量下降，自动优化"""
        try:
            quality_score = self.get_proxy_quality_score()
            self.logger.debug(f"当前代理质量评分: {quality_score:.2f}")

            # 根据质量评分决定是否需要优化
            if quality_score < 0.3:
                self.logger.warning(f"代理质量评分过低 ({quality_score:.2f})，启动智能恢复")
                return await self.smart_error_recovery()
            elif quality_score < 0.6:
                self.logger.info(f"代理质量评分偏低 ({quality_score:.2f})，尝试基本优化")
                # 基本优化：清空缓存并刷新连接
                self.validation_cache.clear()
                if self.http_client:
                    await self.http_client.force_refresh_connections()
                return True
            else:
                self.logger.debug(f"代理质量良好 ({quality_score:.2f})，无需优化")
                return True

        except Exception as e:
            self.logger.error(f"自动优化检查异常: {str(e)}")
            return False

    async def _pre_check_proxy_connectivity(self, ip_port: str) -> bool:
        """代理连通性预检 - 快速检查基本连通性"""
        try:
            # 使用简单的TCP连接测试
            ip, port = ip_port.split(':')
            port = int(port)

            # 设置较短的超时时间进行快速检查
            loop = asyncio.get_running_loop()

            def tcp_check():
                try:
                    sock = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
                    sock.settimeout(2)  # 2秒超时
                    result = sock.connect_ex((ip, port))
                    sock.close()
                    return result == 0
                except Exception:
                    return False

            # 在线程池中执行TCP检查
            is_connectable = await loop.run_in_executor(None, tcp_check)

            if is_connectable:
                self.logger.debug(f"代理 {ip_port} TCP连通性检查通过")
                return True
            else:
                self.logger.debug(f"代理 {ip_port} TCP连通性检查失败")
                return False

        except Exception as e:
            self.logger.debug(f"代理 {ip_port} 连通性预检异常: {str(e)[:50]}")
            return False

    async def handle_407_error(self, failed_proxy: str) -> bool:
        """专门处理407代理IP失效错误，立即切换代理"""
        self.logger.warning(f"检测到407错误，代理IP {failed_proxy} 已失效，立即切换新代理")

        # 将当前代理标记为无效
        if self.current_proxy == failed_proxy:
            self.current_proxy = None

        # 强制更新代理，绕过冷却时间
        return await self.update_proxy_async(force=True)

    async def handle_connection_timeout(self, failed_proxy: str) -> bool:
        """专门处理连接超时，立即切换代理"""
        self.logger.warning(f"检测到连接超时，代理 {failed_proxy} 响应慢，立即切换新代理")

        # 将当前代理标记为无效
        if self.current_proxy == failed_proxy:
            self.current_proxy = None

        # 强制更新代理，绕过冷却时间
        return await self.update_proxy_async(force=True)

    async def handle_proxy_failure(self, failed_proxy: str, error_type: str = "unknown") -> bool:
        """
        统一处理代理失效的方法

        Args:
            failed_proxy: 失效的代理IP
            error_type: 错误类型 (407_auth_required, network_exception, timeout, connection_error, etc.)

        Returns:
            bool: 是否成功切换到新代理
        """
        # 407错误和NetworkException是最高优先级，立即处理，无冷却时间
        if error_type in ["407_auth_required", "network_exception"]:
            if error_type == "407_auth_required":
                self.logger.warning(f" 检测到407错误：代理IP {failed_proxy} 已失效，紧急切换新代理")
            elif error_type == "network_exception":
                self.logger.warning(f" 检测到NetworkException：代理IP {failed_proxy} 网络失效，紧急切换新代理")

            # 立即将当前代理标记为无效
            if self.current_proxy == failed_proxy:
                self.current_proxy = None

            # 绕过所有限制，立即强制更新
            self.last_exception_refresh_time = 0  # 重置冷却时间
            return await self.update_proxy_async(force=True)

        # 其他错误类型按原有逻辑处理
        elif error_type in ["timeout", "connection_error", "refused"]:
            self.logger.warning(f"检测到{error_type}错误，代理 {failed_proxy} 可能失效，尝试切换新代理")

            if self.current_proxy == failed_proxy:
                self.current_proxy = None

            return await self.update_proxy_async(force=True)

        else:
            self.logger.info(f"检测到{error_type}错误，代理 {failed_proxy} 可能存在问题，考虑切换")
            return await self.force_refresh_proxy_on_exception(error_type)